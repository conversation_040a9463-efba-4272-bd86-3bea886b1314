"""
应用常量定义
"""

# 应用信息
APP_NAME = "木偶AI翻唱"
APP_VERSION = "2.0.0"
WINDOW_WIDTH = 1250
WINDOW_HEIGHT = 1000

# 深色主题配色方案
class Colors:
    # 主要背景色
    BACKGROUND = "#111827"          # 深灰蓝
    SECONDARY_BG = "#1C2128"        # 次级背景
    CARD_BG = "#1F2937"             # 卡片背景色
    INPUT_BG = "#374151"            # 输入框背景色
    
    # 文本颜色
    FOREGROUND = "#E5E7EB"          # 主要文本色（浅灰白）
    SECONDARY_TEXT = "#9CA3AF"      # 次要文本色（中灰）
    
    # 强调色
    ACCENT = "#3B82F6"              # 蓝色强调色
    ACCENT_HOVER = "#2563EB"        # 悬停时的强调色
    ACCENT_PRESSED = "#1D4ED8"      # 按下时的强调色
    
    # 边框和分割线
    BORDER = "#30363D"              # 边框颜色
    
    # 状态颜色
    SUCCESS = "#10B981"             # 成功颜色（绿色）
    WARNING = "#F59E0B"             # 警告颜色（橙色）
    ERROR = "#EF4444"               # 错误颜色（红色）
    
    # 波形图
    WAVEFORM_BG = "#282E33"         # 波形图背景色

# 组件样式规范
class Styles:
    # 圆角
    CARD_RADIUS = "12px"
    INPUT_RADIUS = "8px"
    BUTTON_RADIUS = "8px"
    
    # 字体
    FONT_FAMILY = "Microsoft YaHei UI"
    FONT_SIZE_SMALL = 10
    FONT_SIZE_NORMAL = 11
    FONT_SIZE_LARGE = 12
    FONT_SIZE_TITLE = 14
    FONT_SIZE_HEADER = 16
    
    # 间距
    PADDING_SMALL = 8
    PADDING_NORMAL = 12
    PADDING_LARGE = 16
    MARGIN_SMALL = 4
    MARGIN_NORMAL = 8
    MARGIN_LARGE = 12

# 布局配置
class Layout:
    # 左右分栏比例 (参考HTML版本的1:2比例)
    LEFT_PANEL_RATIO = 1
    RIGHT_PANEL_RATIO = 2

    # 手风琴配置
    ACCORDION_ANIMATION_DURATION = 300  # ms

    # 控制台高度
    CONSOLE_HEIGHT = 200

# 文件路径
class Paths:
    MODELS_DIR = "models"
    TEMP_DIR = "temp"
    CACHE_DIR = "cache"
    LOGS_DIR = "logs"
    ASSETS_DIR = "assets"
    FFMPEG_PATH = "ffmpeg/bin/ffmpeg.exe"

# API配置
class API:
    DEFAULT_LOCAL_PORT = 8000
    DEFAULT_TIMEOUT = 30
    WEBSOCKET_RECONNECT_INTERVAL = 5
    MAX_UPLOAD_SIZE = 100 * 1024 * 1024  # 100MB
