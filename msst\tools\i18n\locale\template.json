{"仅供个人娱乐和非商业用途, 禁止用于血腥/暴力/性相关/政治相关内容。[点击前往教程文档](https://r1kc63iz15l.feishu.cn/wiki/JSp3wk7zuinvIXkIqSUcCXY1nKc)<br>本整合包完全免费, 严禁以任何形式倒卖, 如果你从任何地方**付费**购买了本整合包, 请**立即退款**。<br> 整合包作者: [bilibili@阿狸不吃隼舞](https://space.bilibili.com/403335715) [Github@KitsuneX07](https://github.com/KitsuneX07) | [Bilibili@Sucial](https://space.bilibili.com/445022409) [Github@SUC-DriverOld](https://github.com/SUC-DriverOld) | Gradio主题: [Gradio Theme](https://huggingface.co/spaces/NoCrypt/miku)": "text", "MSST分离": "text", "UVR分离": "text", "预设流程": "text", "合奏模式": "text", "小工具": "text", "安装模型": "text", "MSST训练": "text", "设置": "text", "输出音轨": "text", "请至少添加2个模型到合奏流程": "text", "合奏流程已保存": "text", "请上传至少一个音频文件!": "text", "请先创建合奏流程": "text", "模型": "text", "不存在": "text", "用户强制终止": "text", "处理失败: ": "text", "处理完成, 成功: ": "text", "个文件, 失败: ": "text", "个文件": "text", ", 结果已保存至: ": "text", ", 耗时: ": "text", "请上传至少2个文件": "text", "上传的文件数目与权重数目不匹配": "text", "处理完成, 文件已保存为: ": "text", "处理失败!": "text", "input文件夹内文件列表:\\n": "text", "input文件夹为空\\n": "text", "results文件夹内文件列表:\\n": "text", "results文件夹为空\\n": "text", "已删除input文件夹内所有文件": "text", "已删除results文件夹内所有文件": "text", "请先选择模型": "text", "仅输出主音轨": "text", "仅输出次音轨": "text", "已打开下载管理器": "text", "选择模型": "text", "模型名字": "text", "模型类型": "text", "主要提取音轨": "text", "次要提取音轨": "text", "下载链接": "text", "模型类别": "text", "可提取音轨": "text", "模型大小": "text", "模型已安装": "text", "无法校验sha256": "text", "sha256校验失败": "text", "模型sha256校验失败, 请重新下载": "text", "sha256校验成功": "text", "模型未安装": "text", "请选择模型类型": "text", "已安装": "text", "请手动删除后重新下载": "text", "下载成功": "text", "下载失败": "text", "已安装。请勿重复安装。": "text", "已打开": "text", "的下载链接": "text", "上传参数文件": "text", "上传参数": "text", "请上传'.yaml'格式的配置文件": "text", "请上传'ckpt', 'chpt', 'th'格式的模型文件": "text", "请输入正确的模型类别和模型类型": "text", "安装成功。重启WebUI以刷新模型列表": "text", "安装失败": "text", "请输入选择模型参数": "text", "请上传'.json'格式的参数文件": "text", "请上传'.pth'格式的模型文件": "text", "请输入正确的音轨名称": "text", "配置保存成功!": "text", "非官方模型不支持重置配置!": "text", "配置重置成功!": "text", "备份文件不存在!": "text", "选择输出音轨": "text", "请选择模型": "text", "请选择输入目录": "text", "请选择输出目录": "text", "请选择GPU": "text", "处理完成, 结果已保存至: ": "text", "暂无备份文件": "text", "作为下一模型输入(或结果输出)的音轨": "text", "直接保存至输出目录的音轨(可多选)": "text", "不输出": "text", "请填写预设名称": "text", "预设": "text", "保存成功": "text", "请选择预设": "text", "不支持的预设版本: ": "text", ", 请重新制作预设。": "text", "预设版本不支持": "text", "预设不存在": "text", "删除成功": "text", "预设已删除": "text", "选择需要恢复的预设流程备份": "text", "已成功恢复备份": "text", "设置重置成功, 请重启WebUI刷新! ": "text", "记录重置成功, 请重启WebUI刷新! ": "text", "请选择正确的模型目录": "text", "设置保存成功! 请重启WebUI以应用。": "text", "当前版本: ": "text", ", 发现新版本: ": "text", ", 已是最新版本": "text", "检查更新失败": "text", "语言已更改, 重启WebUI生效": "text", "成功将端口设置为": "text", ", 重启WebUI生效": "text", "huggingface.co (需要魔法)": "text", "hf-mirror.com (镜像站可直连)": "text", "下载链接已更改": "text", "公共链接已开启, 重启WebUI生效": "text", "公共链接已关闭, 重启WebUI生效": "text", "已开启局域网分享, 重启WebUI生效": "text", "已关闭局域网分享, 重启WebUI生效": "text", "已开启自动清理缓存": "text", "已关闭自动清理缓存": "text", "已开启调试模式": "text", "已关闭调试模式": "text", "主题已更改, 重启WebUI生效": "text", "音频设置已保存": "text", "已重命名为": "text", "选择模型类型": "text", "请先选择模型类型": "text", "新模型名称后缀错误!": "text", "模型名字已存在! 请重新命名!": "text", "重命名失败!": "text", "检测到": "text", "旧版配置, 正在更新至最新版": "text", "成功清理Gradio缓存": "text", "请上传至少一个文件": "text", "单声道": "text", "处理完成, 成功转换: ": "text", "请先下载SOME预处理模型并放置在tools/SOME_weights文件夹下! ": "text", "请先选择模型保存路径! ": "text", "初始模型": "text", "模型类型错误, 请重新选择": "text", "配置文件不存在, 请重新选择": "text", "数据集路径不存在, 请重新选择": "text", "验证集路径不存在, 请重新选择": "text", "数据集类型错误, 请重新选择": "text", "训练启动成功! 请前往控制台查看训练信息! ": "text", "模型不存在, 请重新选择": "text", "验证完成! 请打开输出文件夹查看详细结果": "text", "错误: 无法找到增强配置文件模板, 请检查文件configs/augmentations_template.yaml是否存在。": "text", "已开启调试日志": "text", "已关闭调试日志": "text", "模型不存在!": "text", "输入音频分离": "text", "输入文件夹分离": "text", "请先选择文件夹!": "text", "显存不足, 请尝试减小batchsize值和chunksize值后重试。": "text", "内存不足，请尝试增大虚拟内存后重试。若分离时出现此报错，也可尝试将推理音频裁切短一些，分段分离。": "text", "FFmpeg未找到，请检查FFmpeg是否正确安装。若使用的是整合包，请重新安装。": "text", "模型损坏，请重新下载并安装模型后重试。": "text", "文件或路径不存在，请根据错误指示检查是否存在该文件。": "text", "合奏模式可用于集成不同算法的结果, 具体的文档位于/docs/ensemble.md。目前主要有以下两种合奏方式:<br>1. 从原始音频合奏: 直接上传一个或多个音频文件, 然后选择多个模型进行处理, 将这些处理结果根据选择的合奏模式进行合奏<br>2. 从分离结果合奏: 上传多个已经分离完成的结果音频, 然后选择合奏模式进行合奏": "text", "从原始音频合奏": "text", "从原始音频合奏需要上传至少一个音频文件, 然后选择多个模型先进行分离处理, 然后将这些处理结果根据选择的合奏模式进行合奏。<br>注意, 请确保你的磁盘空间充足, 合奏过程会产生的临时文件仅会在处理结束后删除。": "text", "制作合奏流程": "text", "权重": "text", "添加到合奏流程": "text", "撤销上一步": "text", "全部清空": "text", "合奏流程": "text", "保存此合奏流程": "text", "集成模式": "text", "输出格式": "text", "使用CPU (注意: 使用CPU会导致速度非常慢) ": "text", "使用TTA (测试时增强), 可能会提高质量, 但时间x3": "text", "输出次级音轨 (例如: 合奏人声时, 同时输出伴奏)": "text", "输入音频": "text", "上传一个或多个音频文件": "text", "输入文件夹": "text", "输入目录": "text", "选择文件夹": "text", "打开文件夹": "text", "输出目录": "text", "从分离结果合奏": "text", "从分离结果合奏需要上传至少两个音频文件, 这些音频文件是使用不同的模型分离同一段音频的结果。因此, 上传的所有音频长度应该相同。": "text", "上传多个音频文件": "text", "权重(以空格分隔, 数量要与上传的音频一致)": "text", "运行": "text", "强制停止": "text", "### 集成模式": "text", "1. `avg_wave`: 在1D变体上进行集成, 独立地找到波形的每个样本的平均值<br>2. `median_wave`: 在1D变体上进行集成, 独立地找到波形的每个样本的中位数<br>3. `min_wave`: 在1D变体上进行集成, 独立地找到波形的每个样本的最小绝对值<br>4. `max_wave`: 在1D变体上进行集成, 独立地找到波形的每个样本的最大绝对值<br>5. `avg_fft`: 在频谱图 (短时傅里叶变换 (STFT) 2D变体) 上进行集成, 独立地找到频谱图的每个像素的平均值。平均后使用逆STFT得到原始的1D波形<br>6. `median_fft`: 与avg_fft相同, 但使用中位数代替平均值 (仅在集成3个或更多来源时有用) <br>7. `min_fft`: 与avg_fft相同, 但使用最小函数代替平均值 (减少激进程度) <br>8. `max_fft`: 与avg_fft相同, 但使用最大函数代替平均值 (增加激进程度) ": "text", "### 注意事项": "text", "1. min_fft可用于进行更保守的合成, 它将减少更激进模型的影响。<br>2. 最好合成等质量的模型。在这种情况下, 它将带来增益。如果其中一个模型质量不好, 它将降低整体质量。<br>3. 在原仓库作者的实验中, 与其他方法相比, avg_wave在SDR分数上总是更好或相等。<br>4. 最终会在输出目录下生成一个`ensemble_<集成模式>.wav`。": "text", "下载官方模型": "text", "点击打开下载管理器": "text", "模型信息": "text", "打开模型目录": "text", "自动下载": "text", "手动下载": "text", "1. MSST模型默认下载在pretrain/<模型类型>文件夹下。UVR模型默认下载在设置中的UVR模型目录中。<br>2. 下加载进度可以打开终端查看。如果一直卡着不动或者速度很慢, 在确信网络正常的情况下请尝试重启WebUI。<br>3. 若下载失败, 会在模型目录**留下一个损坏的模型**, 请**务必**打开模型目录手动删除! <br>4. 点击“重启WebUI”按钮后, 会短暂性的失去连接, 随后会自动开启一个新网页。": "text", "### 模型下载链接": "text", "1. 自动从Github, Huggingface或镜像站下载模型。<br>2. 你也可以在此整合包下载链接中的All_Models文件夹中找到所有可用的模型并下载。": "text", "若自动下载出现报错或下载过慢, 请点击手动下载, 跳转至下载链接。手动下载完成后, 请根据你选择的模型类型放置到对应文件夹内。": "text", "### 当前UVR模型目录: ": "text", ", 如需更改, 请前往设置页面。": "text", "### 模型安装完成后, 需重启WebUI刷新模型列表": "text", "重启WebUI": "text", "安装非官方MSST模型": "text", "你可以从其他途径获取非官方MSST模型, 在此页面完成配置文件设置后, 即可正常使用。<br>注意: 仅支持'.ckpt', '.th', '.chpt'格式的模型。模型显示名字为模型文件名。<br>选择模型类型: 共有三个可选项。依次代表人声相关模型, 多音轨分离模型, 单音轨分离模型。仅用于区分模型大致类型, 可任意选择。<br>选择模型类别: 此选项关系到模型是否能正常推理使用, 必须准确选择!": "text", "上传非官方MSST模型": "text", "上传非官方MSST模型配置文件": "text", "选择模型类别": "text", "模型下载链接 (非必须，若无，可跳过)": "text", "安装非官方VR模型": "text", "你可以从其他途径获取非官方UVR模型, 在此页面完成配置文件设置后, 即可正常使用。<br>注意: 仅支持'.pth'格式的模型。模型显示名字为模型文件名。": "text", "上传非官方VR模型": "text", "主要音轨名称": "text", "次要音轨名称": "text", "选择模型参数": "text", "是否为Karaoke模型": "text", "是否为BV模型": "text", "是否为VR 5.1模型": "text", "MSST音频分离原项目地址: [https://github.com/ZFTurbo/Music-Source-Separation-Training](https://github.com/ZFTurbo/Music-Source-Separation-Training)": "text", "选择使用的GPU": "text", "强制使用CPU推理, 注意: 使用CPU推理速度非常慢!": "text", "使用CPU": "text", "[点击展开] 推理参数设置, 不同模型之间参数相互独立": "text", "只有在点击保存后才会生效。参数直接写入配置文件, 无法撤销。假如不知道如何设置, 请保持默认值。<br>请牢记自己修改前的参数数值, 防止出现问题以后无法恢复。请确保输入正确的参数, 否则可能会导致模型无法正常运行。<br>假如修改后无法恢复, 请点击``重置``按钮, 这会使得配置文件恢复到默认值。": "text", "批次大小, 减小此值可以降低显存占用, 此参数对推理效果影响不大": "text", "重叠数, 增大此值可以提高分离效果, 但会增加处理时间, 建议设置成4": "text", "分块大小, 增大此值可以提高分离效果, 但会增加处理时间和显存占用": "text", "音频归一化, 对音频进行归一化输入和输出, 部分模型没有此功能": "text", "启用TTA, 能小幅提高分离质量, 若使用, 推理时间x3": "text", "保存配置": "text", "重置配置": "text", "预设流程允许按照预设的顺序运行多个模型。每一个模型的输出将作为下一个模型的输入。": "text", "使用预设": "text", "该模式下的UVR推理参数将直接沿用UVR分离页面的推理参数, 如需修改请前往UVR分离页面。<br>修改完成后, 还需要任意处理一首歌才能保存参数! ": "text", "将次级输出保存至输出目录的单独文件夹内": "text", "制作预设": "text", "预设名称": "text", "请输入预设名称": "text", "添加至流程": "text", "保存上述预设流程": "text", "管理预设": "text", "此页面提供查看预设, 删除预设, 备份预设, 恢复预设等功能<br>`model_type`: 模型类型；`model_name`: 模型名称；`input_to_next`: 作为下一模型输入的音轨；`output_to_storage`: 直接保存至输出目录下的direct_output文件夹内的音轨, **不会经过后续流程处理**<br>每次点击删除预设按钮时, 将自动备份预设以免误操作。": "text", "删除所选预设": "text", "请先选择预设": "text", "恢复所选预设": "text", "打开备份文件夹": "text", "WebUI设置": "text", "GPU信息": "text", "系统信息": "text", "设置WebUI端口, 0为自动": "text", "选择语言": "text", "选择MSST模型下载链接": "text", "选择WebUI主题": "text", "对本地局域网开放WebUI: 开启后, 同一局域网内的设备可通过'本机IP:端口'的方式访问WebUI。": "text", "开启公共链接: 开启后, 他人可通过公共链接访问WebUI。链接有效时长为72小时。": "text", "自动清理缓存: 开启后, 每次启动WebUI时会自动清理缓存。": "text", "全局调试模式: 向开发者反馈问题时请开启。(该选项支持热切换)": "text", "选择UVR模型目录": "text", "检查更新": "text", ", 请点击检查更新按钮": "text", "前往Github瞅一眼": "text", "重置WebUI路径记录": "text", "重置WebUI设置": "text", "### 选择UVR模型目录": "text", "如果你的电脑中有安装UVR5, 你不必重新下载一遍UVR5模型, 只需在下方“选择UVR模型目录”中选择你的UVR5模型目录, 定位到models/VR_Models文件夹。<br>例如: E:/Program Files/Ultimate Vocal Remover/models/VR_Models 点击保存设置或重置设置后, 需要重启WebUI以更新。": "text", "### 检查更新": "text", "从Github检查更新, 需要一定的网络要求。点击检查更新按钮后, 会自动检查是否有最新版本。你可以前往此整合包的下载链接或访问Github仓库下载最新版本。": "text", "### 重置WebUI路径记录": "text", "将所有输入输出目录重置为默认路径, 预设/模型/配置文件以及上面的设置等**不会重置**, 无需担心。重置WebUI设置后, 需要重启WebUI。": "text", "### 重置WebUI设置": "text", "仅重置WebUI设置, 例如UVR模型路径, WebUI端口等。重置WebUI设置后, 需要重启WebUI。": "text", "### 重启WebUI": "text", "点击 “重启WebUI” 按钮后, 会短暂性的失去连接, 随后会自动开启一个新网页。": "text", "音频输出设置": "text", "此页面支持用户自定义修改MSST/VR推理后输出音频的质量。输出音频的**采样率, 声道数与模型支持的参数有关, 无法更改**。<br>修改完成后点击保存设置即可生效。": "text", "输出wav位深度": "text", "输出flac位深度": "text", "输出mp3比特率(bps)": "text", "保存设置": "text", "模型改名": "text", "此页面支持用户自定义修改模型名字, 以便记忆和使用。修改完成后, 需要重启WebUI以刷新模型列表。<br>【注意】此操作不可逆 (无法恢复至默认命名), 请谨慎命名。输入新模型名字时, 需保留后缀!": "text", "新模型名": "text", "请输入新模型名字, 需保留后缀!": "text", "确认修改": "text", "立体声": "text", "音频格式转换": "text", "上传一个或多个音频文件并将其转换为指定格式。<br>支持的格式包括 .mp3, .flac, .wav, .ogg, .m4a, .wma, .aac...等等。<br>**不支持**网易云音乐/QQ音乐等加密格式, 如.ncm, .qmc等。": "text", "选择或输入音频输出格式": "text", "选择音频输出目录": "text", "输出音频采样率(Hz)": "text", "输出音频声道数": "text", "输出ogg比特率(bps)": "text", "转换音频": "text", "合并音频": "text", "点击合并音频按钮后, 将自动把输入文件夹中的所有音频文件合并为一整个音频文件<br>合并后的音频会保存至输出目录中, 文件名为merged_audio_<文件夹名字>.wav": "text", "计算SDR": "text", "上传两个**wav音频文件**并计算它们的[SDR](https://www.aicrowd.com/challenges/music-demixing-challenge-ismir-2021#evaluation-metric)。<br>SDR是一个用于评估模型质量的数值。数值越大, 模型算法结果越好。": "text", "参考音频": "text", "待估音频": "text", "歌声转MIDI": "text", "歌声转MIDI功能使用开源项目[SOME](https://github.com/openvpi/SOME/), 可以将分离得到的**干净的歌声**转换成.mid文件。<br>【必须】若想要使用此功能, 请先下载权重文件[model_steps_64000_simplified.ckpt](https://hf-mirror.com/Sucial/MSST-WebUI/resolve/main/SOME_weights/model_steps_64000_simplified.ckpt)并将其放置在程序目录下的`tools/SOME_weights`文件夹内。文件命名不可随意更改!": "text", "如果不知道如何测量歌曲BPM, 可以尝试这两个在线测量工具: [bpmdetector](https://bpmdetector.kniffen.dev/) | [key-bpm-finder](https://vocalremover.org/zh/key-bpm-finder), 测量时建议上传原曲或伴奏, 若干声可能导致测量结果不准确。": "text", "上传音频": "text", "输入音频BPM": "text", "开始转换": "text", "1. 音频BPM (每分钟节拍数) 可以通过MixMeister BPM Analyzer等软件测量获取。<br>2. 为保证MIDI提取质量, 音频文件请采用干净清晰无混响底噪人声。<br>3. 输出MIDI不带歌词信息, 需要用户自行添加歌词。<br>4. 实际使用体验中部分音符会出现断开的现象, 需自行修正。SOME的模型主要面向DiffSinger唱法模型自动标注, 比正常用户在创作中需要的MIDI更加精细, 因而可能导致模型倾向于对音符进行切分。<br>5. 提取的MIDI没有量化/没有对齐节拍/不适配BPM, 需自行到各编辑器中手动调整。": "text", "此页面提供数据集制作教程, 训练参数选择, 以及一键训练。有关配置文件的修改和数据集文件夹的详细说明请参考MSST原项目: [https://github.com/ZFTurbo/Music-Source-Separation-Training](https://github.com/ZFTurbo/Music-Source-Separation-Training)<br>在开始下方的模型训练之前, 请先进行训练数据的制作。<br>说明: 数据集类型即训练集制作Step 1中你选择的类型, 1: Type1; 2: Type2; 3: Type3; 4: Type4, 必须与你的数据集类型相匹配。": "text", "训练": "text", "选择训练模型类型": "text", "配置文件路径": "text", "请输入配置文件路径或选择配置文件": "text", "选择配置文件": "text", "数据集类型": "text", "数据集路径": "text", "请输入或选择数据集文件夹": "text", "选择数据集文件夹": "text", "验证集路径": "text", "请输入或选择验证集文件夹": "text", "选择验证集文件夹": "text", "模型保存路径": "text", "请输入或选择模型保存文件夹": "text", "选择初始模型, 若无初始模型, 留空或选择None即可": "text", "刷新初始模型列表": "text", "训练参数设置": "text", "num_workers: 数据集读取线程数, 0为自动": "text", "随机数种子, 0为随机": "text", "是否将加载的数据放置在固定内存中, 默认为否": "text", "是否使用加速训练, 对于多显卡用户会加快训练": "text", "是否在训练前验证模型, 默认为否": "text", "是否使用MultiSTFT Loss, 默认为否": "text", "是否使用MSE loss, 默认为否": "text", "是否使用L1 loss, 默认为否": "text", "选择输出的评估指标": "text", "选择调度器使用的评估指标": "text", "保存上述训练配置": "text", "开始训练": "text", "点击开始训练后, 请到终端查看训练进度或报错, 下方不会输出报错信息, 想要停止训练可以直接关闭终端。在训练过程中, 你也可以关闭网页, 仅**保留终端**。": "text", "验证": "text", "此页面用于手动验证模型效果, 测试验证集, 输出SDR测试信息。输出的信息会存放在输出文件夹的results.txt中。<br>下方参数将自动加载训练页面的参数, 在训练页面点击保存训练参数后, 重启WebUI即可自动加载。当然你也可以手动输入参数。<br>": "text", "模型路径": "text", "请输入或选择模型文件": "text", "选择模型文件": "text", "验证参数设置": "text", "选择验证集音频格式": "text", "验证集读取线程数, 0为自动": "text", "开始验证": "text", "训练集制作指南": "text", "Step 1: 数据集制作": "text", "请**任选下面四种类型之一**制作数据集文件夹, 并按照给出的目录层级放置你的训练数据。完成后, 记录你的数据集**文件夹路径**以及你选择的**数据集类型**, 以便后续使用。": "text", "不同的文件夹。每个文件夹包含所需的所有stems, 格式为stem_name.wav。与MUSDBHQ18数据集相同。在最新的代码版本中, 可以使用flac替代wav。<br>例如: ": "text", "每个文件夹是stem_name。文件夹中包含仅由所需stem组成的wav文件。<br>例如: ": "text", "可以提供以下结构的CSV文件 (或CSV文件列表) <br>例如: ": "text", "与类型1相同, 但在训练过程中所有乐器都将来自歌曲的相同位置。<br>例如: ": "text", "Step 2: 验证集制作": "text", "验证集制作。验证数据集**必须**与上面数据集制作的Type 1(MUSDB)数据集**结构相同** (**无论你使用哪种类型的数据集进行训练**) , 此外每个文件夹还必须包含每首歌的mixture.wav, mixture.wav是所有stem的总和<br>例如: ": "text", "Step 3: 选择并修改修改配置文件": "text", "请先明确你想要训练的模型类型, 然后选择对应的配置文件进行修改。<br>目前有以下几种模型类型: ": "text", "<br>确定好模型类型后, 你可以前往整合包根目录中的configs_backup文件夹下找到对应的配置文件模板。复制一份模板, 然后根据你的需求进行修改。修改完成后记下你的配置文件路径, 以便后续使用。<br>特别说明: config_musdb18_xxx.yaml是针对MUSDB18数据集的配置文件。<br>": "text", "打开配置文件模板文件夹": "text", "你可以使用下表根据你的GPU选择用于训练的BS_Roformer模型的batch_size参数。表中提供的批量大小值适用于单个GPU。如果你有多个GPU, 则需要将该值乘以GPU的数量。": "text", "Step 4: 数据增强": "text", "数据增强可以动态更改stem, 通过从旧样本创建新样本来增加数据集的大小。现在, 数据增强的控制在配置文件中进行。下面是一个包含所有可用数据增强的完整配置示例。你可以将其复制到你的配置文件中以使用数据增强。<br>注意:<br>1. 要完全禁用所有数据增强, 可以从配置文件中删除augmentations部分或将enable设置为false。<br>2. 如果要禁用某些数据增强, 只需将其设置为0。<br>3. all部分中的数据增强应用于所有stem。<br>4. vocals/bass等部分中的数据增强仅应用于相应的stem。你可以为training.instruments中给出的所有stem创建这样的部分。": "text", "说明: 本整合包仅融合了UVR的VR Architecture模型, MDX23C和HtDemucs类模型可以直接使用前面的MSST音频分离。<br>UVR分离使用项目: [https://github.com/nomadkaraoke/python-audio-separator](https://github.com/nomadkaraoke/python-audio-separator) 并进行了优化。": "text", "Window Size: 窗口大小, 用于平衡速度和质量, 默认为512": "text", "Aggression: 主干提取强度, 范围-100-100, 人声请选5": "text", "[点击展开] 以下是一些高级设置, 一般保持默认即可": "text", "批次大小, 减小此值可以降低显存占用": "text", "后处理特征阈值, 取值为0.1-0.3, 默认0.2": "text", "次级输出使用频谱而非波形进行反转, 可能会提高质量, 但速度稍慢": "text", "启用“测试时增强”, 可能会提高质量, 但速度稍慢": "text", "将输出音频缺失的频率范围镜像输出, 作用不大": "text", "识别人声输出中残留的人工痕迹, 可改善某些歌曲的分离效果": "text", "正在启动WebUI, 请稍等...": "text", "若启动失败, 请尝试以管理员身份运行此程序": "text", "WebUI运行过程中请勿关闭此窗口!": "text", "检测到CUDA, 设备信息: ": "text", "使用MPS": "text", "检测到MPS, 使用MPS": "text", "无可用的加速设备, 使用CPU": "text", "\\033[33m未检测到可用的加速设备, 使用CPU\\033[0m": "text", "\\033[33m如果你使用的是NVIDIA显卡, 请更新显卡驱动至最新版后重试\\033[0m": "text", "模型下载失败, 请重试!": "text", "作者: [Github@KitsuneX07](https://github.com/KitsuneX07) | [Github@SUC-DriverOld](https://github.com/SUC-DriverOld), [点击前往教程文档](https://r1kc63iz15l.feishu.cn/wiki/JSp3wk7zuinvIXkIqSUcCXY1nKc)": "text", "**请将需要处理的音频放置到input文件夹内, 处理完成后的音频将会保存到results文件夹内! 云端输入输出目录不可更改!**": "text", "文件管理": "text", "文件管理页面是云端WebUI特有的页面, 用于上传, 下载, 删除文件。<br>1. 上传文件: 将文件上传到input文件夹内。可以勾选是否自动解压zip文件<br>2. 下载文件: 以zip格式打包results文件夹内的文件, 输出至WebUI以供下载。注意: 打包不会删除results文件夹, 若打包后不再需要分离结果, 请点击按钮手动删除。<br>3. 删除文件: 删除input和results文件夹内的文件。": "text", "删除input文件夹内所有文件": "text", "删除results文件夹内所有文件": "text", "刷新input和results文件列表": "text", "打包results文件夹内所有文件": "text", "上传一个或多个文件至input文件夹": "text", "自动解压zip文件(仅支持zip, 压缩包内文件名若含有非ASCII字符, 解压后文件名可能为乱码)": "text", "上传文件": "text", "input和results文件列表": "text", "请先点击刷新按钮": "text", "下载results文件夹内所有文件": "text", "Window Size: 窗口大小, 用于平衡速度和质量": "text", "初始模型: 继续训练或微调模型训练时, 请选择初始模型, 否则将从头开始训练! ": "text", "点击合并音频按钮后, 将自动把输入文件夹中的所有音频文件合并为一整个音频文件<br>目前支持的格式包括 .mp3, .flac, .wav, .ogg, m4a 这五种<br>合并后的音频会保存至输出目录中, 文件名为merged_audio_<文件夹名字>.wav": "text"}