"""
木偶AI翻唱应用 - FastAPI服务端
单文件API服务，提供音频分离、音色转换、模型管理等功能
"""

import os
import sys
import uuid
import asyncio
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime
import json
import hashlib

# FastAPI相关导入
from fastapi import FastAPI, File, UploadFile, HTTPException, BackgroundTasks, WebSocket, WebSocketDisconnect
from fastapi.responses import FileResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
import uvicorn

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/api_server.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

# 导入音频处理器
try:
    from src.core.audio_processor import AudioProcessor
except ImportError:
    AudioProcessor = None
    logger.warning("音频处理器导入失败，将使用模拟模式")

# 创建FastAPI应用
app = FastAPI(
    title="木偶AI翻唱API",
    description="提供音频分离、音色转换、模型管理等功能的API服务",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量
tasks_storage: Dict[str, Dict] = {}  # 任务存储
websocket_connections: Dict[str, WebSocket] = {}  # WebSocket连接管理
models_cache: Dict[str, Any] = {}  # 模型缓存
audio_processor = None  # 音频处理器

# 数据模型定义
class TaskStatus(BaseModel):
    task_id: str
    status: str  # pending, running, completed, error, cancelled
    progress: float  # 0.0 - 1.0
    message: str
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    result: Optional[Dict] = None
    error: Optional[str] = None

class SeparationRequest(BaseModel):
    mode: str = "complete"  # complete, fast
    output_format: str = "wav"  # wav, mp3
    model_name: str = "bandit_v2"

class ConversionRequest(BaseModel):
    model_name: str
    config_name: Optional[str] = None
    key_shift: int = 0
    formant_shift: int = 0
    f0_extractor: str = "rmvpe"
    vocoder: str = "nsf-hifigan"
    threshold: float = -60.0

class ModelInfo(BaseModel):
    name: str
    path: str
    config_path: Optional[str] = None
    size: int
    modified: datetime
    model_type: str
    compatible_configs: List[str] = []

# 工具函数
def generate_task_id() -> str:
    """生成任务ID"""
    return str(uuid.uuid4())

def get_file_md5(file_path: str) -> str:
    """计算文件MD5"""
    hash_md5 = hashlib.md5()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()

def ensure_directories():
    """确保必要的目录存在"""
    directories = [
        "temp", "cache", "logs", "outputs", "uploads"
    ]
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)

# 启动时初始化
@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    global audio_processor

    logger.info("🚀 木偶AI翻唱API服务启动中...")

    # 确保目录存在
    ensure_directories()

    # 初始化音频处理器
    try:
        audio_processor = AudioProcessor()
        logger.info("✅ 音频处理器初始化成功")
    except Exception as e:
        logger.error(f"❌ 音频处理器初始化失败: {e}")
        audio_processor = None

    # 扫描可用模型
    await scan_models()

    logger.info("✅ API服务启动完成")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("🛑 API服务正在关闭...")

    # 取消所有运行中的任务
    for task_id, task in tasks_storage.items():
        if task["status"] == "running":
            task["status"] = "cancelled"
            task["end_time"] = datetime.now()

    logger.info("✅ API服务已关闭")

# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat(),
        "gpu_available": check_gpu_availability(),
        "models_loaded": len(models_cache),
        "active_tasks": len([t for t in tasks_storage.values() if t["status"] == "running"])
    }

def check_gpu_availability() -> bool:
    """检查GPU可用性"""
    try:
        import torch
        return torch.cuda.is_available()
    except ImportError:
        return False

# 模型管理端点
@app.get("/models/list")
async def list_models():
    """获取可用模型列表"""
    return {
        "models": list(models_cache.values()),
        "count": len(models_cache)
    }

@app.get("/models/scan")
async def scan_models():
    """扫描模型目录"""
    models_dir = Path("models")
    if not models_dir.exists():
        return {"message": "模型目录不存在", "models": []}
    
    models_cache.clear()
    model_count = 0
    
    # 扫描.pt文件
    for model_file in models_dir.glob("*.pt"):
        try:
            # 查找对应的配置文件
            config_file = models_dir / f"{model_file.stem}.yaml"
            
            model_info = ModelInfo(
                name=model_file.stem,
                path=str(model_file),
                config_path=str(config_file) if config_file.exists() else None,
                size=model_file.stat().st_size,
                modified=datetime.fromtimestamp(model_file.stat().st_mtime),
                model_type="RectifiedFlow",  # 默认类型
                compatible_configs=[]
            )
            
            models_cache[model_info.name] = model_info.model_dump()
            model_count += 1
            
        except Exception as e:
            logger.error(f"扫描模型文件失败 {model_file}: {e}")
    
    logger.info(f"扫描完成，发现 {model_count} 个模型")
    return {
        "message": f"模型扫描完成，发现 {model_count} 个模型",
        "models": list(models_cache.values())
    }

# 文件上传端点
@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    """上传音频文件"""
    if not file.filename:
        raise HTTPException(status_code=400, detail="文件名不能为空")
    
    # 检查文件类型
    allowed_extensions = {'.wav', '.mp3', '.flac', '.ogg', '.m4a', '.aac'}
    file_extension = Path(file.filename).suffix.lower()
    
    if file_extension not in allowed_extensions:
        raise HTTPException(
            status_code=400, 
            detail=f"不支持的文件格式: {file_extension}"
        )
    
    # 生成唯一文件名
    file_id = str(uuid.uuid4())
    upload_path = Path("uploads") / f"{file_id}{file_extension}"
    
    try:
        # 保存文件
        with open(upload_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # 计算文件信息
        file_size = len(content)
        file_md5 = get_file_md5(str(upload_path))
        
        logger.info(f"文件上传成功: {file.filename} -> {upload_path}")
        
        return {
            "file_id": file_id,
            "filename": file.filename,
            "path": str(upload_path),
            "size": file_size,
            "md5": file_md5,
            "upload_time": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"文件上传失败: {e}")
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")

# 文件下载端点
@app.get("/download/{file_id}")
async def download_file(file_id: str):
    """下载文件"""
    # 在outputs目录中查找文件
    outputs_dir = Path("outputs")
    
    for file_path in outputs_dir.glob(f"{file_id}.*"):
        if file_path.is_file():
            return FileResponse(
                path=str(file_path),
                filename=file_path.name,
                media_type='application/octet-stream'
            )
    
    raise HTTPException(status_code=404, detail="文件不存在")

# 任务管理端点
@app.get("/tasks")
async def list_tasks():
    """获取所有任务"""
    return {
        "tasks": list(tasks_storage.values()),
        "count": len(tasks_storage)
    }

@app.get("/tasks/{task_id}")
async def get_task_status(task_id: str):
    """获取任务状态"""
    if task_id not in tasks_storage:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    return tasks_storage[task_id]

@app.delete("/tasks/{task_id}")
async def cancel_task(task_id: str):
    """取消任务"""
    if task_id not in tasks_storage:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    task = tasks_storage[task_id]
    if task["status"] == "running":
        task["status"] = "cancelled"
        task["end_time"] = datetime.now()
        task["message"] = "任务已被用户取消"
        
        # 通知WebSocket客户端
        await notify_websocket_clients(task_id, task)
    
    return {"message": "任务已取消", "task": task}

# 音频分离端点
@app.post("/separate")
async def separate_audio(
    background_tasks: BackgroundTasks,
    file_id: str,
    request: SeparationRequest
):
    """音频分离API"""
    # 检查上传的文件是否存在
    upload_path = None
    for ext in ['.wav', '.mp3', '.flac', '.ogg', '.m4a', '.aac']:
        potential_path = Path("uploads") / f"{file_id}{ext}"
        if potential_path.exists():
            upload_path = potential_path
            break

    if not upload_path:
        raise HTTPException(status_code=404, detail="上传文件不存在")

    # 创建任务
    task_id = generate_task_id()
    task = {
        "task_id": task_id,
        "type": "separation",
        "status": "pending",
        "progress": 0.0,
        "message": "任务已创建，等待处理",
        "start_time": datetime.now(),
        "end_time": None,
        "input_file": str(upload_path),
        "parameters": request.model_dump(),
        "result": None,
        "error": None
    }

    tasks_storage[task_id] = task

    # 在后台执行分离任务
    background_tasks.add_task(process_audio_separation, task_id, str(upload_path), request)

    logger.info(f"音频分离任务已创建: {task_id}")
    return {
        "task_id": task_id,
        "status": "accepted",
        "message": "音频分离任务已创建",
        "estimated_time": 120  # 预估时间（秒）
    }

async def process_audio_separation(task_id: str, input_file: str, request: SeparationRequest):
    """处理音频分离任务"""
    task = tasks_storage[task_id]

    try:
        # 更新任务状态
        task["status"] = "running"
        task["message"] = "正在初始化音频分离..."
        task["progress"] = 0.1
        await notify_websocket_clients(task_id, task)

        # 调用实际的MSST分离逻辑
        if audio_processor:
            output_dir = f"outputs/{task_id}_separation"

            async def progress_callback(progress: float, message: str):
                task["progress"] = progress
                task["message"] = message
                await notify_websocket_clients(task_id, task)

            result_files = await audio_processor.separate_audio(
                input_file,
                output_dir,
                request.mode,
                progress_callback
            )

            # 任务完成
            task["status"] = "completed"
            task["end_time"] = datetime.now()
            task["progress"] = 1.0
            task["message"] = "音频分离完成"
            task["result"] = result_files
        else:
            # 回退到模拟处理
            await simulate_separation_process(task_id, input_file, request)

            # 任务完成
            task["status"] = "completed"
            task["end_time"] = datetime.now()
            task["progress"] = 1.0
            task["message"] = "音频分离完成"
            task["result"] = {
                "vocal_file": f"outputs/{task_id}_vocal.wav",
                "instrumental_file": f"outputs/{task_id}_instrumental.wav"
            }

        logger.info(f"音频分离任务完成: {task_id}")

    except Exception as e:
        # 任务失败
        task["status"] = "error"
        task["end_time"] = datetime.now()
        task["error"] = str(e)
        task["message"] = f"音频分离失败: {str(e)}"

        logger.error(f"音频分离任务失败 {task_id}: {e}")

    finally:
        await notify_websocket_clients(task_id, task)

async def simulate_separation_process(task_id: str, input_file: str, request: SeparationRequest):
    """模拟音频分离过程"""
    task = tasks_storage[task_id]

    steps = [
        ("加载音频文件", 0.2),
        ("初始化MSST模型", 0.3),
        ("执行音频分离", 0.7),
        ("保存分离结果", 0.9),
        ("清理临时文件", 1.0)
    ]

    for step_name, progress in steps:
        if task["status"] == "cancelled":
            return

        task["message"] = f"正在{step_name}..."
        task["progress"] = progress
        await notify_websocket_clients(task_id, task)

        # 模拟处理时间
        await asyncio.sleep(2)

# 音色转换端点
@app.post("/convert")
async def convert_voice(
    background_tasks: BackgroundTasks,
    file_id: str,
    request: ConversionRequest
):
    """音色转换API"""
    # 检查上传的文件是否存在
    upload_path = None
    for ext in ['.wav', '.mp3', '.flac', '.ogg', '.m4a', '.aac']:
        potential_path = Path("uploads") / f"{file_id}{ext}"
        if potential_path.exists():
            upload_path = potential_path
            break

    if not upload_path:
        raise HTTPException(status_code=404, detail="上传文件不存在")

    # 检查模型是否存在
    if request.model_name not in models_cache:
        raise HTTPException(status_code=404, detail="指定的模型不存在")

    # 创建任务
    task_id = generate_task_id()
    task = {
        "task_id": task_id,
        "type": "conversion",
        "status": "pending",
        "progress": 0.0,
        "message": "任务已创建，等待处理",
        "start_time": datetime.now(),
        "end_time": None,
        "input_file": str(upload_path),
        "parameters": request.model_dump(),
        "result": None,
        "error": None
    }

    tasks_storage[task_id] = task

    # 在后台执行转换任务
    background_tasks.add_task(process_voice_conversion, task_id, str(upload_path), request)

    logger.info(f"音色转换任务已创建: {task_id}")
    return {
        "task_id": task_id,
        "status": "accepted",
        "message": "音色转换任务已创建",
        "estimated_time": 180  # 预估时间（秒）
    }

async def process_voice_conversion(task_id: str, input_file: str, request: ConversionRequest):
    """处理音色转换任务"""
    task = tasks_storage[task_id]

    try:
        # 更新任务状态
        task["status"] = "running"
        task["message"] = "正在初始化音色转换..."
        task["progress"] = 0.1
        await notify_websocket_clients(task_id, task)

        # 调用实际的main_reflow转换逻辑
        if audio_processor:
            model_info = models_cache[request.model_name]
            output_file = f"outputs/{task_id}_converted.wav"

            async def progress_callback(progress: float, message: str):
                task["progress"] = progress
                task["message"] = message
                await notify_websocket_clients(task_id, task)

            result_file = await audio_processor.convert_voice(
                input_file,
                output_file,
                model_info["model_path"],
                model_info.get("config_path"),
                request.key_shift,
                request.formant_shift,
                request.f0_extractor,
                progress_callback
            )

            # 任务完成
            task["status"] = "completed"
            task["end_time"] = datetime.now()
            task["progress"] = 1.0
            task["message"] = "音色转换完成"
            task["result"] = {
                "output_file": result_file
            }
        else:
            # 回退到模拟处理
            await simulate_conversion_process(task_id, input_file, request)

            # 任务完成
            task["status"] = "completed"
            task["end_time"] = datetime.now()
            task["progress"] = 1.0
            task["message"] = "音色转换完成"
            task["result"] = {
                "output_file": f"outputs/{task_id}_converted.wav"
            }

        logger.info(f"音色转换任务完成: {task_id}")

    except Exception as e:
        # 任务失败
        task["status"] = "error"
        task["end_time"] = datetime.now()
        task["error"] = str(e)
        task["message"] = f"音色转换失败: {str(e)}"

        logger.error(f"音色转换任务失败 {task_id}: {e}")

    finally:
        await notify_websocket_clients(task_id, task)

async def simulate_conversion_process(task_id: str, input_file: str, request: ConversionRequest):
    """模拟音色转换过程"""
    task = tasks_storage[task_id]

    steps = [
        ("加载模型和配置", 0.2),
        ("提取F0特征", 0.4),
        ("提取音量包络", 0.5),
        ("编码音频单元", 0.6),
        ("执行音色转换", 0.8),
        ("声码器合成", 0.95),
        ("保存输出文件", 1.0)
    ]

    for step_name, progress in steps:
        if task["status"] == "cancelled":
            return

        task["message"] = f"正在{step_name}..."
        task["progress"] = progress
        await notify_websocket_clients(task_id, task)

        # 模拟处理时间
        await asyncio.sleep(3)

# WebSocket端点
@app.websocket("/ws/{task_id}")
async def websocket_endpoint(websocket: WebSocket, task_id: str):
    """WebSocket连接端点"""
    await websocket.accept()
    websocket_connections[task_id] = websocket

    try:
        while True:
            # 保持连接活跃
            await websocket.receive_text()
    except WebSocketDisconnect:
        if task_id in websocket_connections:
            del websocket_connections[task_id]

async def notify_websocket_clients(task_id: str, task_data: Dict):
    """通知WebSocket客户端"""
    if task_id in websocket_connections:
        try:
            await websocket_connections[task_id].send_json({
                "type": "task_update",
                "task_id": task_id,
                "data": task_data
            })
        except Exception as e:
            logger.error(f"WebSocket通知失败: {e}")
            # 移除失效的连接
            if task_id in websocket_connections:
                del websocket_connections[task_id]

# 主函数
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="木偶AI翻唱API服务")
    parser.add_argument("--host", default="127.0.0.1", help="服务器地址")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口")
    parser.add_argument("--reload", action="store_true", help="开发模式自动重载")
    parser.add_argument("--log-level", default="info", help="日志级别")
    
    args = parser.parse_args()
    
    logger.info(f"启动API服务: http://{args.host}:{args.port}")
    logger.info(f"API文档: http://{args.host}:{args.port}/docs")
    
    uvicorn.run(
        "api_server:app",
        host=args.host,
        port=args.port,
        reload=args.reload,
        log_level=args.log_level
    )
