"""
手风琴基类
"""

from PySide6.QtWidgets import QFrame, QVBoxLayout, QPushButton, QWidget
from PySide6.QtCore import Qt, QPropertyAnimation, QEasingCurve, Signal
from PySide6.QtGui import QIcon

from ...utils.constants import Colors, Styles, Layout


class BaseAccordion(QFrame):
    """手风琴基类"""
    
    # 信号
    expanded = Signal(bool)  # 展开/收起信号
    
    def __init__(self, title: str, icon: str = None):
        super().__init__()
        self.title = title
        self.icon = icon
        self.is_expanded = False
        self.content_widget = None
        self.animation = None
        
        self.setProperty("class", "accordion")
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        # 创建主布局
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(0)
        
        # 创建标题按钮
        self.header_button = QPushButton()
        self.header_button.setProperty("class", "accordion-header")
        self.header_button.clicked.connect(self.toggle)
        self.update_header_text()
        
        # 设置标题按钮样式
        self.header_button.setStyleSheet(f"""
        QPushButton[class="accordion-header"] {{
            background-color: {Colors.SECONDARY_BG};
            border: none;
            border-radius: {Styles.INPUT_RADIUS};
            padding: {Styles.PADDING_NORMAL}px;
            text-align: left;
            font-weight: bold;
            font-size: {Styles.FONT_SIZE_LARGE}px;
            color: {Colors.FOREGROUND};
            min-height: 40px;
        }}
        
        QPushButton[class="accordion-header"]:hover {{
            background-color: {Colors.ACCENT};
        }}
        
        QPushButton[class="accordion-header"]:pressed {{
            background-color: {Colors.ACCENT_PRESSED};
        }}
        """)
        
        self.layout.addWidget(self.header_button)
        
        # 创建内容容器
        self.content_container = QFrame()
        self.content_container.setMaximumHeight(0)  # 初始状态为收起
        self.content_container.setStyleSheet(f"""
        QFrame {{
            background-color: {Colors.CARD_BG};
            border: none;
            border-bottom-left-radius: {Styles.CARD_RADIUS};
            border-bottom-right-radius: {Styles.CARD_RADIUS};
        }}
        """)
        
        # 内容布局
        self.content_layout = QVBoxLayout(self.content_container)
        self.content_layout.setContentsMargins(Styles.PADDING_NORMAL, Styles.PADDING_NORMAL,
                                              Styles.PADDING_NORMAL, Styles.PADDING_NORMAL)
        self.content_layout.setSpacing(Styles.MARGIN_NORMAL)
        
        self.layout.addWidget(self.content_container)
        
        # 创建动画
        self.animation = QPropertyAnimation(self.content_container, b"maximumHeight")
        self.animation.setDuration(Layout.ACCORDION_ANIMATION_DURATION)
        self.animation.setEasingCurve(QEasingCurve.InOutQuart)
        
    def update_header_text(self):
        """更新标题文本"""
        arrow = "▼" if self.is_expanded else "▶"
        icon_text = f"{self.icon} " if self.icon else ""
        self.header_button.setText(f"{arrow} {icon_text}{self.title}")
        
    def set_content_widget(self, widget: QWidget):
        """设置内容组件"""
        if self.content_widget:
            self.content_layout.removeWidget(self.content_widget)
            self.content_widget.deleteLater()
            
        self.content_widget = widget
        self.content_layout.addWidget(widget)
        
    def toggle(self):
        """切换展开/收起状态"""
        if self.is_expanded:
            self.collapse()
        else:
            self.expand()
            
    def expand(self):
        """展开手风琴"""
        if self.is_expanded:
            return
            
        self.is_expanded = True
        self.update_header_text()
        
        # 计算内容高度
        if self.content_widget:
            content_height = self.content_widget.sizeHint().height() + \
                           self.content_layout.contentsMargins().top() + \
                           self.content_layout.contentsMargins().bottom()
        else:
            content_height = 100  # 默认高度
            
        # 执行展开动画
        self.animation.setStartValue(0)
        self.animation.setEndValue(content_height)
        self.animation.start()
        
        self.expanded.emit(True)
        
    def collapse(self):
        """收起手风琴"""
        if not self.is_expanded:
            return
            
        self.is_expanded = False
        self.update_header_text()
        
        # 执行收起动画
        current_height = self.content_container.height()
        self.animation.setStartValue(current_height)
        self.animation.setEndValue(0)
        self.animation.start()
        
        self.expanded.emit(False)
        
    def is_accordion_expanded(self):
        """检查是否已展开"""
        return self.is_expanded
