{"仅供个人娱乐和非商业用途, 禁止用于血腥/暴力/性相关/政治相关内容。[点击前往教程文档](https://r1kc63iz15l.feishu.cn/wiki/JSp3wk7zuinvIXkIqSUcCXY1nKc)<br>本整合包完全免费, 严禁以任何形式倒卖, 如果你从任何地方**付费**购买了本整合包, 请**立即退款**。<br> 整合包作者: [bilibili@阿狸不吃隼舞](https://space.bilibili.com/403335715) [Github@KitsuneX07](https://github.com/KitsuneX07) | [Bilibili@Sucial](https://space.bilibili.com/445022409) [Github@SUC-DriverOld](https://github.com/SUC-DriverOld) | Gradio主题: [Gradio Theme](https://huggingface.co/spaces/NoCrypt/miku)": "개인적인 용도 및 비상업적 용도로만 사용하며, 유혈/폭력/성 관련/정치 관련 콘텐츠에 사용을 금지합니다. [튜토리얼 문서로 이동하기](https://r1kc63iz15l.feishu.cn/wiki/JSp3wk7zuinvIXkIqSUcCXY1nKc)<br>이 통합 패키지는 전적으로 무료이며, 어떤 형태로든 재판매를 엄격히 금지합니다. 만약 어디서든 이 통합 패키지를 **유료**로 구매하셨다면, **즉시 환불**하십시오.<br>통합 패키지 저자: [bilibili@阿狸不吃隼舞](https://space.bilibili.com/403335715) [Github@KitsuneX07](https://github.com/KitsuneX07) | [Bilibili@Sucial](https://space.bilibili.com/445022409) [Github@SUC-DriverOld](https://github.com/SUC-DriverOld) | Gradio 테마: [Gradio Theme](https://huggingface.co/spaces/NoCrypt/miku)", "MSST分离": "MSST 분리", "UVR分离": "UVR 분리", "预设流程": "사전 설정 흐름", "合奏模式": "합주 모드", "小工具": "도구", "安装模型": "모델 설치", "MSST训练": "MSST 훈련", "设置": "설정", "输出音轨": "출력 음향 트랙", "请至少添加2个模型到合奏流程": "합주 흐름에 최소 2개의 모델을 추가해주세요.", "合奏流程已保存": "합주 흐름이 저장되었습니다.", "请上传至少一个音频文件!": "오디오 파일을 최소 하나 이상 업로드하십시오!", "请先创建合奏流程": "먼저 합주 흐름을 생성해주세요.", "模型": "모델", "不存在": "존재하지 않음", "用户强制终止": "사용자 강제 종료", "处理失败: ": "처리 실패: ", "处理完成, 成功: ": "처리가 완료되었습니다, 성공: ", "个文件, 失败: ": "개 파일, 실패: ", "个文件": "개 파일", ", 结果已保存至: ": ", 결과는 다음 경로에 저장되었습니다: ", ", 耗时: ": ", 소요 시간: ", "请上传至少2个文件": "파일을 최소 2개 이상 업로드하십시오.", "上传的文件数目与权重数目不匹配": "업로드된 파일 수와 가중치 수가 일치하지 않습니다.", "处理完成, 文件已保存为: ": "처리가 완료되었습니다, 파일이 다음 이름으로 저장되었습니다: ", "处理失败!": "처리에 실패했습니다!", "input文件夹内文件列表:\\n": "입력 폴더 내 파일 목록:\\n", "input文件夹为空\\n": "입력 폴더가 비어 있음\\n", "results文件夹内文件列表:\\n": "결과 폴더 내 파일 목록:\\n", "results文件夹为空\\n": "결과 폴더가 비어 있음\\n", "已删除input文件夹内所有文件": "입력 폴더 내 모든 파일이 삭제됨", "已删除results文件夹内所有文件": "결과 폴더 내 모든 파일이 삭제됨", "请先选择模型": "먼저 모델을 선택하십시오", "仅输出主音轨": "주요 트랙만 출력", "仅输出次音轨": "보조 트랙만 출력", "已打开下载管理器": "다운로드 관리자가 열렸습니다", "选择模型": "모델 선택", "模型名字": "모델 이름", "模型类型": "모델 유형", "主要提取音轨": "주요 트랙 추출", "次要提取音轨": "보조 트랙 추출", "下载链接": "다운로드 링크", "模型类别": "모델 카테고리", "可提取音轨": "추출 가능한 트랙", "模型大小": "모델 크기", "模型已安装": "모델이 설치되었습니다", "无法校验sha256": "sha256을 확인할 수 없습니다.", "sha256校验失败": "sha256 검증 실패", "模型sha256校验失败, 请重新下载": "모델의 sha256 검증에 실패했습니다. 다시 다운로드하십시오", "sha256校验成功": "sha256 검증 성공", "模型未安装": "모델이 설치되지 않았습니다", "请选择模型类型": "모델 유형을 선택하십시오", "已安装": "설치됨", "请手动删除后重新下载": "수동으로 삭제한 후 다시 다운로드해주세요.", "下载成功": "다운로드 성공", "下载失败": "다운로드 실패", "已安装。请勿重复安装。": "설치되었습니다. 반복 설치하지 마세요.", "已打开": "열림", "的下载链接": "의 다운로드 링크", "上传参数文件": "파라미터 파일 업로드", "上传参数": "파라미터 업로드", "请上传'.yaml'格式的配置文件": "'.yaml' 형식의 설정 파일을 업로드하십시오", "请上传'ckpt', 'chpt', 'th'格式的模型文件": "'ckpt', 'chpt', 'th' 형식의 모델 파일을 업로드하십시오", "请输入正确的模型类别和模型类型": "올바른 모델 유형과 모델 유형을 입력하십시오.", "安装成功。重启WebUI以刷新模型列表": "설치 성공. WebUI를 재시작하여 모델 목록을 새로 고침하십시오", "安装失败": "설치 실패", "请输入选择模型参数": "모델 매개변수를 선택하십시오", "请上传'.json'格式的参数文件": "'.json' 형식의 파라미터 파일을 업로드하십시오", "请上传'.pth'格式的模型文件": "'.pth' 형식의 모델 파일을 업로드하십시오", "请输入正确的音轨名称": "올바른 트랙 이름을 입력하십시오", "配置保存成功!": "설정이 저장되었습니다!", "非官方模型不支持重置配置!": "비공식 모델은 설정 초기화를 지원하지 않습니다!", "配置重置成功!": "설정 재설정 성공!", "备份文件不存在!": "백업 파일이 존재하지 않습니다!", "选择输出音轨": "출력 트랙 선택", "请选择模型": "모델을 선택하십시오", "请选择输入目录": "입력 디렉토리를 선택하십시오", "请选择输出目录": "출력 디렉토리를 선택하십시오", "请选择GPU": "GPU를 선택하십시오", "处理完成, 结果已保存至: ": "처리 완료, 결과가 다음에 저장되었습니다: ", "暂无备份文件": "백업 파일이 없습니다", "作为下一模型输入(或结果输出)的音轨": "다음 모델 입력(또는 결과 출력)으로서의 트랙", "直接保存至输出目录的音轨(可多选)": "출력 디렉토리에 트랙을 직접 저장(여러 선택 가능)", "不输出": "출력하지 않음", "请填写预设名称": "프리셋 이름을 작성하십시오", "预设": "프리셋", "保存成功": "저장 성공", "请选择预设": "프리셋을 선택하십시오", "不支持的预设版本: ": "지원하지 않는 프리셋 버전: ", ", 请重新制作预设。": ", 프리셋을 다시 제작해주세요.", "预设版本不支持": "프리셋 버전이 지원되지 않습니다.", "预设不存在": "프리셋이 존재하지 않습니다", "删除成功": "삭제 성공", "预设已删除": "프리셋이 삭제되었습니다", "选择需要恢复的预设流程备份": "복원할 프리셋 프로세스 백업을 선택하십시오", "已成功恢复备份": "백업이 성공적으로 복원되었습니다", "设置重置成功, 请重启WebUI刷新! ": "설정이 초기화되었습니다. WebUI를 재시작하여 새로 고침하십시오!", "记录重置成功, 请重启WebUI刷新! ": "기록이 초기화되었습니다. WebUI를 재시작하여 새로 고침하십시오!", "请选择正确的模型目录": "올바른 모델 디렉토리를 선택하십시오", "设置保存成功! 请重启WebUI以应用。": "설정 저장 성공! 적용하려면 WebUI를 재시작하십시오.", "当前版本: ": "현재 버전: ", ", 发现新版本: ": ", 새 버전 발견: ", ", 已是最新版本": ", 최신 버전입니다", "检查更新失败": "업데이트 확인 실패", "语言已更改, 重启WebUI生效": "언어가 변경되었습니다. 적용하려면 WebUI를 재시작하십시오", "成功将端口设置为": "포트를 다음으로 설정했습니다", ", 重启WebUI生效": ", WebUI를 재시작하여 적용하십시오", "huggingface.co (需要魔法)": "huggingface.co", "hf-mirror.com (镜像站可直连)": "hf-mirror.com", "下载链接已更改": "다운로드 링크가 변경되었습니다", "公共链接已开启, 重启WebUI生效": "공용 링크가 활성화되었습니다, WebUI를 재시작하여 적용하십시오", "公共链接已关闭, 重启WebUI生效": "공용 링크가 비활성화되었습니다, WebUI를 재시작하여 적용하십시오", "已开启局域网分享, 重启WebUI生效": "로컬 네트워크 공유가 활성화되었습니다, WebUI를 재시작하여 적용하십시오", "已关闭局域网分享, 重启WebUI生效": "로컬 네트워크 공유가 비활성화되었습니다, WebUI를 재시작하여 적용하십시오", "已开启自动清理缓存": "자동 캐시 정리가 활성화되었습니다", "已关闭自动清理缓存": "자동 캐시 정리가 비활성화되었습니다", "已开启调试模式": "디버그 모드가 활성화되었습니다", "已关闭调试模式": "디버그 모드가 비활성화되었습니다", "主题已更改, 重启WebUI生效": "테마가 변경되었습니다, WebUI를 재시작하여 적용하십시오", "音频设置已保存": "오디오 설정이 저장되었습니다.", "已重命名为": "로 이름이 변경되었습니다", "选择模型类型": "모델 유형을 선택하십시오.", "请先选择模型类型": "먼저 모델 유형을 선택하십시오.", "新模型名称后缀错误!": "새 모델 이름의 확장자가 올바르지 않습니다!", "模型名字已存在! 请重新命名!": "모델 이름이 이미 존재합니다! 다시 이름을 지정하십시오!", "重命名失败!": "이름 변경에 실패했습니다!", "检测到": "감지됨", "旧版配置, 正在更新至最新版": "이전 버전 구성, 최신 버전으로 업데이트 중", "成功清理Gradio缓存": "Gradio 캐시 정리에 성공했습니다.", "请上传至少一个文件": "파일을 최소 하나 이상 업로드하십시오.", "单声道": "모노", "处理完成, 成功转换: ": "처리가 완료되었습니다, 성공적으로 변환됨: ", "请先下载SOME预处理模型并放置在tools/SOME_weights文件夹下! ": "먼저 SOME 전처리 모델을 다운로드하여 tools/SOME_weights 폴더에 놓아주십시오!", "请先选择模型保存路径! ": "먼저 모델 저장 경로를 선택하십시오!", "初始模型": "초기 모델", "模型类型错误, 请重新选择": "모델 유형이 잘못되었습니다, 다시 선택하십시오.", "配置文件不存在, 请重新选择": "설정 파일이 존재하지 않습니다, 다시 선택하십시오.", "数据集路径不存在, 请重新选择": "데이터셋 경로가 존재하지 않습니다, 다시 선택하십시오.", "验证集路径不存在, 请重新选择": "검증 데이터셋 경로가 존재하지 않습니다, 다시 선택하십시오.", "数据集类型错误, 请重新选择": "데이터셋 유형이 잘못되었습니다, 다시 선택하십시오.", "训练启动成功! 请前往控制台查看训练信息! ": "훈련이 성공적으로 시작되었습니다! 콘솔로 이동하여 훈련 정보를 확인하십시오!", "模型不存在, 请重新选择": "모델이 존재하지 않습니다, 다시 선택하십시오.", "验证完成! 请打开输出文件夹查看详细结果": "검증이 완료되었습니다! 출력 폴더를 열어 자세한 결과를 확인하십시오.", "错误: 无法找到增强配置文件模板, 请检查文件configs/augmentations_template.yaml是否存在。": "오류: 증강 설정 파일 템플릿을 찾을 수 없습니다, configs/augmentations_template.yaml 파일이 존재하는지 확인하십시오.", "已开启调试日志": "디버그 로그가 활성화되었습니다.", "已关闭调试日志": "디버그 로그가 비활성화되었습니다.", "模型不存在!": "모델이 존재하지 않습니다!", "输入音频分离": "오디오 파일 분리", "输入文件夹分离": "폴더 파일 분리", "请先选择文件夹!": "먼저 폴더를 선택하십시오!", "显存不足, 请尝试减小batchsize值和chunksize值后重试。": "그래픽 메모리가 부족합니다. batchsize와 chunksize 값을 줄인 후 다시 시도해주세요.", "内存不足，请尝试增大虚拟内存后重试。若分离时出现此报错，也可尝试将推理音频裁切短一些，分段分离。": "메모리가 부족합니다. 가상 메모리를 늘린 후 다시 시도해주세요. 분리 중 오류가 발생하면 오디오 길이를 줄여서 분리해보세요.", "FFmpeg未找到，请检查FFmpeg是否正确安装。若使用的是整合包，请重新安装。": "FFmpeg를 찾을 수 없습니다. FFmpeg가 올바르게 설치되었는지 확인해주세요. 통합 패키지를 사용하는 경우, 재설치를 시도해주세요.", "模型损坏，请重新下载并安装模型后重试。": "모델이 손상되었습니다. 모델을 다시 다운로드하고 설치한 후 시도해주세요.", "文件或路径不存在，请根据错误指示检查是否存在该文件。": "파일이나 경로가 존재하지 않습니다. 오류 메시지에 따라 파일이 존재하는지 확인해주세요.", "合奏模式可用于集成不同算法的结果, 具体的文档位于/docs/ensemble.md。目前主要有以下两种合奏方式:<br>1. 从原始音频合奏: 直接上传一个或多个音频文件, 然后选择多个模型进行处理, 将这些处理结果根据选择的合奏模式进行合奏<br>2. 从分离结果合奏: 上传多个已经分离完成的结果音频, 然后选择合奏模式进行合奏": "합주 모드는 서로 다른 알고리즘의 결과를 통합하는 데 사용됩니다. 구체적인 문서는 /docs/ensemble.md에 있습니다. 현재 주요 합주 방식은 다음과 같습니다:<br>1. 원본 오디오로부터 합주: 하나 이상의 오디오 파일을 업로드하고 여러 모델을 선택하여 처리한 후, 처리된 결과를 선택한 합주 모드에 따라 합주합니다.<br>2. 분리된 결과로 합주: 이미 분리된 오디오 결과를 여러 개 업로드하고, 선택한 합주 모드로 합주합니다.", "从原始音频合奏": "원본 오디오로 합주", "从原始音频合奏需要上传至少一个音频文件, 然后选择多个模型先进行分离处理, 然后将这些处理结果根据选择的合奏模式进行合奏。<br>注意, 请确保你的磁盘空间充足, 合奏过程会产生的临时文件仅会在处理结束后删除。": "원본 오디오로 합주하려면 최소 하나의 오디오 파일을 업로드하고 여러 모델을 선택하여 분리 처리한 후, 처리된 결과를 선택한 합주 모드에 따라 합주해야 합니다.<br>주의: 디스크 공간이 충분한지 확인해주세요. 합주 과정에서 생성되는 임시 파일은 처리 후에만 삭제됩니다.", "制作合奏流程": "합주 흐름 만들기", "权重": "가중치", "添加到合奏流程": "합주 흐름에 추가", "撤销上一步": "이전 단계 취소", "全部清空": "모두 비우기", "合奏流程": "합주 흐름", "保存此合奏流程": "이 합주 흐름 저장", "集成模式": "통합 모드", "输出格式": "출력 형식", "使用CPU (注意: 使用CPU会导致速度非常慢) ": "CPU 사용 (주의: CPU 사용 시 속도가 매우 느려질 수 있습니다)", "使用TTA (测试时增强), 可能会提高质量, 但时间x3": "TTA 사용 (테스트 시 강화), 품질이 향상될 수 있지만 속도가 약간 느려질 수 있습니다", "输出次级音轨 (例如: 合奏人声时, 同时输出伴奏)": "다음 레벨의 음향 트랙 출력 (예: 사람 목소리 합주 시 반주도 출력)", "输入音频": "입력 오디오", "上传一个或多个音频文件": "하나 이상의 오디오 파일 업로드", "输入文件夹": "입력 폴더", "输入目录": "입력 디렉터리", "选择文件夹": "폴더 선택", "打开文件夹": "폴더 열기", "输出目录": "출력 디렉터리", "从分离结果合奏": "분리된 결과로 합주", "从分离结果合奏需要上传至少两个音频文件, 这些音频文件是使用不同的模型分离同一段音频的结果。因此, 上传的所有音频长度应该相同。": "분리된 결과로 합주하려면 최소 두 개의 오디오 파일을 업로드해야 합니다. 이 파일들은 다른 모델을 사용하여 동일한 오디오를 분리한 결과입니다. 따라서 업로드하는 모든 오디오 파일의 길이가 동일해야 합니다.", "上传多个音频文件": "여러 오디오 파일 업로드", "权重(以空格分隔, 数量要与上传的音频一致)": "가중치(공백으로 구분하며, 업로드한 오디오 수와 일치해야 합니다)", "运行": "실행", "强制停止": "강제 중지", "### 集成模式": "### 통합 모드", "1. `avg_wave`: 在1D变体上进行集成, 独立地找到波形的每个样本的平均值<br>2. `median_wave`: 在1D变体上进行集成, 独立地找到波形的每个样本的中位数<br>3. `min_wave`: 在1D变体上进行集成, 独立地找到波形的每个样本的最小绝对值<br>4. `max_wave`: 在1D变体上进行集成, 独立地找到波形的每个样本的最大绝对值<br>5. `avg_fft`: 在频谱图 (短时傅里叶变换 (STFT) 2D变体) 上进行集成, 独立地找到频谱图的每个像素的平均值。平均后使用逆STFT得到原始的1D波形<br>6. `median_fft`: 与avg_fft相同, 但使用中位数代替平均值 (仅在集成3个或更多来源时有用) <br>7. `min_fft`: 与avg_fft相同, 但使用最小函数代替平均值 (减少激进程度) <br>8. `max_fft`: 与avg_fft相同, 但使用最大函数代替平均值 (增加激进程度) ": "1. `avg_wave`: 1D 변형에서 통합하며, 파형의 각 샘플의 평균 값을 독립적으로 찾습니다.<br>2. `median_wave`: 1D 변형에서 통합하며, 파형의 각 샘플의 중간 값을 독립적으로 찾습니다.<br>3. `min_wave`: 1D 변형에서 통합하며, 파형의 각 샘플의 최소 절대 값을 독립적으로 찾습니다.<br>4. `max_wave`: 1D 변형에서 통합하며, 파형의 각 샘플의 최대 절대 값을 독립적으로 찾습니다.<br>5. `avg_fft`: 주파수 스펙트럼(단시간 푸리에 변환(STFT) 2D 변형)에서 통합하며, 스펙트럼의 각 픽셀의 평균 값을 독립적으로 찾습니다. 평균 후 역 STFT를 사용하여 원래의 1D 파형을 얻습니다.<br>6. `median_fft`: avg_fft와 동일하지만 평균 값 대신 중간 값을 사용합니다(3개 이상의 소스를 통합할 때만 유용함).<br>7. `min_fft`: avg_fft와 동일하지만 평균 값 대신 최소 함수를 사용하여 Aggression을 줄입니다.<br>8. `max_fft`: avg_fft와 동일하지만 평균 값 대신 최대 함수를 사용하여 Aggression을 높입니다.", "### 注意事项": "### 주의사항", "1. min_fft可用于进行更保守的合成, 它将减少更激进模型的影响。<br>2. 最好合成等质量的模型。在这种情况下, 它将带来增益。如果其中一个模型质量不好, 它将降低整体质量。<br>3. 在原仓库作者的实验中, 与其他方法相比, avg_wave在SDR分数上总是更好或相等。<br>4. 最终会在输出目录下生成一个`ensemble_<集成模式>.wav`。": "1. min_fft는 보다 보수적인 합성을 위해 사용할 수 있으며, 더 공격적인 모델의 영향을 줄입니다.<br>2. 동일한 품질의 모델을 통합하는 것이 가장 좋습니다. 이 경우, 이득을 가져옵니다. 한 모델의 품질이 좋지 않을 경우, 전체 품질이 저하됩니다.<br>3. 원 저장소 저자의 실험에서, 다른 방법과 비교할 때 avg_wave는 SDR 점수에서 항상 더 좋거나 동일합니다.<br>4. 최종적으로 출력 디렉터리에 `ensemble_<통합 모드>.wav`가 생성됩니다.", "下载官方模型": "공식 모델 다운로드", "点击打开下载管理器": "클릭하여 다운로드 관리자 열기", "模型信息": "모델 정보", "打开模型目录": "모델 디렉터리 열기", "自动下载": "자동 다운로드", "手动下载": "수동 다운로드", "1. MSST模型默认下载在pretrain/<模型类型>文件夹下。UVR模型默认下载在设置中的UVR模型目录中。<br>2. 下加载进度可以打开终端查看。如果一直卡着不动或者速度很慢, 在确信网络正常的情况下请尝试重启WebUI。<br>3. 若下载失败, 会在模型目录**留下一个损坏的模型**, 请**务必**打开模型目录手动删除! <br>4. 点击“重启WebUI”按钮后, 会短暂性的失去连接, 随后会自动开启一个新网页。": "1. MSST 모델은 기본적으로 pretrain/<모델 유형> 폴더에 다운로드됩니다. UVR 모델은 설정의 UVR 모델 디렉터리에 기본 다운로드됩니다.<br>2. 다운로드 진행 상황은 터미널을 열어 확인할 수 있습니다. 계속 멈추거나 속도가 매우 느리면, 네트워크가 정상임을 확인한 후 WebUI를 재시작해 보십시오.<br>3. 다운로드에 실패하면 모델 디렉터리에 **손상된 모델이 남게 되므로**, 반드시 모델 디렉터리를 열어 수동으로 삭제하십시오! <br>4. “WebUI 재시작” 버튼을 클릭하면 일시적으로 연결이 끊기고 자동으로 새 웹페이지가 열립니다.", "### 模型下载链接": "### 모델 다운로드 링크", "1. 自动从Github, Huggingface或镜像站下载模型。<br>2. 你也可以在此整合包下载链接中的All_Models文件夹中找到所有可用的模型并下载。": "1. <PERSON><PERSON><PERSON><PERSON>, Hugging<PERSON> 또는 미러 사이트에서 자동으로 모델을 다운로드합니다.<br>2. 이 통합 패키지 다운로드 링크의 All_Models 폴더에서 모든 사용 가능한 모델을 찾아 다운로드할 수도 있습니다.", "若自动下载出现报错或下载过慢, 请点击手动下载, 跳转至下载链接。手动下载完成后, 请根据你选择的模型类型放置到对应文件夹内。": "자동 다운로드에 오류가 발생하거나 다운로드 속도가 너무 느린 경우, 수동 다운로드를 클릭하여 다운로드 링크로 이동하십시오. 수동 다운로드가 완료되면, 선택한 모델 유형에 따라 해당 폴더에 배치하십시오.", "### 当前UVR模型目录: ": "### 현재 UVR 모델 디렉터리: ", ", 如需更改, 请前往设置页面。": ", 변경하려면 설정 페이지로 이동하십시오.", "### 模型安装完成后, 需重启WebUI刷新模型列表": "### 모델 설치가 완료되면, WebUI를 재시작하여 모델 목록을 새로 고침하십시오.", "重启WebUI": "WebUI 재시작", "安装非官方MSST模型": "비공식 MSST 모델 설치", "你可以从其他途径获取非官方MSST模型, 在此页面完成配置文件设置后, 即可正常使用。<br>注意: 仅支持'.ckpt', '.th', '.chpt'格式的模型。模型显示名字为模型文件名。<br>选择模型类型: 共有三个可选项。依次代表人声相关模型, 多音轨分离模型, 单音轨分离模型。仅用于区分模型大致类型, 可任意选择。<br>选择模型类别: 此选项关系到模型是否能正常推理使用, 必须准确选择!": "다른 경로에서 비공식 MSST 모델을 획득할 수 있습니다. 이 페이지에서 설정 파일 설정을 완료하면 정상적으로 사용할 수 있습니다.<br>주의: '.ckpt', '.th', '.chpt' 형식의 모델만 지원됩니다. 모델 표시 이름은 모델 파일 이름입니다.<br>모델 유형 선택: 세 가지 옵션이 있습니다. 각각은 보컬 관련 모델, 다중 트랙 분리 모델, 단일 트랙 분리 모델을 나타냅니다. 모델의 대략적인 유형을 구분하는 용도로만 사용되며, 임의로 선택할 수 있습니다.<br>모델 카테고리 선택: 이 옵션은 모델이 정상적으로 추론을 사용할 수 있는지와 관련이 있으므로, 정확하게 선택해야 합니다!", "上传非官方MSST模型": "비공식 MSST 모델 업로드", "上传非官方MSST模型配置文件": "비공식 MSST 모델 설정 파일 업로드", "选择模型类别": "모델 카테고리 선택", "模型下载链接 (非必须，若无，可跳过)": "모델 다운로드 링크 (선택 사항이며, 없으면 건너뛰어도 됩니다)", "安装非官方VR模型": "비공식 VR 모델 설치", "你可以从其他途径获取非官方UVR模型, 在此页面完成配置文件设置后, 即可正常使用。<br>注意: 仅支持'.pth'格式的模型。模型显示名字为模型文件名。": "다른 경로에서 비공식 UVR 모델을 획득할 수 있습니다. 이 페이지에서 설정 파일 설정을 완료하면 정상적으로 사용할 수 있습니다.<br>주의: '.pth' 형식의 모델만 지원됩니다. 모델 표시 이름은 모델 파일 이름입니다.", "上传非官方VR模型": "비공식 VR 모델 업로드", "主要音轨名称": "주요 트랙 이름", "次要音轨名称": "보조 트랙 이름", "选择模型参数": "모델 매개변수 선택", "是否为Karaoke模型": "노래방 모델 여부", "是否为BV模型": "BV 모델 여부", "是否为VR 5.1模型": "VR 5.1 모델 여부", "MSST音频分离原项目地址: [https://github.com/ZFTurbo/Music-Source-Separation-Training](https://github.com/ZFTurbo/Music-Source-Separation-Training)": "MSST 오디오 분리 원 프로젝트 주소: [https://github.com/ZFTurbo/Music-Source-Separation-Training](https://github.com/ZFTurbo/Music-Source-Separation-Training)", "选择使用的GPU": "사용할 GPU 선택", "强制使用CPU推理, 注意: 使用CPU推理速度非常慢!": "CPU 추론을 강제로 사용합니다. 주의: CPU 추론 속도가 매우 느립니다!", "使用CPU": "CPU 사용", "[点击展开] 推理参数设置, 不同模型之间参数相互独立": "[확장 클릭] 추론 파라미터 설정, 다른 모델 간의 파라미터는 독립적입니다.", "只有在点击保存后才会生效。参数直接写入配置文件, 无法撤销。假如不知道如何设置, 请保持默认值。<br>请牢记自己修改前的参数数值, 防止出现问题以后无法恢复。请确保输入正确的参数, 否则可能会导致模型无法正常运行。<br>假如修改后无法恢复, 请点击``重置``按钮, 这会使得配置文件恢复到默认值。": "저장 버튼을 클릭해야만 적용됩니다. 매개변수는 설정 파일에 직접 기록되며, 되돌릴 수 없습니다. 설정 방법을 모르는 경우, 기본값을 유지하십시오.<br>문제가 발생하여 복구할 수 없게 되는 것을 방지하기 위해 변경 전의 매개변수 값을 기억해 두십시오. 올바른 매개변수를 입력했는지 확인하십시오. 그렇지 않으면 모델이 정상적으로 작동하지 않을 수 있습니다.<br>변경 후 복구할 수 없는 경우, ``재설정`` 버튼을 클릭하여 설정 파일을 기본값으로 복원할 수 있습니다.", "批次大小, 减小此值可以降低显存占用, 此参数对推理效果影响不大": "배치 크기, 이 값을 줄이면 그래픽 메모리 사용을 줄일 수 있으며, 이 파라미터는 추론 성능에 큰 영향을 미치지 않습니다.", "重叠数, 增大此值可以提高分离效果, 但会增加处理时间, 建议设置成4": "오버랩 수, 이 값을 늘리면 분리 효과가 개선되지만 처리 시간이 늘어나므로 4로 설정하는 것이 좋습니다.", "分块大小, 增大此值可以提高分离效果, 但会增加处理时间和显存占用": "블록 크기, 이 값을 늘리면 분리 효과가 향상되지만 처리 시간과 그래픽 메모리 사용이 증가합니다.", "音频归一化, 对音频进行归一化输入和输出, 部分模型没有此功能": "오디오 정규화, 오디오 입력과 출력을 정규화합니다. 일부 모델은 이 기능을 지원하지 않습니다.", "启用TTA, 能小幅提高分离质量, 若使用, 推理时间x3": "TTA 활성화, 분리 품질을 약간 개선할 수 있으며, 이를 사용할 경우 추론 시간이 3배 증가합니다.", "保存配置": "설정 저장", "重置配置": "설정 재설정", "预设流程允许按照预设的顺序运行多个模型。每一个模型的输出将作为下一个模型的输入。": "사전 설정된 순서대로 여러 모델을 실행할 수 있는 사전 설정 흐름을 허용합니다. 각 모델의 출력은 다음 모델의 입력으로 사용됩니다.", "使用预设": "사전 설정 사용", "该模式下的UVR推理参数将直接沿用UVR分离页面的推理参数, 如需修改请前往UVR分离页面。<br>修改完成后, 还需要任意处理一首歌才能保存参数! ": "이 모드에서의 UVR 추론 매개변수는 UVR 분리 페이지의 추론 매개변수를 직접 따릅니다, 수정하려면 UVR 분리 페이지로 이동하십시오.<br>수정 완료 후, 매개변수를 저장하려면 임의로 노래 하나를 처리해야 합니다!", "将次级输出保存至输出目录的单独文件夹内": "보조 출력을 출력 디렉터리의 별도 폴더에 저장", "制作预设": "사전 설정 제작", "预设名称": "사전 설정 이름", "请输入预设名称": "사전 설정 이름을 입력하십시오.", "添加至流程": "흐름에 추가", "保存上述预设流程": "위의 사전 설정 흐름 저장", "管理预设": "사전 설정 관리", "此页面提供查看预设, 删除预设, 备份预设, 恢复预设等功能<br>`model_type`: 模型类型；`model_name`: 模型名称；`input_to_next`: 作为下一模型输入的音轨；`output_to_storage`: 直接保存至输出目录下的direct_output文件夹内的音轨, **不会经过后续流程处理**<br>每次点击删除预设按钮时, 将自动备份预设以免误操作。": "이 페이지는 사전 설정 보기, 사전 설정 삭제, 사전 설정 백업, 사전 설정 복원 등의 기능을 제공합니다.<br>`model_type`: 모델 유형; `model_name`: 모델 이름; `input_to_next`: 다음 모델의 입력으로 사용되는 트랙; `output_to_storage`: 출력 디렉터리 내의 direct_output 폴더에 트랙을 직접 저장하며, **후속 프로세스를 거치지 않습니다**<br>사전 설정 삭제 버튼을 클릭할 때마다, 실수로 인한 오작동을 방지하기 위해 사전 설정을 자동으로 백업합니다.", "删除所选预设": "선택된 사전 설정 삭제", "请先选择预设": "먼저 사전 설정을 선택하십시오.", "恢复所选预设": "선택된 사전 설정 복원", "打开备份文件夹": "백업 폴더 열기", "WebUI设置": "WebUI 설정", "GPU信息": "GPU 정보", "系统信息": "시스템 정보", "设置WebUI端口, 0为自动": "WebUI 포트 설정, 0은 자동", "选择语言": "언어 선택", "选择MSST模型下载链接": "MSST 모델 다운로드 링크 선택", "选择WebUI主题": "WebUI 테마 선택", "对本地局域网开放WebUI: 开启后, 同一局域网内的设备可通过'本机IP:端口'的方式访问WebUI。": "로컬 네트워크에서 WebUI 열기: 활성화하면 동일한 로컬 네트워크 내의 장치가 '로컬 IP:포트'를 통해 WebUI에 접근할 수 있습니다.", "开启公共链接: 开启后, 他人可通过公共链接访问WebUI。链接有效时长为72小时。": "공용 링크 활성화: 활성화하면 다른 사람들이 공용 링크를 통해 WebUI에 접근할 수 있습니다. 링크 유효 기간은 72시간입니다.", "自动清理缓存: 开启后, 每次启动WebUI时会自动清理缓存。": "자동 캐시 정리: 활성화하면 WebUI 시작 시 자동으로 캐시를 정리합니다.", "全局调试模式: 向开发者反馈问题时请开启。(该选项支持热切换)": "전체 디버그 모드: 개발자에게 문제를 피드백할 때 활성화하십시오. (이 옵션은 핫 스위칭을 지원합니다)", "选择UVR模型目录": "UVR 모델 디렉터리 선택", "检查更新": "업데이트 확인", ", 请点击检查更新按钮": ", 업데이트 확인 버튼을 클릭하십시오.", "前往Github瞅一眼": "Github 릴리즈 페이지로 이동", "重置WebUI路径记录": "WebUI 경로 기록 재설정", "重置WebUI设置": "WebUI 설정 재설정", "### 选择UVR模型目录": "### UVR 모델 디렉터리 선택", "如果你的电脑中有安装UVR5, 你不必重新下载一遍UVR5模型, 只需在下方“选择UVR模型目录”中选择你的UVR5模型目录, 定位到models/VR_Models文件夹。<br>例如: E:/Program Files/Ultimate Vocal Remover/models/VR_Models 点击保存设置或重置设置后, 需要重启WebUI以更新。": "컴퓨터에 UVR5가 설치되어 있는 경우, UVR5 모델을 다시 다운로드할 필요가 없습니다. 아래의 'UVR 모델 디렉터리 선택'에서 UVR5 모델 디렉터리를 선택하고 models/VR_Models 폴더를 지정하십시오.<br>예: E:/Program Files/Ultimate Vocal Remover/models/VR_Models 설정 저장 또는 설정 재설정 후 WebUI를 재시작하여 업데이트해야 합니다.", "### 检查更新": "### 업데이트 확인", "从Github检查更新, 需要一定的网络要求。点击检查更新按钮后, 会自动检查是否有最新版本。你可以前往此整合包的下载链接或访问Github仓库下载最新版本。": "GitHub에서 업데이트를 확인하려면 일정한 네트워크 요구 사항이 필요합니다. 업데이트 확인 버튼을 클릭하면 자동으로 최신 버전을 확인합니다. 이 통합 패키지의 다운로드 링크로 이동하거나 GitHub 저장소를 방문하여 최신 버전을 다운로드할 수 있습니다.", "### 重置WebUI路径记录": "### WebUI 경로 기록 재설정", "将所有输入输出目录重置为默认路径, 预设/模型/配置文件以及上面的设置等**不会重置**, 无需担心。重置WebUI设置后, 需要重启WebUI。": "모든 입력 및 출력 디렉터리를 기본 경로로 재설정합니다. 프리셋/모델/설정 파일 및 상기 설정들은 **재설정되지 않으므로** 걱정하지 않으셔도 됩니다. WebUI 설정을 재설정한 후에는 WebUI를 재시작해야 합니다.", "### 重置WebUI设置": "### WebUI 설정 재설정", "仅重置WebUI设置, 例如UVR模型路径, WebUI端口等。重置WebUI设置后, 需要重启WebUI。": "WebUI 설정만 재설정합니다, 예를 들어 UVR 모델 경로, WebUI 포트 등. WebUI 설정을 재설정한 후에는 WebUI를 재시작해야 합니다.", "### 重启WebUI": "### WebUI 재시작", "点击 “重启WebUI” 按钮后, 会短暂性的失去连接, 随后会自动开启一个新网页。": "'WebUI 재시작' 버튼을 클릭하면 잠시 연결이 끊기고, 자동으로 새 웹페이지가 열립니다.", "音频输出设置": "오디오 출력 설정", "此页面支持用户自定义修改MSST/VR推理后输出音频的质量。输出音频的**采样率, 声道数与模型支持的参数有关, 无法更改**。<br>修改完成后点击保存设置即可生效。": "이 페이지는 사용자가 MSST/VR 추론 후 출력 오디오의 품질을 사용자 정의로 수정할 수 있습니다. 출력 오디오의 **샘플링 속도, 채널 수는 모델이 지원하는 매개변수에 따라 달라지며, 변경할 수 없습니다**.<br>수정이 완료되면 설정 저장을 클릭하여 적용하세요.", "输出wav位深度": "출력 wav 비트 심도", "输出flac位深度": "출력 flac 비트 심도", "输出mp3比特率(bps)": "출력 mp3 비트레이트(bps)", "保存设置": "설정 저장", "模型改名": "모델 이름 변경", "此页面支持用户自定义修改模型名字, 以便记忆和使用。修改完成后, 需要重启WebUI以刷新模型列表。<br>【注意】此操作不可逆 (无法恢复至默认命名), 请谨慎命名。输入新模型名字时, 需保留后缀!": "이 페이지는 사용자가 모델 이름을 커스터마이즈하여 기억하고 사용할 수 있도록 지원합니다. 변경을 완료한 후에는 모델 목록을 새로고침하기 위해 WebUI를 재시작해야 합니다.<br>[주의] 이 작업은 되돌릴 수 없습니다 (기본 이름으로 복구할 수 없음). 신중하게 이름을 지정하십시오. 새 모델 이름을 입력할 때는 확장자를 유지해야 합니다!", "新模型名": "새 모델 이름", "请输入新模型名字, 需保留后缀!": "새 모델 이름을 입력하십시오, 확장자를 유지해야 합니다!", "确认修改": "변경 확인", "立体声": "스테레오", "音频格式转换": "오디오 형식 변환", "上传一个或多个音频文件并将其转换为指定格式。<br>支持的格式包括 .mp3, .flac, .wav, .ogg, .m4a, .wma, .aac...等等。<br>**不支持**网易云音乐/QQ音乐等加密格式, 如.ncm, .qmc等。": "하나 이상의 오디오 파일을 업로드하고 지정된 형식으로 변환하십시오.<br>지원되는 형식은 .mp3, .flac, .wav, .ogg, .m4a, .wma, .aac 등입니다.<br>**지원되지 않음**은 NetEase Cloud Music/QQ Music 등의 암호화 형식인 .ncm, .qmc 등입니다.", "选择或输入音频输出格式": "오디오 출력 형식 선택 또는 입력", "选择音频输出目录": "오디오 출력 디렉터리 선택", "输出音频采样率(Hz)": "출력 오디오 샘플링 속도(Hz)", "输出音频声道数": "출력 오디오 채널 수", "输出ogg比特率(bps)": "출력 ogg 비트레이트(bps)", "转换音频": "오디오 변환", "合并音频": "오디오 병합", "点击合并音频按钮后, 将自动把输入文件夹中的所有音频文件合并为一整个音频文件<br>合并后的音频会保存至输出目录中, 文件名为merged_audio_<文件夹名字>.wav": "오디오 병합 버튼을 클릭하면 입력 폴더의 모든 오디오 파일이 자동으로 하나의 오디오 파일로 병합됩니다.<br>병합된 오디오는 출력 디렉토리에 저장되며, 파일 이름은 merged_audio_<폴더 이름>.wav 입니다.", "计算SDR": "SDR 계산", "上传两个**wav音频文件**并计算它们的[SDR](https://www.aicrowd.com/challenges/music-demixing-challenge-ismir-2021#evaluation-metric)。<br>SDR是一个用于评估模型质量的数值。数值越大, 模型算法结果越好。": "두 개의 **wav 오디오 파일**을 업로드하고 그들의 [SDR](https://www.aicrowd.com/challenges/music-demixing-challenge-ismir-2021#evaluation-metric)을 계산하십시오.<br>SDR은 모델 품질을 평가하기 위한 수치입니다. 값이 클수록, 모델 알고리즘의 결과가 더 좋습니다.", "参考音频": "참고 오디오", "待估音频": "평가할 오디오", "歌声转MIDI": "보컬을 MIDI로 변환", "歌声转MIDI功能使用开源项目[SOME](https://github.com/openvpi/SOME/), 可以将分离得到的**干净的歌声**转换成.mid文件。<br>【必须】若想要使用此功能, 请先下载权重文件[model_steps_64000_simplified.ckpt](https://hf-mirror.com/Sucial/MSST-WebUI/resolve/main/SOME_weights/model_steps_64000_simplified.ckpt)并将其放置在程序目录下的`tools/SOME_weights`文件夹内。文件命名不可随意更改!": "노래를 MIDI로 변환하는 기능은 오픈 소스 프로젝트[SOME](https://github.com/openvpi/SOME/)를 사용하여 분리된 **깨끗한 노래**를 .mid 파일로 변환할 수 있습니다.<br>【필수】이 기능을 사용하려면 먼저 가중치 파일[model_steps_64000_simplified.ckpt](https://huggingface.co/Sucial/MSST-WebUI/resolve/main/SOME_weights/model_steps_64000_simplified.ckpt)을 다운로드하여 프로그램 디렉토리의 `tools/SOME_weights` 폴더에 넣어야 합니다. 파일 이름을 임의로 변경할 수 없습니다.", "如果不知道如何测量歌曲BPM, 可以尝试这两个在线测量工具: [bpmdetector](https://bpmdetector.kniffen.dev/) | [key-bpm-finder](https://vocalremover.org/zh/key-bpm-finder), 测量时建议上传原曲或伴奏, 若干声可能导致测量结果不准确。": "노래 BPM을 측정하는 방법을 모르는 경우, 다음 두 개의 온라인 측정 도구를 시도해 볼 수 있습니다: [bpmdetector](https://bpmdetector.kniffen.dev/) | [key-bpm-finder](https://vocalremover.org/zh/key-bpm-finder). 측정 시 원곡이나 반주를 업로드하는 것이 좋으며, 몇 개의 음성이 포함되면 측정 결과가 부정확할 수 있습니다.", "上传音频": "오디오 업로드", "输入音频BPM": "오디오 BPM 입력", "开始转换": "변환 시작", "1. 音频BPM (每分钟节拍数) 可以通过MixMeister BPM Analyzer等软件测量获取。<br>2. 为保证MIDI提取质量, 音频文件请采用干净清晰无混响底噪人声。<br>3. 输出MIDI不带歌词信息, 需要用户自行添加歌词。<br>4. 实际使用体验中部分音符会出现断开的现象, 需自行修正。SOME的模型主要面向DiffSinger唱法模型自动标注, 比正常用户在创作中需要的MIDI更加精细, 因而可能导致模型倾向于对音符进行切分。<br>5. 提取的MIDI没有量化/没有对齐节拍/不适配BPM, 需自行到各编辑器中手动调整。": "1. 오디오 BPM(분당 비트 수)은 MixMeister BPM Analyzer 등의 소프트웨어를 통해 측정할 수 있습니다.<br>2. MIDI 추출 품질을 보장하기 위해, 오디오 파일은 깨끗하고 명확하며 리버브 및 배경 노이즈가 없는 보컬을 사용하십시오.<br>3. 출력된 MIDI는 가사 정보를 포함하지 않으며, 사용자가 직접 가사를 추가해야 합니다.<br>4. 실제 사용 경험에서 일부 음표가 끊어지는 현상이 발생할 수 있으며, 수동으로 수정해야 합니다. SOME의 모델은 DiffSinger 노래방 모델 자동 표기를 주로 대상으로 하며, 일반 사용자가 창작에서 필요한 MIDI보다 더 정밀하기 때문에, 모델이 음표를 분할하는 경향이 있을 수 있습니다.<br>5. 추출된 MIDI는 퀀타이징이 되어 있지 않으며, 박자가 맞춰져 있지 않거나 BPM에 적합하지 않으므로, 각 편집기에서 수동으로 조정해야 합니다.", "此页面提供数据集制作教程, 训练参数选择, 以及一键训练。有关配置文件的修改和数据集文件夹的详细说明请参考MSST原项目: [https://github.com/ZFTurbo/Music-Source-Separation-Training](https://github.com/ZFTurbo/Music-Source-Separation-Training)<br>在开始下方的模型训练之前, 请先进行训练数据的制作。<br>说明: 数据集类型即训练集制作Step 1中你选择的类型, 1: Type1; 2: Type2; 3: Type3; 4: Type4, 必须与你的数据集类型相匹配。": "이 페이지는 데이터셋 제작 튜토리얼, 훈련 매개변수 선택, 및 일키 훈련을 제공합니다. 설정 파일의 수정 및 데이터셋 폴더의 자세한 설명은 MSST 원 프로젝트 [https://github.com/ZFTurbo/Music-Source-Separation-Training](https://github.com/ZFTurbo/Music-Source-Separation-Training)를 참조하십시오.<br>모델 훈련을 시작하기 전에, 먼저 훈련 데이터를 제작하십시오.<br>설명: 데이터셋 유형은 훈련 데이터 제작의 Step 1에서 선택한 유형, 1: Type1; 2: Type2; 3: Type3; 4: Type4와 일치해야 합니다.", "训练": "훈련", "选择训练模型类型": "훈련할 모델 유형 선택", "配置文件路径": "설정 파일 경로", "请输入配置文件路径或选择配置文件": "설정 파일 경로를 입력하거나 설정 파일을 선택하십시오", "选择配置文件": "설정 파일 선택", "数据集类型": "데이터셋 유형", "数据集路径": "데이터셋 경로", "请输入或选择数据集文件夹": "데이터셋 폴더를 입력하거나 선택하십시오", "选择数据集文件夹": "데이터셋 폴더 선택", "验证集路径": "검증 데이터셋 경로", "请输入或选择验证集文件夹": "검증 데이터셋 폴더를 입력하거나 선택하십시오", "选择验证集文件夹": "검증 데이터셋 폴더 선택", "模型保存路径": "모델 저장 경로", "请输入或选择模型保存文件夹": "모델 저장 폴더를 입력하거나 선택하십시오", "选择初始模型, 若无初始模型, 留空或选择None即可": "초기 모델을 선택하세요. 초기 모델이 없다면 비워두거나 None을 선택하세요.", "刷新初始模型列表": "초기 모델 목록 새로고침", "训练参数设置": "훈련 매개변수 설정", "num_workers: 数据集读取线程数, 0为自动": "num_workers: 데이터셋 읽기 스레드 수, 0은 자동", "随机数种子, 0为随机": "랜덤 시드, 0은 무작위", "是否将加载的数据放置在固定内存中, 默认为否": "로딩된 데이터를 고정 메모리에 배치할지 여부, 기본값은 아니오", "是否使用加速训练, 对于多显卡用户会加快训练": "가속 훈련을 사용할지 여부, 다중 GPU 사용자의 경우 훈련 속도가 빨라집니다", "是否在训练前验证模型, 默认为否": "훈련 전에 모델을 검증할지 여부, 기본값은 아니오", "是否使用MultiSTFT Loss, 默认为否": "MultiSTFT Loss를 사용할지 여부, 기본값은 아니오", "是否使用MSE loss, 默认为否": "MSE loss를 사용할지 여부, 기본값은 아니오", "是否使用L1 loss, 默认为否": "L1 loss를 사용할지 여부, 기본값은 아니오", "选择输出的评估指标": "출력 평가 지표 선택", "选择调度器使用的评估指标": "스케줄러에서 사용할 평가 지표 선택", "保存上述训练配置": "위의 훈련 설정 저장", "开始训练": "훈련 시작", "点击开始训练后, 请到终端查看训练进度或报错, 下方不会输出报错信息, 想要停止训练可以直接关闭终端。在训练过程中, 你也可以关闭网页, 仅**保留终端**。": "'훈련 시작'을 클릭한 후, 터미널에서 훈련 진행 상황 또는 오류를 확인하십시오. 아래쪽에는 오류 정보가 출력되지 않으며, 훈련을 중지하려면 터미널을 직접 닫으면 됩니다. 훈련 과정에서는 페이지를 닫을 수 있으며, **터미널만 열은 상태를 유지**하십시오.", "验证": "검증", "此页面用于手动验证模型效果, 测试验证集, 输出SDR测试信息。输出的信息会存放在输出文件夹的results.txt中。<br>下方参数将自动加载训练页面的参数, 在训练页面点击保存训练参数后, 重启WebUI即可自动加载。当然你也可以手动输入参数。<br>": "이 페이지는 모델의 효과를 수동으로 검증하고 검증 세트를 테스트하며 SDR 테스트 정보를 출력하는 데 사용됩니다. 출력된 정보는 출력 폴더의 results.txt에 저장됩니다.<br>아래 매개변수는 훈련 페이지의 매개변수를 자동으로 불러오며, 훈련 페이지에서 훈련 매개변수를 저장한 후 WebUI를 재시작하면 자동으로 불러옵니다. 물론 매개변수를 수동으로 입력할 수도 있습니다.<br>", "模型路径": "모델 경로", "请输入或选择模型文件": "모델 파일을 입력하거나 선택하십시오", "选择模型文件": "모델 파일 선택", "验证参数设置": "검증 매개변수 설정", "选择验证集音频格式": "검증 세트 오디오 형식 선택", "验证集读取线程数, 0为自动": "검증 세트 읽기 스레드 수, 0은 자동", "开始验证": "검증 시작", "训练集制作指南": "훈련 세트 제작 가이드", "Step 1: 数据集制作": "Step 1: 데이터 세트 제작", "请**任选下面四种类型之一**制作数据集文件夹, 并按照给出的目录层级放置你的训练数据。完成后, 记录你的数据集**文件夹路径**以及你选择的**数据集类型**, 以便后续使用。": "아래 네 가지 유형 중 하나를 선택하여 데이터 세트 폴더를 제작하고, 제공된 디렉토리 계층에 따라 훈련 데이터를 배치하십시오. 완료 후, 데이터 세트 **폴더 경로**와 선택한 **데이터 세트 유형**을 기록하여 이후에 사용하십시오.", "不同的文件夹。每个文件夹包含所需的所有stems, 格式为stem_name.wav。与MUSDBHQ18数据集相同。在最新的代码版本中, 可以使用flac替代wav。<br>例如: ": "다양한 폴더. 각 폴더는 필요한 모든 stems을 포함하며, 형식은 stem_name.wav입니다. MUSDBHQ18 데이터 세트와 동일합니다. 최신 코드 버전에서는 flac을 wav 대신 사용할 수 있습니다.<br>예: ", "每个文件夹是stem_name。文件夹中包含仅由所需stem组成的wav文件。<br>例如: ": "각 폴더는 stem_name입니다. 폴더에는 필요한 stem으로만 설정된 wav 파일이 포함됩니다.<br>예: ", "可以提供以下结构的CSV文件 (或CSV文件列表) <br>例如: ": "다음 구조의 CSV 파일(또는 CSV 파일 목록) 제공 가능<br>예: ", "与类型1相同, 但在训练过程中所有乐器都将来自歌曲的相同位置。<br>例如: ": "유형 1과 동일하지만, 훈련 과정에서 모든 악기는 노래의 동일한 위치에서 옵니다.<br>예: ", "Step 2: 验证集制作": "Step 2: 검증 세트 제작", "验证集制作。验证数据集**必须**与上面数据集制作的Type 1(MUSDB)数据集**结构相同** (**无论你使用哪种类型的数据集进行训练**) , 此外每个文件夹还必须包含每首歌的mixture.wav, mixture.wav是所有stem的总和<br>例如: ": "검증 세트 제작. 검증 데이터 세트는 위에서 제작한 Type 1(MUSDB) 데이터 세트의 **구조와 동일해야 합니다** (**어떤 유형의 데이터 세트를 사용하여 훈련하든 상관없음**), 또한 각 폴더에는 각 노래의 mixture.wav가 포함되어야 하며, mixture.wav는 모든 stems의 합계입니다.<br>예: ", "Step 3: 选择并修改修改配置文件": "Step 3: 설정 파일 선택 및 수정", "请先明确你想要训练的模型类型, 然后选择对应的配置文件进行修改。<br>目前有以下几种模型类型: ": "먼저 훈련하려는 모델 유형을 명확히 하고, 해당하는 설정 파일을 선택하여 수정하십시오.<br>현재 다음과 같은 모델 유형이 있습니다: ", "<br>确定好模型类型后, 你可以前往整合包根目录中的configs_backup文件夹下找到对应的配置文件模板。复制一份模板, 然后根据你的需求进行修改。修改完成后记下你的配置文件路径, 以便后续使用。<br>特别说明: config_musdb18_xxx.yaml是针对MUSDB18数据集的配置文件。<br>": "<br>모델 유형을 결정한 후, 통합 패키지의 루트 디렉토리에 있는 configs_backup 폴더로 이동하여 해당 설정 파일 템플릿을 찾을 수 있습니다. 템플릿을 복사한 후, 필요에 따라 수정하십시오. 수정이 완료되면 설정 파일 경로를 기록하여 이후에 사용하십시오.<br>특별 설명: config_musdb18_xxx.yaml은 MUSDB18 데이터 세트를 위한 설정 파일입니다.<br>", "打开配置文件模板文件夹": "설정 파일 템플릿 폴더 열기", "你可以使用下表根据你的GPU选择用于训练的BS_Roformer模型的batch_size参数。表中提供的批量大小值适用于单个GPU。如果你有多个GPU, 则需要将该值乘以GPU的数量。": "다음 표를 사용하여 GPU에 따라 훈련에 사용할 BS_Roformer 모델의 batch_size 매개변수를 선택할 수 있습니다. 표에서 제공된 배치 크기 값은 단일 GPU에 적합합니다. 다중 GPU가 있는 경우, 이 값을 GPU 수에 곱해야 합니다.", "Step 4: 数据增强": "Step 4: 데이터 증강", "数据增强可以动态更改stem, 通过从旧样本创建新样本来增加数据集的大小。现在, 数据增强的控制在配置文件中进行。下面是一个包含所有可用数据增强的完整配置示例。你可以将其复制到你的配置文件中以使用数据增强。<br>注意:<br>1. 要完全禁用所有数据增强, 可以从配置文件中删除augmentations部分或将enable设置为false。<br>2. 如果要禁用某些数据增强, 只需将其设置为0。<br>3. all部分中的数据增强应用于所有stem。<br>4. vocals/bass等部分中的数据增强仅应用于相应的stem。你可以为training.instruments中给出的所有stem创建这样的部分。": "데이터 증강은 stem을 동적으로 변경하여 기존 샘플에서 새 샘플을 생성함으로써 데이터 세트 크기를 늘릴 수 있습니다. 현재, 데이터 증강의 제어는 설정 파일에서 이루어집니다. 아래는 모든 사용 가능한 데이터 증강을 포함하는 전체 설정 예제입니다. 이를 설정 파일에 복사하여 데이터 증강을 사용할 수 있습니다.<br>주의:<br>1. 모든 데이터 증강을 완전히 비활성화하려면, 설정 파일에서 augmentations 섹션을 삭제하거나 enable을 false로 설정하십시오.<br>2. 특정 데이터 증강을 비활성화하려면, 해당 값을 0으로 설정하십시오.<br>3. all 섹션의 데이터 증강은 모든 stem에 적용됩니다.<br>4. vocals/bass 등의 섹션에 있는 데이터 증강은 해당 stem에만 적용됩니다. training.instruments에 있는 모든 stem에 대해 이러한 섹션을 생성할 수 있습니다.", "说明: 本整合包仅融合了UVR的VR Architecture模型, MDX23C和HtDemucs类模型可以直接使用前面的MSST音频分离。<br>UVR分离使用项目: [https://github.com/nomadkaraoke/python-audio-separator](https://github.com/nomadkaraoke/python-audio-separator) 并进行了优化。": "설명: 이 통합 패키지는 UVR의 VR Architecture 모델만 통합하였으며, MDX23C 및 HtDemucs 클래스 모델은 이전의 MSST 오디오 분리를 직접 사용할 수 있습니다.<br>UVR 분리는 프로젝트: [https://github.com/nomadkaraoke/python-audio-separator](https://github.com/nomadkaraoke/python-audio-separator)을 사용하며 최적화되었습니다.", "Window Size: 窗口大小, 用于平衡速度和质量, 默认为512": "윈도우 크기: 속도와 품질을 균형 있게 조절하는 데 사용되며 기본값은 512입니다.", "Aggression: 主干提取强度, 范围-100-100, 人声请选5": "Aggression: 주 추출 강도, 범위 -100-100, 보컬의 경우 5를 선택하십시오.", "[点击展开] 以下是一些高级设置, 一般保持默认即可": "[확장 클릭] 고급 설정, 일반적으로 기본값을 유지할 수 있습니다.", "批次大小, 减小此值可以降低显存占用": "배치 크기, 이 값을 줄이면 그래픽 메모리 사용을 줄일 수 있습니다.", "后处理特征阈值, 取值为0.1-0.3, 默认0.2": "후처리 특징 임계값, 값은 0.1-0.3 사이이며 기본값은 0.2입니다.", "次级输出使用频谱而非波形进行反转, 可能会提高质量, 但速度稍慢": "보조 출력은 파형 대신 스펙트럼을 사용하여 반전하며, 품질을 개선할 수 있지만 속도가 다소 느려질 수 있습니다.", "启用“测试时增强”, 可能会提高质量, 但速度稍慢": "\"테스트 시 향상\"을 활성화하면 품질을 개선할 수 있지만 속도가 다소 느려질 수 있습니다.", "将输出音频缺失的频率范围镜像输出, 作用不大": "출력 오디오에서 누락된 주파수 범위를 미러링하여 출력하지만 효과는 미미합니다.", "识别人声输出中残留的人工痕迹, 可改善某些歌曲的分离效果": "출력에서 사람의 목소리에서 남은 인위적인 흔적을 인식하여 일부 노래의 분리 효과를 개선할 수 있습니다.", "正在启动WebUI, 请稍等...": "WebUI를 시작 중입니다, 잠시만 기다려 주십시오...", "若启动失败, 请尝试以管理员身份运行此程序": "시작에 실패할 경우, 이 프로그램을 관리자 권한으로 실행해 보십시오.", "WebUI运行过程中请勿关闭此窗口!": "WebUI가 실행되는 동안 이 창을 닫지 마십시오!", "检测到CUDA, 设备信息: ": "CUDA가 감지되었습니다, 장치 정보: ", "使用MPS": "MPS 사용", "检测到MPS, 使用MPS": "MPS가 감지되어 MPS를 사용합니다.", "无可用的加速设备, 使用CPU": "사용 가능한 가속 장치가 없으므로 CPU를 사용합니다.", "\\033[33m未检测到可用的加速设备, 使用CPU\\033[0m": "\\033[33m사용 가능한 가속 장치를 감지하지 못했습니다, CPU를 사용합니다\\033[0m", "\\033[33m如果你使用的是NVIDIA显卡, 请更新显卡驱动至最新版后重试\\033[0m": "\\033[33mNVIDIA 그래픽 카드를 사용하는 경우, 그래픽 드라이버를 최신 버전으로 업데이트한 후 다시 시도하십시오\\033[0m", "模型下载失败, 请重试!": "모델 다운로드에 실패했습니다, 다시 시도하십시오!", "作者: [Github@KitsuneX07](https://github.com/KitsuneX07) | [Github@SUC-DriverOld](https://github.com/SUC-DriverOld), [点击前往教程文档](https://r1kc63iz15l.feishu.cn/wiki/JSp3wk7zuinvIXkIqSUcCXY1nKc)": "저자: [Github@KitsuneX07](https://github.com/KitsuneX07) | [Github@SUC-DriverOld](https://github.com/SUC-DriverOld), [튜토리얼 문서로 이동하기](https://r1kc63iz15l.feishu.cn/wiki/JSp3wk7zuinvIXkIqSUcCXY1nKc)", "**请将需要处理的音频放置到input文件夹内, 处理完成后的音频将会保存到results文件夹内! 云端输入输出目录不可更改!**": "**처리할 오디오를 input 폴더에 넣으십시오, 처리 완료된 오디오는 results 폴더에 저장됩니다! 클라우드 입력 및 출력 디렉토리는 변경할 수 없습니다!**", "文件管理": "파일 관리", "文件管理页面是云端WebUI特有的页面, 用于上传, 下载, 删除文件。<br>1. 上传文件: 将文件上传到input文件夹内。可以勾选是否自动解压zip文件<br>2. 下载文件: 以zip格式打包results文件夹内的文件, 输出至WebUI以供下载。注意: 打包不会删除results文件夹, 若打包后不再需要分离结果, 请点击按钮手动删除。<br>3. 删除文件: 删除input和results文件夹内的文件。": "파일 관리 페이지는 클라우드 WebUI 고유의 페이지로 파일 업로드, 다운로드, 삭제를 위해 사용됩니다.<br>1. 파일 업로드: 파일을 입력 폴더로 업로드합니다. zip 파일 자동 해제 여부를 선택할 수 있습니다.<br>2. 파일 다운로드: 결과 폴더 내의 파일을 zip 형식으로 압축하여 WebUI로 출력하여 다운로드할 수 있습니다. 주의: 압축은 결과 폴더를 삭제하지 않으며, 압축 후 결과를 더 이상 분리할 필요가 없다면 버튼을 클릭하여 수동으로 삭제해야 합니다.<br>3. 파일 삭제: 입력 및 결과 폴더 내의 파일을 삭제합니다.", "删除input文件夹内所有文件": "입력 폴더 내 모든 파일 삭제", "删除results文件夹内所有文件": "결과 폴더 내 모든 파일 삭제", "刷新input和results文件列表": "입력 및 결과 파일 목록 새로 고침", "打包results文件夹内所有文件": "결과 폴더 내 모든 파일을 압축", "上传一个或多个文件至input文件夹": "입력 폴더로 하나 이상의 파일 업로드", "自动解压zip文件(仅支持zip, 压缩包内文件名若含有非ASCII字符, 解压后文件名可能为乱码)": "zip 파일 자동 해제(오직 zip만 지원, 압축 파일 내 파일 이름에 비ASCII 문자가 포함되어 있으면 해제 후 파일 이름이 깨질 수 있음)", "上传文件": "파일 업로드", "input和results文件列表": "입력 및 결과 파일 목록", "请先点击刷新按钮": "먼저 새로 고침 버튼을 클릭하세요", "下载results文件夹内所有文件": "결과 폴더 내 모든 파일 다운로드", "Window Size: 窗口大小, 用于平衡速度和质量": "Window Size: 속도와 품질의 균형을 맞추기 위해 사용됩니다.", "初始模型: 继续训练或微调模型训练时, 请选择初始模型, 否则将从头开始训练! ": "초기 모델: 훈련을 계속하거나 미세 조정하려는 경우, 초기 모델을 선택하십시오. 그렇지 않으면 처음부터 훈련이 시작됩니다!", "点击合并音频按钮后, 将自动把输入文件夹中的所有音频文件合并为一整个音频文件<br>目前支持的格式包括 .mp3, .flac, .wav, .ogg, m4a 这五种<br>合并后的音频会保存至输出目录中, 文件名为merged_audio_<文件夹名字>.wav": "'오디오 병합' 버튼을 클릭하면 입력 폴더의 모든 오디오 파일을 하나의 전체 오디오 파일로 자동 병합합니다.<br>현재 지원되는 형식은 .mp3, .flac, .wav, .ogg, m4a 등 다섯 가지입니다.<br>병합된 오디오 파일은 출력 디렉터리에 저장되며, 파일 이름은 merged_audio_<폴더 이름>.wav입니다."}