audio:
  chunk_size: 352800
  dim_f: 1024
  dim_t: 801
  hop_length: 441
  min_mean_abs: 0.0
  n_fft: 2048
  num_channels: 2
  sample_rate: 44100
augmentations:
  enable: true
  loudness: true
  loudness_max: 1.5
  loudness_min: 0.5
  mixup: false
  mixup_loudness_max: 1.5
  mixup_loudness_min: 0.5
  mixup_probs: !!python/tuple
  - 0.2
  - 0.02
inference:
  batch_size: 1
  dim_t: 801
  num_overlap: 4
model:
  attn_dropout: 0.1
  depth: 8
  dim: 256
  dim_freqs_in: 1025
  dim_head: 64
  ff_dropout: 0.1
  flash_attn: true
  freq_transformer_depth: 1
  heads: 8
  linear_transformer_depth: 0
  mask_estimator_depth: 2
  multi_stft_hop_size: 147
  multi_stft_normalized: false
  multi_stft_resolution_loss_weight: 1.0
  multi_stft_resolutions_window_sizes: !!python/tuple
  - 4096
  - 2048
  - 1024
  - 512
  - 256
  num_bands: 60
  num_stems: 2
  sample_rate: 44100
  stereo: true
  stft_hop_length: 441
  stft_n_fft: 2048
  stft_normalized: false
  stft_win_length: 2048
  time_transformer_depth: 1
training:
  batch_size: 1
  coarse_loss_clip: true
  ema_momentum: 0.999
  grad_clip: 0
  gradient_accumulation_steps: 8
  instruments:
  - aspiration
  - other
  lr: 4.0e-05
  num_epochs: 1000
  num_steps: 1000
  optimizer: adam
  other_fix: false
  patience: 2
  q: 0.95
  reduce_factor: 0.95
  target_instrument: null
  use_amp: true
