{"仅供个人娱乐和非商业用途, 禁止用于血腥/暴力/性相关/政治相关内容。[点击前往教程文档](https://r1kc63iz15l.feishu.cn/wiki/JSp3wk7zuinvIXkIqSUcCXY1nKc)<br>本整合包完全免费, 严禁以任何形式倒卖, 如果你从任何地方**付费**购买了本整合包, 请**立即退款**。<br> 整合包作者: [bilibili@阿狸不吃隼舞](https://space.bilibili.com/403335715) [Github@KitsuneX07](https://github.com/KitsuneX07) | [Bilibili@Sucial](https://space.bilibili.com/445022409) [Github@SUC-DriverOld](https://github.com/SUC-DriverOld) | Gradio主题: [Gradio Theme](https://huggingface.co/spaces/NoCrypt/miku)": "僅供個人娛樂和非商業用途, 禁止用於血腥/暴力/性相關/政治相關內容。[點擊前往教程文檔](https://r1kc63iz15l.feishu.cn/wiki/JSp3wk7zuinvIXkIqSUcCXY1nKc)<br>本整合包完全免費, 嚴禁以任何形式倒賣, 如果你從任何地方**付費**購買了本整合包, 請**立即退款**。<br> 整合包作者: [bilibili@阿狸不喫隼舞](https://space.bilibili.com/403335715) [Github@KitsuneX07](https://github.com/KitsuneX07) | [Bilibili@Sucial](https://space.bilibili.com/445022409) [Github@SUC-DriverOld](https://github.com/SUC-DriverOld) | Gradio主題: [Gradio Theme](https://huggingface.co/spaces/NoCrypt/miku)", "MSST分离": "MSST分離", "UVR分离": "UVR分離", "预设流程": "預設流程", "合奏模式": "合奏模式", "小工具": "小工具", "安装模型": "安裝模型", "MSST训练": "MSST訓練", "设置": "設置", "输出音轨": "輸出音軌", "请至少添加2个模型到合奏流程": "請至少添加2個模型到合奏流程", "合奏流程已保存": "合奏流程已保存", "请上传至少一个音频文件!": "請上傳至少一個音頻文件!", "请先创建合奏流程": "請先創建合奏流程", "模型": "模型", "不存在": "不存在", "用户强制终止": "用戶強制終止", "处理失败: ": "處理失敗: ", "处理完成, 成功: ": "處理完成, 成功: ", "个文件, 失败: ": "個文件, 失敗: ", "个文件": "個文件", ", 结果已保存至: ": ", 結果已保存至: ", ", 耗时: ": ", 耗時: ", "请上传至少2个文件": "請上傳至少2個文件", "上传的文件数目与权重数目不匹配": "上傳的文件數目與權重數目不匹配", "处理完成, 文件已保存为: ": "處理完成, 文件已保存爲: ", "处理失败!": "處理失敗!", "input文件夹内文件列表:\\n": "input文件夾內文件列表:\\n", "input文件夹为空\\n": "input文件夾爲空\\n", "results文件夹内文件列表:\\n": "results文件夾內文件列表:\\n", "results文件夹为空\\n": "results文件夾爲空\\n", "已删除input文件夹内所有文件": "已刪除input文件夾內所有文件", "已删除results文件夹内所有文件": "已刪除results文件夾內所有文件", "请先选择模型": "請先選擇模型", "仅输出主音轨": "僅輸出主音軌", "仅输出次音轨": "僅輸出次音軌", "已打开下载管理器": "已打開下載管理器", "选择模型": "選擇模型", "模型名字": "模型名字", "模型类型": "模型類型", "主要提取音轨": "主要提取音軌", "次要提取音轨": "次要提取音軌", "下载链接": "下載鏈接", "模型类别": "模型類別", "可提取音轨": "可提取音軌", "模型大小": "模型大小", "模型已安装": "模型已安裝", "无法校验sha256": "無法校驗sha256", "sha256校验失败": "sha256校驗失敗", "模型sha256校验失败, 请重新下载": "模型sha256校驗失敗, 請重新下載", "sha256校验成功": "sha256校驗成功", "模型未安装": "模型未安裝", "请选择模型类型": "請選擇模型類型", "已安装": "已安裝", "请手动删除后重新下载": "請手動刪除後重新下載", "下载成功": "下載成功", "下载失败": "下載失敗", "已安装。请勿重复安装。": "已安裝。請勿重複安裝。", "已打开": "已打開", "的下载链接": "的下載鏈接", "上传参数文件": "上傳參數文件", "上传参数": "上傳參數", "请上传'.yaml'格式的配置文件": "請上傳'.yaml'格式的配置文件", "请上传'ckpt', 'chpt', 'th'格式的模型文件": "請上傳'ckpt', 'chpt', 'th'格式的模型文件", "请输入正确的模型类别和模型类型": "請輸入正確的模型類別和模型類型", "安装成功。重启WebUI以刷新模型列表": "安裝成功。重啓WebUI以刷新模型列表", "安装失败": "安裝失敗", "请输入选择模型参数": "請輸入選擇模型參數", "请上传'.json'格式的参数文件": "請上傳'.json'格式的參數文件", "请上传'.pth'格式的模型文件": "請上傳'.pth'格式的模型文件", "请输入正确的音轨名称": "請輸入正確的音軌名稱", "配置保存成功!": "配置保存成功!", "非官方模型不支持重置配置!": "非官方模型不支持重置配置!", "配置重置成功!": "配置重置成功!", "备份文件不存在!": "備份文件不存在!", "选择输出音轨": "選擇輸出音軌", "请选择模型": "請選擇模型", "请选择输入目录": "請選擇輸入目錄", "请选择输出目录": "請選擇輸出目錄", "请选择GPU": "請選擇GPU", "处理完成, 结果已保存至: ": "處理完成, 結果已保存至: ", "暂无备份文件": "暫無備份文件", "作为下一模型输入(或结果输出)的音轨": "作爲下一模型輸入(或結果輸出)的音軌", "直接保存至输出目录的音轨(可多选)": "直接保存至輸出目錄的音軌(可多選)", "不输出": "不輸出", "请填写预设名称": "請填寫預設名稱", "预设": "預設", "保存成功": "保存成功", "请选择预设": "請選擇預設", "不支持的预设版本: ": "不支持的預設版本: ", ", 请重新制作预设。": ", 請重新制作預設。", "预设版本不支持": "預設版本不支持", "预设不存在": "預設不存在", "删除成功": "刪除成功", "预设已删除": "預設已刪除", "选择需要恢复的预设流程备份": "選擇需要恢復的預設流程備份", "已成功恢复备份": "已成功恢復備份", "设置重置成功, 请重启WebUI刷新! ": "設置重置成功, 請重啓WebUI刷新! ", "记录重置成功, 请重启WebUI刷新! ": "記錄重置成功, 請重啓WebUI刷新! ", "请选择正确的模型目录": "請選擇正確的模型目錄", "设置保存成功! 请重启WebUI以应用。": "設置保存成功! 請重啓WebUI以應用。", "当前版本: ": "當前版本: ", ", 发现新版本: ": ", 發現新版本: ", ", 已是最新版本": ", 已是最新版本", "检查更新失败": "檢查更新失敗", "语言已更改, 重启WebUI生效": "語言已更改, 重啓WebUI生效", "成功将端口设置为": "成功將端口設置爲", ", 重启WebUI生效": ", 重啓WebUI生效", "huggingface.co (需要魔法)": "huggingface.co (需要魔法)", "hf-mirror.com (镜像站可直连)": "hf-mirror.com (鏡像站可直連)", "下载链接已更改": "下載鏈接已更改", "公共链接已开启, 重启WebUI生效": "公共鏈接已開啓, 重啓WebUI生效", "公共链接已关闭, 重启WebUI生效": "公共鏈接已關閉, 重啓WebUI生效", "已开启局域网分享, 重启WebUI生效": "已開啓局域網分享, 重啓WebUI生效", "已关闭局域网分享, 重启WebUI生效": "已關閉局域網分享, 重啓WebUI生效", "已开启自动清理缓存": "已開啓自動清理緩存", "已关闭自动清理缓存": "已關閉自動清理緩存", "已开启调试模式": "已開啓調試模式", "已关闭调试模式": "已關閉調試模式", "主题已更改, 重启WebUI生效": "主題已更改, 重啓WebUI生效", "音频设置已保存": "音頻設置已保存", "已重命名为": "已重命名爲", "选择模型类型": "選擇模型類型", "请先选择模型类型": "請先選擇模型類型", "新模型名称后缀错误!": "新模型名稱後綴錯誤!", "模型名字已存在! 请重新命名!": "模型名字已存在! 請重新命名!", "重命名失败!": "重命名失敗!", "检测到": "檢測到", "旧版配置, 正在更新至最新版": "舊版配置, 正在更新至最新版", "成功清理Gradio缓存": "成功清理Gradio緩存", "请上传至少一个文件": "請上傳至少一個文件", "单声道": "單聲道", "处理完成, 成功转换: ": "處理完成, 成功轉換: ", "请先下载SOME预处理模型并放置在tools/SOME_weights文件夹下! ": "請先下載SOME預處理模型並放置在tools/SOME_weights文件夾下! ", "请先选择模型保存路径! ": "請先選擇模型保存路徑! ", "初始模型": "初始模型", "模型类型错误, 请重新选择": "模型類型錯誤, 請重新選擇", "配置文件不存在, 请重新选择": "配置文件不存在, 請重新選擇", "数据集路径不存在, 请重新选择": "數據集路徑不存在, 請重新選擇", "验证集路径不存在, 请重新选择": "驗證集路徑不存在, 請重新選擇", "数据集类型错误, 请重新选择": "數據集類型錯誤, 請重新選擇", "训练启动成功! 请前往控制台查看训练信息! ": "訓練啓動成功! 請前往控制檯查看訓練信息! ", "模型不存在, 请重新选择": "模型不存在, 請重新選擇", "验证完成! 请打开输出文件夹查看详细结果": "驗證完成! 請打開輸出文件夾查看詳細結果", "错误: 无法找到增强配置文件模板, 请检查文件configs/augmentations_template.yaml是否存在。": "錯誤: 無法找到增強配置文件模板, 請檢查文件configs/augmentations_template.yaml是否存在。", "已开启调试日志": "已開啓調試日誌", "已关闭调试日志": "已關閉調試日誌", "模型不存在!": "模型不存在!", "输入音频分离": "輸入音頻分離", "输入文件夹分离": "輸入文件夾分離", "请先选择文件夹!": "請先選擇文件夾!", "显存不足, 请尝试减小batchsize值和chunksize值后重试。": "顯存不足, 請嘗試減小batchsize值和chunksize值後重試。", "内存不足，请尝试增大虚拟内存后重试。若分离时出现此报错，也可尝试将推理音频裁切短一些，分段分离。": "內存不足，請嘗試增大虛擬內存後重試。若分離時出現此報錯，也可嘗試將推理音頻裁切短一些，分段分離。", "FFmpeg未找到，请检查FFmpeg是否正确安装。若使用的是整合包，请重新安装。": "FFmpeg未找到，請檢查FFmpeg是否正確安裝。若使用的是整合包，請重新安裝。", "模型损坏，请重新下载并安装模型后重试。": "模型損壞，請重新下載並安裝模型後重試。", "文件或路径不存在，请根据错误指示检查是否存在该文件。": "文件或路徑不存在，請根據錯誤指示檢查是否存在該文件。", "合奏模式可用于集成不同算法的结果, 具体的文档位于/docs/ensemble.md。目前主要有以下两种合奏方式:<br>1. 从原始音频合奏: 直接上传一个或多个音频文件, 然后选择多个模型进行处理, 将这些处理结果根据选择的合奏模式进行合奏<br>2. 从分离结果合奏: 上传多个已经分离完成的结果音频, 然后选择合奏模式进行合奏": "合奏模式可用於集成不同算法的結果, 具體的文檔位於/docs/ensemble.md。目前主要有以下兩種合奏方式:<br>1. 從原始音頻合奏: 直接上傳一個或多個音頻文件, 然後選擇多個模型進行處理, 將這些處理結果根據選擇的合奏模式進行合奏<br>2. 從分離結果合奏: 上傳多個已經分離完成的結果音頻, 然後選擇合奏模式進行合奏", "从原始音频合奏": "從原始音頻合奏", "从原始音频合奏需要上传至少一个音频文件, 然后选择多个模型先进行分离处理, 然后将这些处理结果根据选择的合奏模式进行合奏。<br>注意, 请确保你的磁盘空间充足, 合奏过程会产生的临时文件仅会在处理结束后删除。": "從原始音頻合奏需要上傳至少一個音頻文件, 然後選擇多個模型先進行分離處理, 然後將這些處理結果根據選擇的合奏模式進行合奏。<br>注意, 請確保你的磁盤空間充足, 合奏過程會產生的臨時文件僅會在處理結束後刪除。", "制作合奏流程": "製作合奏流程", "权重": "權重", "添加到合奏流程": "添加到合奏流程", "撤销上一步": "撤銷上一步", "全部清空": "全部清空", "合奏流程": "合奏流程", "保存此合奏流程": "保存此合奏流程", "集成模式": "集成模式", "输出格式": "輸出格式", "使用CPU (注意: 使用CPU会导致速度非常慢) ": "使用CPU (注意: 使用CPU會導致速度非常慢) ", "使用TTA (测试时增强), 可能会提高质量, 但时间x3": "使用TTA (測試時增強), 可能會提高質量, 但時間x3", "输出次级音轨 (例如: 合奏人声时, 同时输出伴奏)": "輸出次級音軌 (例如: 合奏人聲時, 同時輸出伴奏)", "输入音频": "輸入音頻", "上传一个或多个音频文件": "上傳一個或多個音頻文件", "输入文件夹": "輸入文件夾", "输入目录": "輸入目錄", "选择文件夹": "選擇文件夾", "打开文件夹": "打開文件夾", "输出目录": "輸出目錄", "从分离结果合奏": "從分離結果合奏", "从分离结果合奏需要上传至少两个音频文件, 这些音频文件是使用不同的模型分离同一段音频的结果。因此, 上传的所有音频长度应该相同。": "從分離結果合奏需要上傳至少兩個音頻文件, 這些音頻文件是使用不同的模型分離同一段音頻的結果。因此, 上傳的所有音頻長度應該相同。", "上传多个音频文件": "上傳多個音頻文件", "权重(以空格分隔, 数量要与上传的音频一致)": "權重(以空格分隔, 數量要與上傳的音頻一致)", "运行": "運行", "强制停止": "強制停止", "### 集成模式": "### 集成模式", "1. `avg_wave`: 在1D变体上进行集成, 独立地找到波形的每个样本的平均值<br>2. `median_wave`: 在1D变体上进行集成, 独立地找到波形的每个样本的中位数<br>3. `min_wave`: 在1D变体上进行集成, 独立地找到波形的每个样本的最小绝对值<br>4. `max_wave`: 在1D变体上进行集成, 独立地找到波形的每个样本的最大绝对值<br>5. `avg_fft`: 在频谱图 (短时傅里叶变换 (STFT) 2D变体) 上进行集成, 独立地找到频谱图的每个像素的平均值。平均后使用逆STFT得到原始的1D波形<br>6. `median_fft`: 与avg_fft相同, 但使用中位数代替平均值 (仅在集成3个或更多来源时有用) <br>7. `min_fft`: 与avg_fft相同, 但使用最小函数代替平均值 (减少激进程度) <br>8. `max_fft`: 与avg_fft相同, 但使用最大函数代替平均值 (增加激进程度) ": "1. `avg_wave`: 在1D變體上進行集成, 獨立地找到波形的每個樣本的平均值<br>2. `median_wave`: 在1D變體上進行集成, 獨立地找到波形的每個樣本的中位數<br>3. `min_wave`: 在1D變體上進行集成, 獨立地找到波形的每個樣本的最小絕對值<br>4. `max_wave`: 在1D變體上進行集成, 獨立地找到波形的每個樣本的最大絕對值<br>5. `avg_fft`: 在頻譜圖 (短時傅里葉變換 (STFT) 2D變體) 上進行集成, 獨立地找到頻譜圖的每個像素的平均值。平均後使用逆STFT得到原始的1D波形<br>6. `median_fft`: 與avg_fft相同, 但使用中位數代替平均值 (僅在集成3個或更多來源時有用) <br>7. `min_fft`: 與avg_fft相同, 但使用最小函數代替平均值 (減少激進程度) <br>8. `max_fft`: 與avg_fft相同, 但使用最大函數代替平均值 (增加激進程度) ", "### 注意事项": "### 注意事項", "1. min_fft可用于进行更保守的合成, 它将减少更激进模型的影响。<br>2. 最好合成等质量的模型。在这种情况下, 它将带来增益。如果其中一个模型质量不好, 它将降低整体质量。<br>3. 在原仓库作者的实验中, 与其他方法相比, avg_wave在SDR分数上总是更好或相等。<br>4. 最终会在输出目录下生成一个`ensemble_<集成模式>.wav`。": "1. min_fft可用於進行更保守的合成, 它將減少更激進模型的影響。<br>2. 最好合成等質量的模型。在這種情況下, 它將帶來增益。如果其中一個模型質量不好, 它將降低整體質量。<br>3. 在原倉庫作者的實驗中, 與其他方法相比, avg_wave在SDR分數上總是更好或相等。<br>4. 最終會在輸出目錄下生成一個`ensemble_<集成模式>.wav`。", "下载官方模型": "下載官方模型", "点击打开下载管理器": "點擊打開下載管理器", "模型信息": "模型信息", "打开模型目录": "打開模型目錄", "自动下载": "自動下載", "手动下载": "手動下載", "1. MSST模型默认下载在pretrain/<模型类型>文件夹下。UVR模型默认下载在设置中的UVR模型目录中。<br>2. 下加载进度可以打开终端查看。如果一直卡着不动或者速度很慢, 在确信网络正常的情况下请尝试重启WebUI。<br>3. 若下载失败, 会在模型目录**留下一个损坏的模型**, 请**务必**打开模型目录手动删除! <br>4. 点击“重启WebUI”按钮后, 会短暂性的失去连接, 随后会自动开启一个新网页。": "1. MSST模型默認下載在pretrain/<模型類型>文件夾下。UVR模型默認下載在設置中的UVR模型目錄中。<br>2. 下加載進度可以打開終端查看。如果一直卡着不動或者速度很慢, 在確信網絡正常的情況下請嘗試重啓WebUI。<br>3. 若下載失敗, 會在模型目錄**留下一個損壞的模型**, 請**務必**打開模型目錄手動刪除! <br>4. 點擊“重啓WebUI”按鈕後, 會短暫性的失去連接, 隨後會自動開啓一個新網頁。", "### 模型下载链接": "### 模型下載鏈接", "1. 自动从Github, Huggingface或镜像站下载模型。<br>2. 你也可以在此整合包下载链接中的All_Models文件夹中找到所有可用的模型并下载。": "1. 自動從G<PERSON><PERSON>, Huggingface或鏡像站下載模型。<br>2. 你也可以在此整合包下載鏈接中的All_Models文件夾中找到所有可用的模型並下載。", "若自动下载出现报错或下载过慢, 请点击手动下载, 跳转至下载链接。手动下载完成后, 请根据你选择的模型类型放置到对应文件夹内。": "若自動下載出現報錯或下載過慢, 請點擊手動下載, 跳轉至下載鏈接。手動下載完成後, 請根據你選擇的模型類型放置到對應文件夾內。", "### 当前UVR模型目录: ": "### 當前UVR模型目錄: ", ", 如需更改, 请前往设置页面。": ", 如需更改, 請前往設置頁面。", "### 模型安装完成后, 需重启WebUI刷新模型列表": "### 模型安裝完成後, 需重啓WebUI刷新模型列表", "重启WebUI": "重啓WebUI", "安装非官方MSST模型": "安裝非官方MSST模型", "你可以从其他途径获取非官方MSST模型, 在此页面完成配置文件设置后, 即可正常使用。<br>注意: 仅支持'.ckpt', '.th', '.chpt'格式的模型。模型显示名字为模型文件名。<br>选择模型类型: 共有三个可选项。依次代表人声相关模型, 多音轨分离模型, 单音轨分离模型。仅用于区分模型大致类型, 可任意选择。<br>选择模型类别: 此选项关系到模型是否能正常推理使用, 必须准确选择!": "你可以從其他途徑獲取非官方MSST模型, 在此頁面完成配置文件設置後, 即可正常使用。<br>注意: 僅支持'.ckpt', '.th', '.chpt'格式的模型。模型顯示名字爲模型文件名。<br>選擇模型類型: 共有三個可選項。依次代表人聲相關模型, 多音軌分離模型, 單音軌分離模型。僅用於區分模型大致類型, 可任意選擇。<br>選擇模型類別: 此選項關係到模型是否能正常推理使用, 必須準確選擇!", "上传非官方MSST模型": "上傳非官方MSST模型", "上传非官方MSST模型配置文件": "上傳非官方MSST模型配置文件", "选择模型类别": "選擇模型類別", "模型下载链接 (非必须，若无，可跳过)": "模型下載鏈接 (非必須，若無，可跳過)", "安装非官方VR模型": "安裝非官方VR模型", "你可以从其他途径获取非官方UVR模型, 在此页面完成配置文件设置后, 即可正常使用。<br>注意: 仅支持'.pth'格式的模型。模型显示名字为模型文件名。": "你可以從其他途徑獲取非官方UVR模型, 在此頁面完成配置文件設置後, 即可正常使用。<br>注意: 僅支持'.pth'格式的模型。模型顯示名字爲模型文件名。", "上传非官方VR模型": "上傳非官方VR模型", "主要音轨名称": "主要音軌名稱", "次要音轨名称": "次要音軌名稱", "选择模型参数": "選擇模型參數", "是否为Karaoke模型": "是否爲Karaoke模型", "是否为BV模型": "是否爲BV模型", "是否为VR 5.1模型": "是否爲VR 5.1模型", "MSST音频分离原项目地址: [https://github.com/ZFTurbo/Music-Source-Separation-Training](https://github.com/ZFTurbo/Music-Source-Separation-Training)": "MSST音頻分離原項目地址: [https://github.com/ZFTurbo/Music-Source-Separation-Training](https://github.com/ZFTurbo/Music-Source-Separation-Training)", "选择使用的GPU": "選擇使用的GPU", "强制使用CPU推理, 注意: 使用CPU推理速度非常慢!": "強制使用CPU推理, 注意: 使用CPU推理速度非常慢!", "使用CPU": "使用CPU", "[点击展开] 推理参数设置, 不同模型之间参数相互独立": "[點擊展開] 推理參數設置, 不同模型之間參數相互獨立", "只有在点击保存后才会生效。参数直接写入配置文件, 无法撤销。假如不知道如何设置, 请保持默认值。<br>请牢记自己修改前的参数数值, 防止出现问题以后无法恢复。请确保输入正确的参数, 否则可能会导致模型无法正常运行。<br>假如修改后无法恢复, 请点击``重置``按钮, 这会使得配置文件恢复到默认值。": "只有在點擊保存後纔會生效。參數直接寫入配置文件, 無法撤銷。假如不知道如何設置, 請保持默認值。<br>請牢記自己修改前的參數數值, 防止出現問題以後無法恢復。請確保輸入正確的參數, 否則可能會導致模型無法正常運行。<br>假如修改後無法恢復, 請點擊``重置``按鈕, 這會使得配置文件恢復到默認值。", "批次大小, 减小此值可以降低显存占用, 此参数对推理效果影响不大": "批次大小, 減小此值可以降低顯存佔用, 此參數對推理效果影響不大", "重叠数, 增大此值可以提高分离效果, 但会增加处理时间, 建议设置成4": "重疊數, 增大此值可以提高分離效果, 但會增加處理時間, 建議設置成4", "分块大小, 增大此值可以提高分离效果, 但会增加处理时间和显存占用": "分塊大小, 增大此值可以提高分離效果, 但會增加處理時間和顯存佔用", "音频归一化, 对音频进行归一化输入和输出, 部分模型没有此功能": "音頻歸一化, 對音頻進行歸一化輸入和輸出, 部分模型沒有此功能", "启用TTA, 能小幅提高分离质量, 若使用, 推理时间x3": "啓用TTA, 能小幅提高分離質量, 若使用, 推理時間x3", "保存配置": "保存配置", "重置配置": "重置配置", "预设流程允许按照预设的顺序运行多个模型。每一个模型的输出将作为下一个模型的输入。": "預設流程允許按照預設的順序運行多個模型。每一個模型的輸出將作爲下一個模型的輸入。", "使用预设": "使用預設", "该模式下的UVR推理参数将直接沿用UVR分离页面的推理参数, 如需修改请前往UVR分离页面。<br>修改完成后, 还需要任意处理一首歌才能保存参数! ": "該模式下的UVR推理參數將直接沿用UVR分離頁面的推理參數, 如需修改請前往UVR分離頁面。<br>修改完成後, 還需要任意處理一首歌才能保存參數! ", "将次级输出保存至输出目录的单独文件夹内": "將次級輸出保存至輸出目錄的單獨文件夾內", "制作预设": "製作預設", "预设名称": "預設名稱", "请输入预设名称": "請輸入預設名稱", "添加至流程": "添加至流程", "保存上述预设流程": "保存上述預設流程", "管理预设": "管理預設", "此页面提供查看预设, 删除预设, 备份预设, 恢复预设等功能<br>`model_type`: 模型类型；`model_name`: 模型名称；`input_to_next`: 作为下一模型输入的音轨；`output_to_storage`: 直接保存至输出目录下的direct_output文件夹内的音轨, **不会经过后续流程处理**<br>每次点击删除预设按钮时, 将自动备份预设以免误操作。": "此頁面提供查看預設, 刪除預設, 備份預設, 恢復預設等功能<br>`model_type`: 模型類型；`model_name`: 模型名稱；`input_to_next`: 作爲下一模型輸入的音軌；`output_to_storage`: 直接保存至輸出目錄下的direct_output文件夾內的音軌, **不會經過後續流程處理**<br>每次點擊刪除預設按鈕時, 將自動備份預設以免誤操作。", "删除所选预设": "刪除所選預設", "请先选择预设": "請先選擇預設", "恢复所选预设": "恢復所選預設", "打开备份文件夹": "打開備份文件夾", "WebUI设置": "WebUI設置", "GPU信息": "GPU信息", "系统信息": "系統信息", "设置WebUI端口, 0为自动": "設置WebUI端口, 0爲自動", "选择语言": "選擇語言", "选择MSST模型下载链接": "選擇MSST模型下載鏈接", "选择WebUI主题": "選擇WebUI主題", "对本地局域网开放WebUI: 开启后, 同一局域网内的设备可通过'本机IP:端口'的方式访问WebUI。": "對本地局域網開放WebUI: 開啓後, 同一局域網內的設備可通過'本機IP:端口'的方式訪問WebUI。", "开启公共链接: 开启后, 他人可通过公共链接访问WebUI。链接有效时长为72小时。": "開啓公共鏈接: 開啓後, 他人可通過公共鏈接訪問WebUI。鏈接有效時長爲72小時。", "自动清理缓存: 开启后, 每次启动WebUI时会自动清理缓存。": "自動清理緩存: 開啓後, 每次啓動WebUI時會自動清理緩存。", "全局调试模式: 向开发者反馈问题时请开启。(该选项支持热切换)": "全局調試模式: 向開發者反饋問題時請開啓。(該選項支持熱切換)", "选择UVR模型目录": "選擇UVR模型目錄", "检查更新": "檢查更新", ", 请点击检查更新按钮": ", 請點擊檢查更新按鈕", "前往Github瞅一眼": "前往Github瞅一眼", "重置WebUI路径记录": "重置WebUI路徑記錄", "重置WebUI设置": "重置WebUI設置", "### 选择UVR模型目录": "### 選擇UVR模型目錄", "如果你的电脑中有安装UVR5, 你不必重新下载一遍UVR5模型, 只需在下方“选择UVR模型目录”中选择你的UVR5模型目录, 定位到models/VR_Models文件夹。<br>例如: E:/Program Files/Ultimate Vocal Remover/models/VR_Models 点击保存设置或重置设置后, 需要重启WebUI以更新。": "如果你的電腦中有安裝UVR5, 你不必重新下載一遍UVR5模型, 只需在下方“選擇UVR模型目錄”中選擇你的UVR5模型目錄, 定位到models/VR_Models文件夾。<br>例如: E:/Program Files/Ultimate Vocal Remover/models/VR_Models 點擊保存設置或重置設置後, 需要重啓WebUI以更新。", "### 检查更新": "### 檢查更新", "从Github检查更新, 需要一定的网络要求。点击检查更新按钮后, 会自动检查是否有最新版本。你可以前往此整合包的下载链接或访问Github仓库下载最新版本。": "從Github檢查更新, 需要一定的網絡要求。點擊檢查更新按鈕後, 會自動檢查是否有最新版本。你可以前往此整合包的下載鏈接或訪問Github倉庫下載最新版本。", "### 重置WebUI路径记录": "### 重置WebUI路徑記錄", "将所有输入输出目录重置为默认路径, 预设/模型/配置文件以及上面的设置等**不会重置**, 无需担心。重置WebUI设置后, 需要重启WebUI。": "將所有輸入輸出目錄重置爲默認路徑, 預設/模型/配置文件以及上面的設置等**不會重置**, 無需擔心。重置WebUI設置後, 需要重啓WebUI。", "### 重置WebUI设置": "### 重置WebUI設置", "仅重置WebUI设置, 例如UVR模型路径, WebUI端口等。重置WebUI设置后, 需要重启WebUI。": "僅重置WebUI設置, 例如UVR模型路徑, WebUI端口等。重置WebUI設置後, 需要重啓WebUI。", "### 重启WebUI": "### 重啓WebUI", "点击 “重启WebUI” 按钮后, 会短暂性的失去连接, 随后会自动开启一个新网页。": "點擊 “重啓WebUI” 按鈕後, 會短暫性的失去連接, 隨後會自動開啓一個新網頁。", "音频输出设置": "音頻輸出設置", "此页面支持用户自定义修改MSST/VR推理后输出音频的质量。输出音频的**采样率, 声道数与模型支持的参数有关, 无法更改**。<br>修改完成后点击保存设置即可生效。": "此頁面支持用戶自定義修改MSST/VR推理後輸出音頻的質量。輸出音頻的**採樣率, 聲道數與模型支持的參數有關, 無法更改**。<br>修改完成後點擊保存設置即可生效。", "输出wav位深度": "輸出wav位深度", "输出flac位深度": "輸出flac位深度", "输出mp3比特率(bps)": "輸出mp3比特率(bps)", "保存设置": "保存設置", "模型改名": "模型改名", "此页面支持用户自定义修改模型名字, 以便记忆和使用。修改完成后, 需要重启WebUI以刷新模型列表。<br>【注意】此操作不可逆 (无法恢复至默认命名), 请谨慎命名。输入新模型名字时, 需保留后缀!": "此頁面支持用戶自定義修改模型名字, 以便記憶和使用。修改完成後, 需要重啓WebUI以刷新模型列表。<br>【注意】此操作不可逆 (無法恢復至默認命名), 請謹慎命名。輸入新模型名字時, 需保留後綴!", "新模型名": "新模型名", "请输入新模型名字, 需保留后缀!": "請輸入新模型名字, 需保留後綴!", "确认修改": "確認修改", "立体声": "立體聲", "音频格式转换": "音頻格式轉換", "上传一个或多个音频文件并将其转换为指定格式。<br>支持的格式包括 .mp3, .flac, .wav, .ogg, .m4a, .wma, .aac...等等。<br>**不支持**网易云音乐/QQ音乐等加密格式, 如.ncm, .qmc等。": "上傳一個或多個音頻文件並將其轉換爲指定格式。<br>支持的格式包括 .mp3, .flac, .wav, .ogg, .m4a, .wma, .aac...等等。<br>**不支持**網易雲音樂/QQ音樂等加密格式, 如.ncm, .qmc等。", "选择或输入音频输出格式": "選擇或輸入音頻輸出格式", "选择音频输出目录": "選擇音頻輸出目錄", "输出音频采样率(Hz)": "輸出音頻採樣率(Hz)", "输出音频声道数": "輸出音頻聲道數", "输出ogg比特率(bps)": "輸出ogg比特率(bps)", "转换音频": "轉換音頻", "合并音频": "合併音頻", "点击合并音频按钮后, 将自动把输入文件夹中的所有音频文件合并为一整个音频文件<br>合并后的音频会保存至输出目录中, 文件名为merged_audio_<文件夹名字>.wav": "點擊合併音頻按鈕後, 將自動把輸入文件夾中的所有音頻文件合併爲一整個音頻文件<br>合併後的音頻會保存至輸出目錄中, 文件名爲merged_audio_<文件夾名字>.wav", "计算SDR": "計算SDR", "上传两个**wav音频文件**并计算它们的[SDR](https://www.aicrowd.com/challenges/music-demixing-challenge-ismir-2021#evaluation-metric)。<br>SDR是一个用于评估模型质量的数值。数值越大, 模型算法结果越好。": "上傳兩個**wav音頻文件**並計算它們的[SDR](https://www.aicrowd.com/challenges/music-demixing-challenge-ismir-2021#evaluation-metric)。<br>SDR是一個用於評估模型質量的數值。數值越大, 模型算法結果越好。", "参考音频": "參考音頻", "待估音频": "待估音頻", "歌声转MIDI": "歌聲轉MIDI", "歌声转MIDI功能使用开源项目[SOME](https://github.com/openvpi/SOME/), 可以将分离得到的**干净的歌声**转换成.mid文件。<br>【必须】若想要使用此功能, 请先下载权重文件[model_steps_64000_simplified.ckpt](https://hf-mirror.com/Sucial/MSST-WebUI/resolve/main/SOME_weights/model_steps_64000_simplified.ckpt)并将其放置在程序目录下的`tools/SOME_weights`文件夹内。文件命名不可随意更改!": "歌聲轉MIDI功能使用開源項目[SOME](https://github.com/openvpi/SOME/), 可以將分離得到的**乾淨的歌聲**轉換成.mid文件。<br>【必須】若想要使用此功能, 請先下載權重文件[model_steps_64000_simplified.ckpt](https://hf-mirror.com/Sucial/MSST-WebUI/resolve/main/SOME_weights/model_steps_64000_simplified.ckpt)並將其放置在程序目錄下的`tools/SOME_weights`文件夾內。文件命名不可隨意更改!", "如果不知道如何测量歌曲BPM, 可以尝试这两个在线测量工具: [bpmdetector](https://bpmdetector.kniffen.dev/) | [key-bpm-finder](https://vocalremover.org/zh/key-bpm-finder), 测量时建议上传原曲或伴奏, 若干声可能导致测量结果不准确。": "如果不知道如何測量歌曲BPM, 可以嘗試這兩個在線測量工具: [bpmdetector](https://bpmdetector.kniffen.dev/) | [key-bpm-finder](https://vocalremover.org/zh/key-bpm-finder), 測量時建議上傳原曲或伴奏, 若干聲可能導致測量結果不準確。", "上传音频": "上傳音頻", "输入音频BPM": "輸入音頻BPM", "开始转换": "開始轉換", "1. 音频BPM (每分钟节拍数) 可以通过MixMeister BPM Analyzer等软件测量获取。<br>2. 为保证MIDI提取质量, 音频文件请采用干净清晰无混响底噪人声。<br>3. 输出MIDI不带歌词信息, 需要用户自行添加歌词。<br>4. 实际使用体验中部分音符会出现断开的现象, 需自行修正。SOME的模型主要面向DiffSinger唱法模型自动标注, 比正常用户在创作中需要的MIDI更加精细, 因而可能导致模型倾向于对音符进行切分。<br>5. 提取的MIDI没有量化/没有对齐节拍/不适配BPM, 需自行到各编辑器中手动调整。": "1. 音頻BPM (每分鐘節拍數) 可以通過MixMeister BPM Analyzer等軟件測量獲取。<br>2. 爲保證MIDI提取質量, 音頻文件請採用乾淨清晰無混響底噪人聲。<br>3. 輸出MIDI不帶歌詞信息, 需要用戶自行添加歌詞。<br>4. 實際使用體驗中部分音符會出現斷開的現象, 需自行修正。SOME的模型主要面向DiffSinger唱法模型自動標註, 比正常用戶在創作中需要的MIDI更加精細, 因而可能導致模型傾向於對音符進行切分。<br>5. 提取的MIDI沒有量化/沒有對齊節拍/不適配BPM, 需自行到各編輯器中手動調整。", "此页面提供数据集制作教程, 训练参数选择, 以及一键训练。有关配置文件的修改和数据集文件夹的详细说明请参考MSST原项目: [https://github.com/ZFTurbo/Music-Source-Separation-Training](https://github.com/ZFTurbo/Music-Source-Separation-Training)<br>在开始下方的模型训练之前, 请先进行训练数据的制作。<br>说明: 数据集类型即训练集制作Step 1中你选择的类型, 1: Type1; 2: Type2; 3: Type3; 4: Type4, 必须与你的数据集类型相匹配。": "此頁面提供數據集製作教程, 訓練參數選擇, 以及一鍵訓練。有關配置文件的修改和數據集文件夾的詳細說明請參考MSST原項目: [https://github.com/ZFTurbo/Music-Source-Separation-Training](https://github.com/ZFTurbo/Music-Source-Separation-Training)<br>在開始下方的模型訓練之前, 請先進行訓練數據的製作。<br>說明: 數據集類型即訓練集製作Step 1中你選擇的類型, 1: Type1; 2: Type2; 3: Type3; 4: Type4, 必須與你的數據集類型相匹配。", "训练": "訓練", "选择训练模型类型": "選擇訓練模型類型", "配置文件路径": "配置文件路徑", "请输入配置文件路径或选择配置文件": "請輸入配置文件路徑或選擇配置文件", "选择配置文件": "選擇配置文件", "数据集类型": "數據集類型", "数据集路径": "數據集路徑", "请输入或选择数据集文件夹": "請輸入或選擇數據集文件夾", "选择数据集文件夹": "選擇數據集文件夾", "验证集路径": "驗證集路徑", "请输入或选择验证集文件夹": "請輸入或選擇驗證集文件夾", "选择验证集文件夹": "選擇驗證集文件夾", "模型保存路径": "模型保存路徑", "请输入或选择模型保存文件夹": "請輸入或選擇模型保存文件夾", "选择初始模型, 若无初始模型, 留空或选择None即可": "選擇初始模型, 若無初始模型, 留空或選擇None即可", "刷新初始模型列表": "刷新初始模型列表", "训练参数设置": "訓練參數設置", "num_workers: 数据集读取线程数, 0为自动": "num_workers: 數據集讀取線程數, 0爲自動", "随机数种子, 0为随机": "隨機數種子, 0爲隨機", "是否将加载的数据放置在固定内存中, 默认为否": "是否將加載的數據放置在固定內存中, 默認爲否", "是否使用加速训练, 对于多显卡用户会加快训练": "是否使用加速訓練, 對於多顯卡用戶會加快訓練", "是否在训练前验证模型, 默认为否": "是否在訓練前驗證模型, 默認爲否", "是否使用MultiSTFT Loss, 默认为否": "是否使用MultiSTFT Loss, 默認爲否", "是否使用MSE loss, 默认为否": "是否使用MSE loss, 默認爲否", "是否使用L1 loss, 默认为否": "是否使用L1 loss, 默認爲否", "选择输出的评估指标": "選擇輸出的評估指標", "选择调度器使用的评估指标": "選擇調度器使用的評估指標", "保存上述训练配置": "保存上述訓練配置", "开始训练": "開始訓練", "点击开始训练后, 请到终端查看训练进度或报错, 下方不会输出报错信息, 想要停止训练可以直接关闭终端。在训练过程中, 你也可以关闭网页, 仅**保留终端**。": "點擊開始訓練後, 請到終端查看訓練進度或報錯, 下方不會輸出報錯信息, 想要停止訓練可以直接關閉終端。在訓練過程中, 你也可以關閉網頁, 僅**保留終端**。", "验证": "驗證", "此页面用于手动验证模型效果, 测试验证集, 输出SDR测试信息。输出的信息会存放在输出文件夹的results.txt中。<br>下方参数将自动加载训练页面的参数, 在训练页面点击保存训练参数后, 重启WebUI即可自动加载。当然你也可以手动输入参数。<br>": "此頁面用於手動驗證模型效果, 測試驗證集, 輸出SDR測試信息。輸出的信息會存放在輸出文件夾的results.txt中。<br>下方參數將自動加載訓練頁面的參數, 在訓練頁面點擊保存訓練參數後, 重啓WebUI即可自動加載。當然你也可以手動輸入參數。<br>", "模型路径": "模型路徑", "请输入或选择模型文件": "請輸入或選擇模型文件", "选择模型文件": "選擇模型文件", "验证参数设置": "驗證參數設置", "选择验证集音频格式": "選擇驗證集音頻格式", "验证集读取线程数, 0为自动": "驗證集讀取線程數, 0爲自動", "开始验证": "開始驗證", "训练集制作指南": "訓練集製作指南", "Step 1: 数据集制作": "Step 1: 數據集製作", "请**任选下面四种类型之一**制作数据集文件夹, 并按照给出的目录层级放置你的训练数据。完成后, 记录你的数据集**文件夹路径**以及你选择的**数据集类型**, 以便后续使用。": "請**任選下面四種類型之一**製作數據集文件夾, 並按照給出的目錄層級放置你的訓練數據。完成後, 記錄你的數據集**文件夾路徑**以及你選擇的**數據集類型**, 以便後續使用。", "不同的文件夹。每个文件夹包含所需的所有stems, 格式为stem_name.wav。与MUSDBHQ18数据集相同。在最新的代码版本中, 可以使用flac替代wav。<br>例如: ": "不同的文件夾。每個文件夾包含所需的所有stems, 格式爲stem_name.wav。與MUSDBHQ18數據集相同。在最新的代碼版本中, 可以使用flac替代wav。<br>例如: ", "每个文件夹是stem_name。文件夹中包含仅由所需stem组成的wav文件。<br>例如: ": "每個文件夾是stem_name。文件夾中包含僅由所需stem組成的wav文件。<br>例如: ", "可以提供以下结构的CSV文件 (或CSV文件列表) <br>例如: ": "可以提供以下結構的CSV文件 (或CSV文件列表) <br>例如: ", "与类型1相同, 但在训练过程中所有乐器都将来自歌曲的相同位置。<br>例如: ": "與類型1相同, 但在訓練過程中所有樂器都將來自歌曲的相同位置。<br>例如: ", "Step 2: 验证集制作": "Step 2: 驗證集製作", "验证集制作。验证数据集**必须**与上面数据集制作的Type 1(MUSDB)数据集**结构相同** (**无论你使用哪种类型的数据集进行训练**) , 此外每个文件夹还必须包含每首歌的mixture.wav, mixture.wav是所有stem的总和<br>例如: ": "驗證集製作。驗證數據集**必須**與上面數據集製作的Type 1(MUSDB)數據集**結構相同** (**無論你使用哪種類型的數據集進行訓練**) , 此外每個文件夾還必須包含每首歌的mixture.wav, mixture.wav是所有stem的總和<br>例如: ", "Step 3: 选择并修改修改配置文件": "Step 3: 選擇並修改修改配置文件", "请先明确你想要训练的模型类型, 然后选择对应的配置文件进行修改。<br>目前有以下几种模型类型: ": "請先明確你想要訓練的模型類型, 然後選擇對應的配置文件進行修改。<br>目前有以下幾種模型類型: ", "<br>确定好模型类型后, 你可以前往整合包根目录中的configs_backup文件夹下找到对应的配置文件模板。复制一份模板, 然后根据你的需求进行修改。修改完成后记下你的配置文件路径, 以便后续使用。<br>特别说明: config_musdb18_xxx.yaml是针对MUSDB18数据集的配置文件。<br>": "<br>確定好模型類型後, 你可以前往整合包根目錄中的configs_backup文件夾下找到對應的配置文件模板。複製一份模板, 然後根據你的需求進行修改。修改完成後記下你的配置文件路徑, 以便後續使用。<br>特別說明: config_musdb18_xxx.yaml是針對MUSDB18數據集的配置文件。<br>", "打开配置文件模板文件夹": "打開配置文件模板文件夾", "你可以使用下表根据你的GPU选择用于训练的BS_Roformer模型的batch_size参数。表中提供的批量大小值适用于单个GPU。如果你有多个GPU, 则需要将该值乘以GPU的数量。": "你可以使用下表根據你的GPU選擇用於訓練的BS_Roformer模型的batch_size參數。表中提供的批量大小值適用於單個GPU。如果你有多個GPU, 則需要將該值乘以GPU的數量。", "Step 4: 数据增强": "Step 4: 數據增強", "数据增强可以动态更改stem, 通过从旧样本创建新样本来增加数据集的大小。现在, 数据增强的控制在配置文件中进行。下面是一个包含所有可用数据增强的完整配置示例。你可以将其复制到你的配置文件中以使用数据增强。<br>注意:<br>1. 要完全禁用所有数据增强, 可以从配置文件中删除augmentations部分或将enable设置为false。<br>2. 如果要禁用某些数据增强, 只需将其设置为0。<br>3. all部分中的数据增强应用于所有stem。<br>4. vocals/bass等部分中的数据增强仅应用于相应的stem。你可以为training.instruments中给出的所有stem创建这样的部分。": "數據增強可以動態更改stem, 通過從舊樣本創建新樣本來增加數據集的大小。現在, 數據增強的控制在配置文件中進行。下面是一個包含所有可用數據增強的完整配置示例。你可以將其複製到你的配置文件中以使用數據增強。<br>注意:<br>1. 要完全禁用所有數據增強, 可以從配置文件中刪除augmentations部分或將enable設置爲false。<br>2. 如果要禁用某些數據增強, 只需將其設置爲0。<br>3. all部分中的數據增強應用於所有stem。<br>4. vocals/bass等部分中的數據增強僅應用於相應的stem。你可以爲training.instruments中給出的所有stem創建這樣的部分。", "说明: 本整合包仅融合了UVR的VR Architecture模型, MDX23C和HtDemucs类模型可以直接使用前面的MSST音频分离。<br>UVR分离使用项目: [https://github.com/nomadkaraoke/python-audio-separator](https://github.com/nomadkaraoke/python-audio-separator) 并进行了优化。": "說明: 本整合包僅融合了UVR的VR Architecture模型, MDX23C和HtDemucs類模型可以直接使用前面的MSST音頻分離。<br>UVR分離使用項目: [https://github.com/nomadkaraoke/python-audio-separator](https://github.com/nomadkaraoke/python-audio-separator) 並進行了優化。", "Window Size: 窗口大小, 用于平衡速度和质量, 默认为512": "Window Size: 窗口大小, 用於平衡速度和質量, 默認爲512", "Aggression: 主干提取强度, 范围-100-100, 人声请选5": "Aggression: 主幹提取強度, 範圍-100-100, 人聲請選5", "[点击展开] 以下是一些高级设置, 一般保持默认即可": "[點擊展開] 以下是一些高級設置, 一般保持默認即可", "批次大小, 减小此值可以降低显存占用": "批次大小, 減小此值可以降低顯存佔用", "后处理特征阈值, 取值为0.1-0.3, 默认0.2": "後處理特徵閾值, 取值爲0.1-0.3, 默認0.2", "次级输出使用频谱而非波形进行反转, 可能会提高质量, 但速度稍慢": "次級輸出使用頻譜而非波形進行反轉, 可能會提高質量, 但速度稍慢", "启用“测试时增强”, 可能会提高质量, 但速度稍慢": "啓用“測試時增強”, 可能會提高質量, 但速度稍慢", "将输出音频缺失的频率范围镜像输出, 作用不大": "將輸出音頻缺失的頻率範圍鏡像輸出, 作用不大", "识别人声输出中残留的人工痕迹, 可改善某些歌曲的分离效果": "識別人聲輸出中殘留的人工痕跡, 可改善某些歌曲的分離效果", "正在启动WebUI, 请稍等...": "正在啓動WebUI, 請稍等...", "若启动失败, 请尝试以管理员身份运行此程序": "若啓動失敗, 請嘗試以管理員身份運行此程序", "WebUI运行过程中请勿关闭此窗口!": "WebUI運行過程中請勿關閉此窗口!", "检测到CUDA, 设备信息: ": "檢測到CUDA, 設備信息: ", "使用MPS": "使用MPS", "检测到MPS, 使用MPS": "檢測到MPS, 使用MPS", "无可用的加速设备, 使用CPU": "無可用的加速設備, 使用CPU", "\\033[33m未检测到可用的加速设备, 使用CPU\\033[0m": "\\033[33m未檢測到可用的加速設備, 使用CPU\\033[0m", "\\033[33m如果你使用的是NVIDIA显卡, 请更新显卡驱动至最新版后重试\\033[0m": "\\033[33m如果你使用的是NVIDIA顯卡, 請更新顯卡驅動至最新版後重試\\033[0m", "模型下载失败, 请重试!": "模型下載失敗, 請重試!", "作者: [Github@KitsuneX07](https://github.com/KitsuneX07) | [Github@SUC-DriverOld](https://github.com/SUC-DriverOld), [点击前往教程文档](https://r1kc63iz15l.feishu.cn/wiki/JSp3wk7zuinvIXkIqSUcCXY1nKc)": "作者: [Github@KitsuneX07](https://github.com/KitsuneX07) | [Github@SUC-DriverOld](https://github.com/SUC-DriverOld), [點擊前往教程文檔](https://r1kc63iz15l.feishu.cn/wiki/JSp3wk7zuinvIXkIqSUcCXY1nKc)", "**请将需要处理的音频放置到input文件夹内, 处理完成后的音频将会保存到results文件夹内! 云端输入输出目录不可更改!**": "**請將需要處理的音頻放置到input文件夾內, 處理完成後的音頻將會保存到results文件夾內! 雲端輸入輸出目錄不可更改!**", "文件管理": "文件管理", "文件管理页面是云端WebUI特有的页面, 用于上传, 下载, 删除文件。<br>1. 上传文件: 将文件上传到input文件夹内。可以勾选是否自动解压zip文件<br>2. 下载文件: 以zip格式打包results文件夹内的文件, 输出至WebUI以供下载。注意: 打包不会删除results文件夹, 若打包后不再需要分离结果, 请点击按钮手动删除。<br>3. 删除文件: 删除input和results文件夹内的文件。": "文件管理頁面是雲端WebUI特有的頁面, 用於上傳, 下載, 刪除文件。<br>1. 上傳文件: 將文件上傳到input文件夾內。可以勾選是否自動解壓zip文件<br>2. 下載文件: 以zip格式打包results文件夾內的文件, 輸出至WebUI以供下載。注意: 打包不會刪除results文件夾, 若打包後不再需要分離結果, 請點擊按鈕手動刪除。<br>3. 刪除文件: 刪除input和results文件夾內的文件。", "删除input文件夹内所有文件": "刪除input文件夾內所有文件", "删除results文件夹内所有文件": "刪除results文件夾內所有文件", "刷新input和results文件列表": "刷新input和results文件列表", "打包results文件夹内所有文件": "打包results文件夾內所有文件", "上传一个或多个文件至input文件夹": "上傳一個或多個文件至input文件夾", "自动解压zip文件(仅支持zip, 压缩包内文件名若含有非ASCII字符, 解压后文件名可能为乱码)": "自動解壓zip文件(僅支持zip, 壓縮包內文件名若含有非ASCII字符, 解壓後文件名可能爲亂碼)", "上传文件": "上傳文件", "input和results文件列表": "input和results文件列表", "请先点击刷新按钮": "請先點擊刷新按鈕", "下载results文件夹内所有文件": "下載results文件夾內所有文件", "Window Size: 窗口大小, 用于平衡速度和质量": "Window Size: 窗口大小, 用於平衡速度和質量", "初始模型: 继续训练或微调模型训练时, 请选择初始模型, 否则将从头开始训练! ": "初始模型: 繼續訓練或微調模型訓練時, 請選擇初始模型, 否則將從頭開始訓練! ", "点击合并音频按钮后, 将自动把输入文件夹中的所有音频文件合并为一整个音频文件<br>目前支持的格式包括 .mp3, .flac, .wav, .ogg, m4a 这五种<br>合并后的音频会保存至输出目录中, 文件名为merged_audio_<文件夹名字>.wav": "點擊合併音頻按鈕後, 將自動把輸入文件夾中的所有音頻文件合併爲一整個音頻文件<br>目前支持的格式包括 .mp3, .flac, .wav, .ogg, m4a 這五種<br>合併後的音頻會保存至輸出目錄中, 文件名爲merged_audio_<文件夾名字>.wav"}