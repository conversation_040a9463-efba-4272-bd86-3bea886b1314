audio:
  chunk_size: 261120
  dim_f: 4096
  dim_t: 256
  hop_length: 1024
  min_mean_abs: 0.001
  n_fft: 8192
  num_channels: 2
  sample_rate: 44100
augmentations:
  all:
    channel_shuffle: 0.5
    mp3_compression: 0.01
    mp3_compression_backend: lameenc
    mp3_compression_max_bitrate: 320
    mp3_compression_min_bitrate: 32
    random_inverse: 0.1
    random_polarity: 0.5
  enable: true
  loudness: true
  loudness_max: 1.5
  loudness_min: 0.5
  mixup: true
  mixup_loudness_max: 1.5
  mixup_loudness_min: 0.5
  mixup_probs: !!python/tuple
  - 0.2
  - 0.02
  mp3_compression_on_mixture: 0.01
  mp3_compression_on_mixture_backend: lameenc
  mp3_compression_on_mixture_bitrate_max: 320
  mp3_compression_on_mixture_bitrate_min: 32
  other:
    gaussian_noise: 0.1
    gaussian_noise_max_amplitude: 0.015
    gaussian_noise_min_amplitude: 0.001
    pitch_shift: 0.1
    pitch_shift_max_semitones: 4
    pitch_shift_min_semitones: -4
    time_stretch: 0.01
    time_stretch_max_rate: 1.25
    time_stretch_min_rate: 0.8
  vocals:
    pitch_shift: 0.1
    pitch_shift_max_semitones: 5
    pitch_shift_min_semitones: -5
    seven_band_parametric_eq: 0.25
    seven_band_parametric_eq_max_gain_db: 9
    seven_band_parametric_eq_min_gain_db: -9
    tanh_distortion: 0.1
    tanh_distortion_max: 0.7
    tanh_distortion_min: 0.1
inference:
  batch_size: 1
  dim_t: 256
  num_overlap: 4
model:
  act: gelu
  bottleneck_factor: 4
  growth: 128
  norm: InstanceNorm
  num_blocks_per_scale: 2
  num_channels: 128
  num_scales: 5
  num_subbands: 4
  scale:
  - 2
  - 2
training:
  batch_size: 6
  coarse_loss_clip: true
  ema_momentum: 0.999
  grad_clip: 0
  gradient_accumulation_steps: 1
  instruments:
  - vocals
  - other
  lr: 9.0e-05
  num_epochs: 1000
  num_steps: 1000
  optimizer: adam
  other_fix: true
  patience: 2
  q: 0.95
  read_metadata_procs: 8
  reduce_factor: 0.95
  target_instrument: null
  use_amp: true
