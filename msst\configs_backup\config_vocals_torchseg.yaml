audio:
  chunk_size: 261632
  dim_f: 4096
  dim_t: 512
  hop_length: 512
  n_fft: 8192
  num_channels: 2
  sample_rate: 44100
  min_mean_abs: 0.000

model:
  encoder_name: maxvit_tiny_tf_512 # look with torchseg.list_encoders(). Currently 858 available
  decoder_type: unet # unet, fpn
  act: gelu
  num_channels: 128
  num_subbands: 8

training:
  batch_size: 18
  gradient_accumulation_steps: 1
  grad_clip: 1.0
  instruments:
  - vocals
  - other
  lr: 1.0e-04
  patience: 2
  reduce_factor: 0.95
  target_instrument: null
  num_epochs: 1000
  num_steps: 1000
  q: 0.95
  coarse_loss_clip: true
  ema_momentum: 0.999
  optimizer: radam
  other_fix: true # it's needed for checking on multisong dataset if other is actually instrumental
  use_amp: true # enable or disable usage of mixed precision (float16) - usually it must be true

augmentations:
  enable: false # enable or disable all augmentations (to fast disable if needed)
  loudness: true # randomly change loudness of each stem on the range (loudness_min; loudness_max)
  loudness_min: 0.5
  loudness_max: 1.5
  mixup: true # mix several stems of same type with some probability (only works for dataset types: 1, 2, 3)
  mixup_probs: !!python/tuple # 2 additional stems of the same type (1st with prob 0.2, 2nd with prob 0.02)
    - 0.2
    - 0.02
  mixup_loudness_min: 0.5
  mixup_loudness_max: 1.5

  all:
    channel_shuffle: 0.5 # Set 0 or lower to disable
    random_inverse: 0.1 # inverse track (better lower probability)
    random_polarity: 0.5 # polarity change (multiply waveform to -1)

inference:
  batch_size: 8
  dim_t: 512
  num_overlap: 2