"""
API客户端模块
用于GUI与FastAPI服务的通信
"""

import asyncio
import aiohttp
import json
import logging
from pathlib import Path
from typing import Dict, Optional, Callable, Any, List
import websockets
from datetime import datetime

logger = logging.getLogger(__name__)

class APIClient:
    """API客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url.rstrip('/')
        self.session: Optional[aiohttp.ClientSession] = None
        self.websocket_connections: Dict[str, websockets.WebSocketClientProtocol] = {}
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
        
        # 关闭所有WebSocket连接
        for ws in self.websocket_connections.values():
            await ws.close()
        self.websocket_connections.clear()
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            async with self.session.get(f"{self.base_url}/health") as response:
                if response.status == 200:
                    return await response.json()
                else:
                    raise Exception(f"健康检查失败: HTTP {response.status}")
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            raise
    
    async def upload_file(self, file_path: str) -> Dict[str, Any]:
        """上传文件"""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            data = aiohttp.FormData()
            data.add_field('file', 
                          open(file_path, 'rb'),
                          filename=file_path.name,
                          content_type='audio/wav')
            
            async with self.session.post(f"{self.base_url}/upload", data=data) as response:
                if response.status == 200:
                    result = await response.json()
                    logger.info(f"文件上传成功: {file_path.name}")
                    return result
                else:
                    error_text = await response.text()
                    raise Exception(f"文件上传失败: HTTP {response.status} - {error_text}")
                    
        except Exception as e:
            logger.error(f"文件上传失败: {e}")
            raise
    
    async def separate_audio(
        self, 
        file_id: str, 
        mode: str = "complete",
        output_format: str = "wav"
    ) -> Dict[str, Any]:
        """音频分离"""
        try:
            request_data = {
                "mode": mode,
                "output_format": output_format,
                "model_name": "bandit_v2"
            }
            
            params = {"file_id": file_id}
            
            async with self.session.post(
                f"{self.base_url}/separate", 
                params=params,
                json=request_data
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    logger.info(f"音频分离任务创建成功: {result['task_id']}")
                    return result
                else:
                    error_text = await response.text()
                    raise Exception(f"音频分离失败: HTTP {response.status} - {error_text}")
                    
        except Exception as e:
            logger.error(f"音频分离失败: {e}")
            raise
    
    async def convert_voice(
        self,
        file_id: str,
        model_name: str,
        config_name: Optional[str] = None,
        key_shift: int = 0,
        formant_shift: int = 0,
        f0_extractor: str = "rmvpe",
        vocoder: str = "nsf-hifigan",
        threshold: float = -60.0
    ) -> Dict[str, Any]:
        """音色转换"""
        try:
            request_data = {
                "model_name": model_name,
                "config_name": config_name,
                "key_shift": key_shift,
                "formant_shift": formant_shift,
                "f0_extractor": f0_extractor,
                "vocoder": vocoder,
                "threshold": threshold
            }
            
            params = {"file_id": file_id}
            
            async with self.session.post(
                f"{self.base_url}/convert",
                params=params,
                json=request_data
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    logger.info(f"音色转换任务创建成功: {result['task_id']}")
                    return result
                else:
                    error_text = await response.text()
                    raise Exception(f"音色转换失败: HTTP {response.status} - {error_text}")
                    
        except Exception as e:
            logger.error(f"音色转换失败: {e}")
            raise
    
    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        try:
            async with self.session.get(f"{self.base_url}/tasks/{task_id}") as response:
                if response.status == 200:
                    return await response.json()
                elif response.status == 404:
                    raise Exception("任务不存在")
                else:
                    error_text = await response.text()
                    raise Exception(f"获取任务状态失败: HTTP {response.status} - {error_text}")
                    
        except Exception as e:
            logger.error(f"获取任务状态失败: {e}")
            raise
    
    async def cancel_task(self, task_id: str) -> Dict[str, Any]:
        """取消任务"""
        try:
            async with self.session.delete(f"{self.base_url}/tasks/{task_id}") as response:
                if response.status == 200:
                    result = await response.json()
                    logger.info(f"任务取消成功: {task_id}")
                    return result
                else:
                    error_text = await response.text()
                    raise Exception(f"取消任务失败: HTTP {response.status} - {error_text}")
                    
        except Exception as e:
            logger.error(f"取消任务失败: {e}")
            raise
    
    async def list_models(self) -> Dict[str, Any]:
        """获取模型列表"""
        try:
            async with self.session.get(f"{self.base_url}/models/list") as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    raise Exception(f"获取模型列表失败: HTTP {response.status} - {error_text}")
                    
        except Exception as e:
            logger.error(f"获取模型列表失败: {e}")
            raise
    
    async def scan_models(self) -> Dict[str, Any]:
        """扫描模型"""
        try:
            async with self.session.get(f"{self.base_url}/models/scan") as response:
                if response.status == 200:
                    result = await response.json()
                    logger.info(f"模型扫描完成: {result['message']}")
                    return result
                else:
                    error_text = await response.text()
                    raise Exception(f"模型扫描失败: HTTP {response.status} - {error_text}")
                    
        except Exception as e:
            logger.error(f"模型扫描失败: {e}")
            raise
    
    async def download_file(self, file_id: str, save_path: str) -> str:
        """下载文件"""
        try:
            async with self.session.get(f"{self.base_url}/download/{file_id}") as response:
                if response.status == 200:
                    save_path = Path(save_path)
                    save_path.parent.mkdir(parents=True, exist_ok=True)
                    
                    with open(save_path, 'wb') as f:
                        async for chunk in response.content.iter_chunked(8192):
                            f.write(chunk)
                    
                    logger.info(f"文件下载成功: {save_path}")
                    return str(save_path)
                else:
                    error_text = await response.text()
                    raise Exception(f"文件下载失败: HTTP {response.status} - {error_text}")
                    
        except Exception as e:
            logger.error(f"文件下载失败: {e}")
            raise
    
    async def connect_websocket(
        self, 
        task_id: str, 
        progress_callback: Callable[[Dict[str, Any]], None]
    ):
        """连接WebSocket获取实时进度"""
        try:
            ws_url = f"{self.base_url.replace('http', 'ws')}/ws/{task_id}"
            
            async with websockets.connect(ws_url) as websocket:
                self.websocket_connections[task_id] = websocket
                
                try:
                    async for message in websocket:
                        data = json.loads(message)
                        if data.get("type") == "task_update":
                            progress_callback(data.get("data", {}))
                            
                            # 如果任务完成或失败，断开连接
                            task_data = data.get("data", {})
                            if task_data.get("status") in ["completed", "error", "cancelled"]:
                                break
                                
                except websockets.exceptions.ConnectionClosed:
                    logger.info(f"WebSocket连接已关闭: {task_id}")
                finally:
                    if task_id in self.websocket_connections:
                        del self.websocket_connections[task_id]
                        
        except Exception as e:
            logger.error(f"WebSocket连接失败: {e}")
            raise
    
    def is_server_available(self) -> bool:
        """检查服务器是否可用（同步方法）"""
        try:
            import requests
            response = requests.get(f"{self.base_url}/health", timeout=5)
            return response.status_code == 200
        except:
            return False
