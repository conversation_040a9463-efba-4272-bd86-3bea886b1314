"""
主窗口实现
"""

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QHBoxLayout, QSplitter, QVBoxLayout
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon

from ..utils.constants import APP_NAME, WINDOW_WIDTH, WINDOW_HEIGHT, Layout
from ..utils.styles import StyleManager
from .panels.left_panel import LeftPanel
from .panels.right_panel import RightPanel


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.setup_style()
        
    def init_ui(self):
        """初始化用户界面"""
        # 设置窗口属性
        self.setWindowTitle(APP_NAME)
        self.setGeometry(100, 100, WINDOW_WIDTH, WINDOW_HEIGHT)

        # 设置固定窗口大小，防止手风琴展开时改变整体尺寸
        self.setFixedSize(WINDOW_WIDTH, WINDOW_HEIGHT)

        # 设置窗口图标（如果存在）
        try:
            icon_path = "assets/images/icon.ico"
            self.setWindowIcon(QIcon(icon_path))
        except:
            pass  # 图标文件不存在时忽略
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(8, 8, 8, 8)
        main_layout.setSpacing(8)
        
        # 创建分割器
        self.splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(self.splitter)
        
        # 创建左右面板
        self.left_panel = LeftPanel()
        self.right_panel = RightPanel()
        
        # 添加面板到分割器
        self.splitter.addWidget(self.left_panel)
        self.splitter.addWidget(self.right_panel)
        
        # 设置分割器比例 (1:3)
        self.splitter.setSizes([
            WINDOW_WIDTH * Layout.LEFT_PANEL_RATIO // (Layout.LEFT_PANEL_RATIO + Layout.RIGHT_PANEL_RATIO),
            WINDOW_WIDTH * Layout.RIGHT_PANEL_RATIO // (Layout.LEFT_PANEL_RATIO + Layout.RIGHT_PANEL_RATIO)
        ])
        
        # 设置分割器属性
        self.splitter.setChildrenCollapsible(False)  # 防止面板被完全折叠
        
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet(StyleManager.get_complete_stylesheet())
        
    # 移除resizeEvent方法，因为窗口现在是固定大小
