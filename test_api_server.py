"""
API服务测试脚本
"""

import asyncio
import sys
import os
from pathlib import Path
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_api_server():
    """测试API服务"""
    print("🎵 木偶AI翻唱应用 - API服务测试")
    print("=" * 60)
    
    try:
        # 测试导入
        from src.core.api_client import APIClient
        from src.core.audio_processor import AudioProcessor
        print("✅ API模块导入成功")
        
        # 测试音频处理器
        audio_processor = AudioProcessor()
        print("✅ 音频处理器创建成功")
        
        # 检查关键路径
        print(f"📁 项目根目录: {audio_processor.project_root}")
        print(f"🐍 Python路径: {audio_processor.python_path}")
        print(f"🎵 MSST路径: {audio_processor.msst_path}")
        print(f"🔄 main_reflow路径: {audio_processor.main_reflow_path}")
        
        # 检查路径是否存在
        paths_check = {
            "Python解释器": audio_processor.python_path,
            "MSST目录": audio_processor.msst_path,
            "main_reflow脚本": audio_processor.main_reflow_path,
            "models目录": audio_processor.project_root / "models",
            "workenv目录": audio_processor.project_root / "workenv"
        }
        
        print("\n📋 路径检查:")
        for name, path in paths_check.items():
            if Path(path).exists():
                print(f"   ✅ {name}: {path}")
            else:
                print(f"   ❌ {name}: {path} (不存在)")
        
        # 测试模型扫描
        print("\n🔍 扫描可用模型:")
        models = audio_processor.get_available_models()
        if models:
            for model_name, model_info in models.items():
                print(f"   📦 {model_name}")
                print(f"      模型文件: {model_info['model_path']}")
                if model_info['config_path']:
                    print(f"      配置文件: {model_info['config_path']}")
                print(f"      文件大小: {model_info['size'] / 1024 / 1024:.1f} MB")
        else:
            print("   ⚠️ 未找到可用模型")
        
        # 测试API客户端
        print("\n🌐 测试API客户端:")
        async with APIClient() as client:
            print("✅ API客户端创建成功")
            
            # 测试服务器连接（如果服务器运行中）
            if client.is_server_available():
                print("✅ API服务器连接成功")
                
                try:
                    health = await client.health_check()
                    print(f"   服务状态: {health.get('status')}")
                    print(f"   GPU可用: {health.get('gpu_available')}")
                    print(f"   已加载模型: {health.get('models_loaded')}")
                except Exception as e:
                    print(f"   ⚠️ 健康检查失败: {e}")
                
                try:
                    models_response = await client.list_models()
                    print(f"   API模型数量: {models_response.get('count', 0)}")
                except Exception as e:
                    print(f"   ⚠️ 获取模型列表失败: {e}")
            else:
                print("⚠️ API服务器未运行")
        
        print("\n✅ API服务测试完成！")
        
        print("\n📋 第三阶段功能总结:")
        print("   ✅ FastAPI服务器 - 提供RESTful API接口")
        print("   ✅ 音频处理器 - 集成MSST和main_reflow")
        print("   ✅ API客户端 - GUI与API通信")
        print("   ✅ WebSocket支持 - 实时进度追踪")
        print("   ✅ 文件上传/下载 - 音频文件管理")
        print("   ✅ 任务管理 - 异步任务处理")
        
        print("\n🚀 启动API服务:")
        print(f"   g:/0_Software/DDSP/DDSP6.3/workenv/python.exe api_server.py")
        print("   或者:")
        print(f"   g:/0_Software/DDSP/DDSP6.3/workenv/python.exe api_server.py --host 0.0.0.0 --port 8000")
        
        print("\n📖 API文档:")
        print("   http://localhost:8000/docs")
        print("   http://localhost:8000/redoc")
        
        print("\n🔧 测试API端点:")
        print("   GET  /health - 健康检查")
        print("   GET  /models/list - 获取模型列表")
        print("   POST /upload - 上传音频文件")
        print("   POST /separate - 音频分离")
        print("   POST /convert - 音色转换")
        print("   GET  /tasks/{task_id} - 获取任务状态")
        print("   WS   /ws/{task_id} - 实时进度追踪")
        
        return 0
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("\n🔧 请检查以下依赖:")
        print("   - FastAPI: pip install fastapi")
        print("   - uvicorn: pip install uvicorn")
        print("   - aiohttp: pip install aiohttp")
        print("   - websockets: pip install websockets")
        return 1
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

def test_api_imports():
    """测试API相关导入"""
    print("🔍 测试API依赖导入...")
    
    try:
        import fastapi
        print(f"✅ FastAPI {fastapi.__version__}")
    except ImportError:
        print("❌ FastAPI 未安装")
    
    try:
        import uvicorn
        print(f"✅ Uvicorn {uvicorn.__version__}")
    except ImportError:
        print("❌ Uvicorn 未安装")
    
    try:
        import aiohttp
        print(f"✅ aiohttp {aiohttp.__version__}")
    except ImportError:
        print("❌ aiohttp 未安装")
    
    try:
        import websockets
        print(f"✅ websockets {websockets.__version__}")
    except ImportError:
        print("❌ websockets 未安装")
    
    try:
        import pydantic
        print(f"✅ pydantic {pydantic.__version__}")
    except ImportError:
        print("❌ pydantic 未安装")

if __name__ == "__main__":
    print("🎵 木偶AI翻唱应用 - API服务测试")
    print("=" * 60)
    
    # 测试导入
    test_api_imports()
    print()
    
    # 运行异步测试
    exit_code = asyncio.run(test_api_server())
    sys.exit(exit_code)
