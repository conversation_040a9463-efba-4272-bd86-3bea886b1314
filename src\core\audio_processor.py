"""
音频处理核心模块
集成MSST音频分离和main_reflow音色转换功能
"""

import os
import sys
import subprocess
import asyncio
import logging
from pathlib import Path
from typing import Dict, Optional, Callable, Any
import tempfile
import shutil

logger = logging.getLogger(__name__)

class AudioProcessor:
    """音频处理器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent.parent
        self.python_path = self.project_root / "workenv" / "python.exe"
        self.msst_path = self.project_root / "msst"
        self.main_reflow_path = self.project_root / "main_reflow.py"
        
    async def separate_audio(
        self, 
        input_file: str, 
        output_dir: str,
        mode: str = "complete",
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, str]:
        """
        音频分离
        
        Args:
            input_file: 输入音频文件路径
            output_dir: 输出目录
            mode: 处理模式 (complete/fast)
            progress_callback: 进度回调函数
            
        Returns:
            包含分离结果文件路径的字典
        """
        try:
            if progress_callback:
                await progress_callback(0.1, "准备音频分离...")
            
            # 确保输出目录存在
            Path(output_dir).mkdir(parents=True, exist_ok=True)
            
            # 构建MSST命令
            cmd = [
                str(self.python_path),
                "-m", "msst.inference",
                "--input", input_file,
                "--output_dir", output_dir,
                "--model_type", "bandit_v2" if mode == "complete" else "mel_band_roformer",
                "--device", "cuda" if self._check_cuda() else "cpu"
            ]
            
            if progress_callback:
                await progress_callback(0.2, "启动MSST分离...")
            
            # 执行分离命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=str(self.project_root)
            )
            
            # 监控进度
            await self._monitor_process_progress(
                process, progress_callback, 0.2, 0.9, "音频分离中"
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                error_msg = stderr.decode('utf-8', errors='ignore')
                raise Exception(f"MSST分离失败: {error_msg}")
            
            if progress_callback:
                await progress_callback(0.95, "整理输出文件...")
            
            # 查找输出文件
            result_files = self._find_separation_results(output_dir, input_file)
            
            if progress_callback:
                await progress_callback(1.0, "音频分离完成")
            
            logger.info(f"音频分离完成: {result_files}")
            return result_files
            
        except Exception as e:
            logger.error(f"音频分离失败: {e}")
            raise
    
    async def convert_voice(
        self,
        input_file: str,
        output_file: str,
        model_path: str,
        config_path: Optional[str] = None,
        key_shift: int = 0,
        formant_shift: int = 0,
        f0_extractor: str = "rmvpe",
        progress_callback: Optional[Callable] = None
    ) -> str:
        """
        音色转换
        
        Args:
            input_file: 输入音频文件路径
            output_file: 输出音频文件路径
            model_path: 模型文件路径
            config_path: 配置文件路径
            key_shift: 音高调节
            formant_shift: 共振峰偏移
            f0_extractor: F0提取器
            progress_callback: 进度回调函数
            
        Returns:
            输出文件路径
        """
        try:
            if progress_callback:
                await progress_callback(0.1, "准备音色转换...")
            
            # 确保输出目录存在
            Path(output_file).parent.mkdir(parents=True, exist_ok=True)
            
            # 构建main_reflow命令
            cmd = [
                str(self.python_path),
                str(self.main_reflow_path),
                "-m", model_path,
                "-i", input_file,
                "-o", output_file,
                "-k", str(key_shift),
                "-f", str(formant_shift),
                "-pe", f0_extractor
            ]
            
            if config_path:
                cmd.extend(["-c", config_path])
            
            if progress_callback:
                await progress_callback(0.2, "启动音色转换...")
            
            # 执行转换命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=str(self.project_root)
            )
            
            # 监控进度
            await self._monitor_process_progress(
                process, progress_callback, 0.2, 0.9, "音色转换中"
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                error_msg = stderr.decode('utf-8', errors='ignore')
                raise Exception(f"音色转换失败: {error_msg}")
            
            if not Path(output_file).exists():
                raise Exception("转换完成但输出文件不存在")
            
            if progress_callback:
                await progress_callback(1.0, "音色转换完成")
            
            logger.info(f"音色转换完成: {output_file}")
            return output_file
            
        except Exception as e:
            logger.error(f"音色转换失败: {e}")
            raise
    
    def _check_cuda(self) -> bool:
        """检查CUDA可用性"""
        try:
            import torch
            return torch.cuda.is_available()
        except ImportError:
            return False
    
    async def _monitor_process_progress(
        self,
        process: asyncio.subprocess.Process,
        progress_callback: Optional[Callable],
        start_progress: float,
        end_progress: float,
        message: str
    ):
        """监控进程进度"""
        if not progress_callback:
            return
        
        # 简单的进度模拟，实际应用中可以解析输出来获取真实进度
        progress_range = end_progress - start_progress
        steps = 10
        
        for i in range(steps):
            if process.returncode is not None:
                break
                
            current_progress = start_progress + (progress_range * i / steps)
            await progress_callback(current_progress, f"{message}... ({i+1}/{steps})")
            await asyncio.sleep(1)
    
    def _find_separation_results(self, output_dir: str, input_file: str) -> Dict[str, str]:
        """查找分离结果文件"""
        output_path = Path(output_dir)
        input_name = Path(input_file).stem
        
        # 常见的MSST输出文件命名模式
        patterns = [
            f"{input_name}_vocals.wav",
            f"{input_name}_instrumental.wav",
            f"{input_name}_Vocals.wav",
            f"{input_name}_Instrumental.wav",
            "vocals.wav",
            "instrumental.wav"
        ]
        
        result = {}
        
        # 查找人声文件
        for pattern in patterns:
            if "vocal" in pattern.lower():
                vocal_file = output_path / pattern
                if vocal_file.exists():
                    result["vocal"] = str(vocal_file)
                    break
        
        # 查找伴奏文件
        for pattern in patterns:
            if "instrumental" in pattern.lower():
                instrumental_file = output_path / pattern
                if instrumental_file.exists():
                    result["instrumental"] = str(instrumental_file)
                    break
        
        # 如果没有找到标准命名的文件，列出所有wav文件
        if not result:
            wav_files = list(output_path.glob("*.wav"))
            if len(wav_files) >= 2:
                result["vocal"] = str(wav_files[0])
                result["instrumental"] = str(wav_files[1])
            elif len(wav_files) == 1:
                result["output"] = str(wav_files[0])
        
        return result
    
    def get_available_models(self) -> Dict[str, Dict]:
        """获取可用模型列表"""
        models_dir = self.project_root / "models"
        models = {}
        
        if not models_dir.exists():
            return models
        
        for model_file in models_dir.glob("*.pt"):
            config_file = models_dir / f"{model_file.stem}.yaml"
            
            models[model_file.stem] = {
                "name": model_file.stem,
                "model_path": str(model_file),
                "config_path": str(config_file) if config_file.exists() else None,
                "size": model_file.stat().st_size,
                "type": "RectifiedFlow"
            }
        
        return models
    
    def standardize_audio(self, input_file: str, output_file: str) -> str:
        """标准化音频格式"""
        try:
            ffmpeg_path = self.project_root / "ffmpeg" / "bin" / "ffmpeg.exe"
            
            cmd = [
                str(ffmpeg_path),
                "-i", input_file,
                "-ar", "44100",
                "-ac", "1",
                "-y",
                output_file
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                raise Exception(f"音频标准化失败: {result.stderr}")
            
            return output_file
            
        except Exception as e:
            logger.error(f"音频标准化失败: {e}")
            raise
