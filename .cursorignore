# Add directories or file patterns to ignore during indexing (e.g. foo/ or *.csv)
/workenv
/pretrain
/ffmpeg  
/0-other
.pt
.ckpt
.onnx
.pth
只修改我提到的部分，不要动其他代码，可以调用sequential-thinking MCP来辅助你，use context7。
desktop-commander和

# 命令行参数 ddsp转换
main_reflow.py -i C:\\Users\\<USER>\\Desktop\\测试\\测试人声.wav -o C:\\Users\\<USER>\\Desktop\\转换测试.wav -m G:\\0_Software\\DDSP\\DDSP6.3\\models\\YSML.pt -c G:\\0_Software\\DDSP\\DDSP6.3\\models\\YSML.yaml -step 10 -method euler -id 0 -pe rmvpe -k 0

# 命令行参数 MMST分离
./msst/scripts/preset_infer_cli.py -p ./msst/preset.json -i C:\\Users\\<USER>\\Desktop\\测试 -o results -f wav --extra_output_dir