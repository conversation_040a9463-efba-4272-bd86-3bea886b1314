"""
右侧面板实现
"""

from PySide6.QtWidgets import (
    QFrame, QVBoxLayout, QLabel, QComboBox, QSlider, 
    QHBoxLayout, QCheckBox, QScrollArea, QWidget, QGroupBox
)
from PySide6.QtCore import Qt

from ...utils.constants import Colors, Styles
from ..components.slider_widget import SliderWidget


class RightPanel(QFrame):
    """右侧面板类"""
    
    def __init__(self):
        super().__init__()
        self.setProperty("class", "panel")
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        # 创建主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(Styles.PADDING_NORMAL, Styles.PADDING_NORMAL, 
                                 Styles.PADDING_NORMAL, Styles.PADDING_NORMAL)
        layout.setSpacing(Styles.MARGIN_NORMAL)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setFrameShape(QFrame.NoFrame)
        
        # 配置容器
        config_widget = QWidget()
        config_layout = QVBoxLayout(config_widget)
        config_layout.setContentsMargins(0, 0, 0, 0)
        config_layout.setSpacing(Styles.MARGIN_NORMAL)
        
        # 模型与音高配置
        self.create_model_config_section(config_layout)
        
        # 混响与和声配置
        self.create_reverb_config_section(config_layout)
        
        # 高级参数配置
        self.create_advanced_config_section(config_layout)
        
        # 添加弹性空间
        config_layout.addStretch()
        
        scroll_area.setWidget(config_widget)
        layout.addWidget(scroll_area)
        
        # 设置滚动区域样式
        scroll_area.setStyleSheet(f"""
        QScrollArea {{
            background-color: transparent;
            border: none;
        }}
        QScrollBar:vertical {{
            background-color: {Colors.SECONDARY_BG};
            width: 8px;
            border-radius: 4px;
        }}
        QScrollBar::handle:vertical {{
            background-color: {Colors.BORDER};
            border-radius: 4px;
            min-height: 20px;
        }}
        QScrollBar::handle:vertical:hover {{
            background-color: {Colors.ACCENT};
        }}
        """)
        
    def create_model_config_section(self, layout):
        """创建模型与音高配置区域"""
        group = QGroupBox("模型与音高配置")
        group.setStyleSheet(f"""
        QGroupBox {{
            font-weight: bold;
            font-size: {Styles.FONT_SIZE_LARGE}px;
            color: {Colors.FOREGROUND};
            border: 1px solid {Colors.BORDER};
            border-radius: {Styles.CARD_RADIUS};
            margin-top: 10px;
            padding-top: 10px;
        }}
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }}
        """)
        
        group_layout = QVBoxLayout(group)
        group_layout.setSpacing(Styles.MARGIN_NORMAL)
        
        # 音色模型选择
        model_label = QLabel("音色模型:")
        self.model_combo = QComboBox()
        self.model_combo.addItems(["请选择模型...", "YSML", "于洋", "山海"])
        group_layout.addWidget(model_label)
        group_layout.addWidget(self.model_combo)
        
        # 配置文件选择
        config_label = QLabel("配置文件:")
        self.config_combo = QComboBox()
        self.config_combo.addItems(["自动匹配", "YSML.yaml", "于洋.yaml", "山海.yaml"])
        group_layout.addWidget(config_label)
        group_layout.addWidget(self.config_combo)
        
        # 人声音高调节
        self.vocal_pitch_slider = SliderWidget("人声音高", -12, 12, 0, "半音")
        group_layout.addWidget(self.vocal_pitch_slider)
        
        # 伴奏音高调节
        self.instrumental_pitch_slider = SliderWidget("伴奏音高", -12, 12, 0, "半音")
        group_layout.addWidget(self.instrumental_pitch_slider)
        
        layout.addWidget(group)
        
    def create_reverb_config_section(self, layout):
        """创建混响与和声配置区域"""
        group = QGroupBox("混响与和声配置")
        group.setStyleSheet(f"""
        QGroupBox {{
            font-weight: bold;
            font-size: {Styles.FONT_SIZE_LARGE}px;
            color: {Colors.FOREGROUND};
            border: 1px solid {Colors.BORDER};
            border-radius: {Styles.CARD_RADIUS};
            margin-top: 10px;
            padding-top: 10px;
        }}
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }}
        """)
        
        group_layout = QVBoxLayout(group)
        group_layout.setSpacing(Styles.MARGIN_NORMAL)
        
        # 混响开关
        self.reverb_enabled = QCheckBox("启用混响效果")
        self.reverb_enabled.setChecked(True)
        group_layout.addWidget(self.reverb_enabled)
        
        # 房间大小
        self.room_size_slider = SliderWidget("房间大小", 0, 100, 30, "%")
        group_layout.addWidget(self.room_size_slider)
        
        # 湿声比例
        self.wet_level_slider = SliderWidget("湿声比例", 0, 100, 20, "%")
        group_layout.addWidget(self.wet_level_slider)
        
        # 干声比例
        self.dry_level_slider = SliderWidget("干声比例", 0, 100, 90, "%")
        group_layout.addWidget(self.dry_level_slider)
        
        layout.addWidget(group)
        
    def create_advanced_config_section(self, layout):
        """创建高级参数配置区域"""
        group = QGroupBox("高级参数配置")
        group.setStyleSheet(f"""
        QGroupBox {{
            font-weight: bold;
            font-size: {Styles.FONT_SIZE_LARGE}px;
            color: {Colors.FOREGROUND};
            border: 1px solid {Colors.BORDER};
            border-radius: {Styles.CARD_RADIUS};
            margin-top: 10px;
            padding-top: 10px;
        }}
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }}
        """)
        
        group_layout = QVBoxLayout(group)
        group_layout.setSpacing(Styles.MARGIN_NORMAL)
        
        # 声码器选择
        vocoder_label = QLabel("声码器:")
        self.vocoder_combo = QComboBox()
        self.vocoder_combo.addItems(["nsf-hifigan", "pc-nsf-hifigan"])
        group_layout.addWidget(vocoder_label)
        group_layout.addWidget(self.vocoder_combo)
        
        # F0提取器选择
        f0_label = QLabel("F0提取器:")
        self.f0_combo = QComboBox()
        self.f0_combo.addItems(["rmvpe", "crepe", "harvest", "dio", "parselmouth"])
        group_layout.addWidget(f0_label)
        group_layout.addWidget(self.f0_combo)
        
        # 共振峰偏移
        self.formant_shift_slider = SliderWidget("共振峰偏移", -12, 12, 0, "半音")
        group_layout.addWidget(self.formant_shift_slider)
        
        # 响应阈值
        self.threshold_slider = SliderWidget("响应阈值", -80, -20, -60, "dB")
        group_layout.addWidget(self.threshold_slider)
        
        layout.addWidget(group)
