"""
右侧面板实现
"""

from PySide6.QtWidgets import (
    QFrame, QVBoxLayout, QLabel, QComboBox, QSlider,
    QHBoxLayout, QCheckBox, QScrollArea, QWidget, QGroupBox, QSpinBox
)
from PySide6.QtCore import Qt

from ...utils.constants import Colors, Styles
from ..components.slider_widget import SliderWidget


class RightPanel(QFrame):
    """右侧面板类"""
    
    def __init__(self):
        super().__init__()
        self.setProperty("class", "panel")
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面 - 参考HTML版本的统一卡片布局"""
        # 创建主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(Styles.PADDING_NORMAL, Styles.PADDING_NORMAL,
                                 Styles.PADDING_NORMAL, Styles.PADDING_NORMAL)
        layout.setSpacing(Styles.MARGIN_NORMAL)

        # 创建统一的卡片容器 (参考HTML版本的单一card布局)
        main_card = QFrame()
        main_card.setProperty("class", "main-config-card")
        main_card.setStyleSheet(f"""
        QFrame[class="main-config-card"] {{
            background-color: {Colors.CARD_BG};
            border: 1px solid {Colors.BORDER};
            border-radius: {Styles.CARD_RADIUS};
            padding: {Styles.PADDING_LARGE}px;
        }}
        """)

        # 卡片内容布局
        card_layout = QVBoxLayout(main_card)
        card_layout.setContentsMargins(0, 0, 0, 0)
        card_layout.setSpacing(Styles.MARGIN_LARGE * 2)  # 增大各部分间距

        # 模型与音高配置
        self.create_model_config_section(card_layout)

        # 混响与和声配置
        self.create_reverb_config_section(card_layout)

        # 高级参数配置
        self.create_advanced_config_section(card_layout)

        # 将卡片添加到滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setFrameShape(QFrame.NoFrame)
        scroll_area.setWidget(main_card)

        layout.addWidget(scroll_area)
        
        # 设置滚动区域样式
        scroll_area.setStyleSheet(f"""
        QScrollArea {{
            background-color: transparent;
            border: none;
        }}
        QScrollBar:vertical {{
            background-color: {Colors.SECONDARY_BG};
            width: 8px;
            border-radius: 4px;
        }}
        QScrollBar::handle:vertical {{
            background-color: {Colors.BORDER};
            border-radius: 4px;
            min-height: 20px;
        }}
        QScrollBar::handle:vertical:hover {{
            background-color: {Colors.ACCENT};
        }}
        """)
        
    def create_model_config_section(self, layout):
        """创建模型与音高配置区域 - 参考HTML版本样式"""
        # 创建区域标题
        title_label = QLabel("模型与音高")
        title_label.setStyleSheet(f"""
        QLabel {{
            font-size: {Styles.FONT_SIZE_TITLE}px;
            font-weight: bold;
            color: {Colors.FOREGROUND};
            margin-bottom: {Styles.MARGIN_NORMAL}px;
        }}
        """)
        layout.addWidget(title_label)

        # 创建网格布局容器
        grid_container = QFrame()
        grid_layout = QVBoxLayout(grid_container)
        grid_layout.setSpacing(Styles.MARGIN_NORMAL)

        # 第一行：音色模型和配置文件
        row1_container = QFrame()
        row1_layout = QHBoxLayout(row1_container)
        row1_layout.setSpacing(Styles.MARGIN_LARGE)

        # 音色模型 (pt)
        model_container = QFrame()
        model_layout = QVBoxLayout(model_container)
        model_layout.setContentsMargins(0, 0, 0, 0)
        model_layout.setSpacing(Styles.MARGIN_SMALL)

        model_label = QLabel("音色模型 (pt):")
        model_label.setProperty("class", "label-text")
        self.model_combo = QComboBox()
        self.model_combo.addItems(["YSML.pt", "于洋.pt", "山海.pt"])
        self.model_combo.setProperty("class", "input-field")

        model_layout.addWidget(model_label)
        model_layout.addWidget(self.model_combo)
        row1_layout.addWidget(model_container)

        # 配置文件 (yaml)
        config_container = QFrame()
        config_layout = QVBoxLayout(config_container)
        config_layout.setContentsMargins(0, 0, 0, 0)
        config_layout.setSpacing(Styles.MARGIN_SMALL)

        config_label = QLabel("配置文件 (yaml):")
        config_label.setProperty("class", "label-text")
        self.config_combo = QComboBox()
        self.config_combo.addItems(["YSML.yaml", "于洋.yaml", "山海.yaml"])
        self.config_combo.setProperty("class", "input-field")

        config_layout.addWidget(config_label)
        config_layout.addWidget(self.config_combo)
        row1_layout.addWidget(config_container)

        grid_layout.addWidget(row1_container)

        # 第二行：音高调节滑块
        row2_container = QFrame()
        row2_layout = QHBoxLayout(row2_container)
        row2_layout.setSpacing(Styles.MARGIN_LARGE)

        # 人声音高
        self.vocal_pitch_slider = SliderWidget("人声音高", -12, 12, 0, "")
        row2_layout.addWidget(self.vocal_pitch_slider)

        # 伴奏音高
        self.instrumental_pitch_slider = SliderWidget("伴奏音高", -12, 12, 0, "")
        row2_layout.addWidget(self.instrumental_pitch_slider)

        grid_layout.addWidget(row2_container)
        layout.addWidget(grid_container)
        
    def create_reverb_config_section(self, layout):
        """创建混响与和声配置区域 - 参考HTML版本"""
        # 创建区域标题
        title_label = QLabel("混响与和声")
        title_label.setStyleSheet(f"""
        QLabel {{
            font-size: {Styles.FONT_SIZE_TITLE}px;
            font-weight: bold;
            color: {Colors.FOREGROUND};
            margin-bottom: {Styles.MARGIN_NORMAL}px;
        }}
        """)
        layout.addWidget(title_label)

        # 创建配置容器
        config_container = QFrame()
        config_layout = QVBoxLayout(config_container)
        config_layout.setSpacing(Styles.MARGIN_NORMAL)

        # 复选框行
        checkbox_container = QFrame()
        checkbox_layout = QHBoxLayout(checkbox_container)
        checkbox_layout.setSpacing(Styles.MARGIN_LARGE)

        # 启用混响
        self.reverb_enabled = QCheckBox("启用混响")
        self.reverb_enabled.setChecked(False)  # 参考HTML版本默认未选中
        checkbox_layout.addWidget(self.reverb_enabled)

        # 和声加入伴奏 (参考HTML版本新增)
        self.harmony_to_instrumental = QCheckBox("和声加入伴奏")
        self.harmony_to_instrumental.setChecked(False)
        checkbox_layout.addWidget(self.harmony_to_instrumental)

        checkbox_layout.addStretch()  # 添加弹性空间
        config_layout.addWidget(checkbox_container)

        # 滑块网格布局
        sliders_container = QFrame()
        sliders_layout = QVBoxLayout(sliders_container)
        sliders_layout.setSpacing(Styles.MARGIN_NORMAL)

        # 第一行滑块
        row1_container = QFrame()
        row1_layout = QHBoxLayout(row1_container)
        row1_layout.setSpacing(Styles.MARGIN_LARGE)

        # 房间大小 (参考HTML版本数值范围0-1，默认0.60)
        self.room_size_slider = SliderWidget("房间大小", 0, 100, 60, "")
        row1_layout.addWidget(self.room_size_slider)

        # 阻尼 (参考HTML版本新增)
        self.reverb_damping_slider = SliderWidget("阻尼", 0, 100, 10, "")
        row1_layout.addWidget(self.reverb_damping_slider)

        sliders_layout.addWidget(row1_container)

        # 第二行滑块
        row2_container = QFrame()
        row2_layout = QHBoxLayout(row2_container)
        row2_layout.setSpacing(Styles.MARGIN_LARGE)

        # 湿润度 (参考HTML版本，默认0.30)
        self.wet_level_slider = SliderWidget("湿润度", 0, 100, 30, "")
        row2_layout.addWidget(self.wet_level_slider)

        # 干燥度 (参考HTML版本，默认0.90)
        self.dry_level_slider = SliderWidget("干燥度", 0, 100, 90, "")
        row2_layout.addWidget(self.dry_level_slider)

        sliders_layout.addWidget(row2_container)
        config_layout.addWidget(sliders_container)

        layout.addWidget(config_container)
        
    def create_advanced_config_section(self, layout):
        """创建高级参数配置区域 - 参考HTML版本完整选项"""
        # 创建区域标题
        title_label = QLabel("高级参数配置")
        title_label.setStyleSheet(f"""
        QLabel {{
            font-size: {Styles.FONT_SIZE_TITLE}px;
            font-weight: bold;
            color: {Colors.FOREGROUND};
            margin-bottom: {Styles.MARGIN_NORMAL}px;
        }}
        """)
        layout.addWidget(title_label)

        # 创建网格布局容器
        grid_container = QFrame()
        grid_layout = QVBoxLayout(grid_container)
        grid_layout.setSpacing(Styles.MARGIN_NORMAL)

        # 第一行：声码器和F0提取器
        row1_container = QFrame()
        row1_layout = QHBoxLayout(row1_container)
        row1_layout.setSpacing(Styles.MARGIN_LARGE)

        # 声码器
        vocoder_container = QFrame()
        vocoder_layout = QVBoxLayout(vocoder_container)
        vocoder_layout.setContentsMargins(0, 0, 0, 0)
        vocoder_layout.setSpacing(Styles.MARGIN_SMALL)

        vocoder_label = QLabel("声码器:")
        vocoder_label.setProperty("class", "label-text")
        self.vocoder_combo = QComboBox()
        self.vocoder_combo.addItems([
            "pc_nsf_hifigan_testing",  # 参考HTML版本默认选项
            "kouon_pc",
            "nsf_hifigan"
        ])
        self.vocoder_combo.setProperty("class", "input-field")

        vocoder_layout.addWidget(vocoder_label)
        vocoder_layout.addWidget(self.vocoder_combo)
        row1_layout.addWidget(vocoder_container)

        # F0提取器
        f0_container = QFrame()
        f0_layout = QVBoxLayout(f0_container)
        f0_layout.setContentsMargins(0, 0, 0, 0)
        f0_layout.setSpacing(Styles.MARGIN_SMALL)

        f0_label = QLabel("F0提取器:")
        f0_label.setProperty("class", "label-text")
        self.f0_combo = QComboBox()
        self.f0_combo.addItems([
            "rmvpe (默认)",  # 参考HTML版本
            "parselmouth",
            "dio",
            "harvest",
            "crepe",
            "fcpe"
        ])
        self.f0_combo.setProperty("class", "input-field")

        f0_layout.addWidget(f0_label)
        f0_layout.addWidget(self.f0_combo)
        row1_layout.addWidget(f0_container)

        grid_layout.addWidget(row1_container)

        # 第二行：共振峰偏移和采样步数
        row2_container = QFrame()
        row2_layout = QHBoxLayout(row2_container)
        row2_layout.setSpacing(Styles.MARGIN_LARGE)

        # 共振峰偏移
        self.formant_shift_slider = SliderWidget("共振峰偏移", -6, 6, 0, "")
        row2_layout.addWidget(self.formant_shift_slider)

        # 采样步数 (参考HTML版本新增)
        sampling_container = QFrame()
        sampling_layout = QVBoxLayout(sampling_container)
        sampling_layout.setContentsMargins(0, 0, 0, 0)
        sampling_layout.setSpacing(Styles.MARGIN_SMALL)

        sampling_label = QLabel("采样步数:")
        sampling_label.setProperty("class", "label-text")
        self.sampling_steps_spinbox = QSpinBox()
        self.sampling_steps_spinbox.setRange(1, 200)
        self.sampling_steps_spinbox.setValue(50)  # 参考HTML版本默认值
        self.sampling_steps_spinbox.setProperty("class", "input-field")

        sampling_layout.addWidget(sampling_label)
        sampling_layout.addWidget(self.sampling_steps_spinbox)
        row2_layout.addWidget(sampling_container)

        grid_layout.addWidget(row2_container)

        # 第三行：采样器和设备选择
        row3_container = QFrame()
        row3_layout = QHBoxLayout(row3_container)
        row3_layout.setSpacing(Styles.MARGIN_LARGE)

        # 采样器 (参考HTML版本新增)
        sampler_container = QFrame()
        sampler_layout = QVBoxLayout(sampler_container)
        sampler_layout.setContentsMargins(0, 0, 0, 0)
        sampler_layout.setSpacing(Styles.MARGIN_SMALL)

        sampler_label = QLabel("采样器:")
        sampler_label.setProperty("class", "label-text")
        self.sampler_combo = QComboBox()
        self.sampler_combo.addItems(["euler", "rk4"])  # 参考HTML版本
        self.sampler_combo.setProperty("class", "input-field")

        sampler_layout.addWidget(sampler_label)
        sampler_layout.addWidget(self.sampler_combo)
        row3_layout.addWidget(sampler_container)

        # 设备选择 (参考HTML版本新增)
        device_container = QFrame()
        device_layout = QVBoxLayout(device_container)
        device_layout.setContentsMargins(0, 0, 0, 0)
        device_layout.setSpacing(Styles.MARGIN_SMALL)

        device_label = QLabel("设备选择:")
        device_label.setProperty("class", "label-text")
        self.device_combo = QComboBox()
        self.device_combo.addItems(["CUDA (默认)", "CPU"])  # 参考HTML版本
        self.device_combo.setProperty("class", "input-field")

        device_layout.addWidget(device_label)
        device_layout.addWidget(self.device_combo)
        row3_layout.addWidget(device_container)

        grid_layout.addWidget(row3_container)
        layout.addWidget(grid_container)
