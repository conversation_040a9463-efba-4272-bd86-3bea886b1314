{"仅供个人娱乐和非商业用途, 禁止用于血腥/暴力/性相关/政治相关内容。[点击前往教程文档](https://r1kc63iz15l.feishu.cn/wiki/JSp3wk7zuinvIXkIqSUcCXY1nKc)<br>本整合包完全免费, 严禁以任何形式倒卖, 如果你从任何地方**付费**购买了本整合包, 请**立即退款**。<br> 整合包作者: [bilibili@阿狸不吃隼舞](https://space.bilibili.com/403335715) [Github@KitsuneX07](https://github.com/KitsuneX07) | [Bilibili@Sucial](https://space.bilibili.com/445022409) [Github@SUC-DriverOld](https://github.com/SUC-DriverOld) | Gradio主题: [Gradio Theme](https://huggingface.co/spaces/NoCrypt/miku)": "個人の娯楽および非商業用途のみにご利用いただけます、血腥/暴力/性関連/政治関連のコンテンツには使用禁止です。<br>パッケージの作者: [Github@KitsuneX07](https://github.com/KitsuneX07) | [Github@SUC-DriverOld](https://github.com/SUC-DriverOld) | Gradioテーマ: [Gradio Theme](https://huggingface.co/spaces/NoCrypt/miku)", "MSST分离": "MSST分離", "UVR分离": "UVR分離", "预设流程": "プリセットフロー", "合奏模式": "合奏モード", "小工具": "ツール", "安装模型": "モデルをインストール", "MSST训练": "MSSTトレーニング", "设置": "設定", "输出音轨": "出力トラック", "请至少添加2个模型到合奏流程": "少なくとも2つのモデルを合奏プロセスに追加してください。", "合奏流程已保存": "合奏プロセスが保存されました。", "请上传至少一个音频文件!": "少なくとも1つのオーディオファイルをアップロードしてください！", "请先创建合奏流程": "まず合奏プロセスを作成してください。", "模型": "モデル", "不存在": "存在しません", "用户强制终止": "ユーザーが強制終了しました", "处理失败: ": "処理失敗: ", "处理完成, 成功: ": "処理完了、成功：", "个文件, 失败: ": "個のファイル、失敗: ", "个文件": "個のファイル", ", 结果已保存至: ": "、結果は次に保存されました：", ", 耗时: ": "、所要時間: ", "请上传至少2个文件": "少なくとも2つのファイルをアップロードしてください", "上传的文件数目与权重数目不匹配": "アップロードされたファイルの数と重みの数が一致しません", "处理完成, 文件已保存为: ": "処理完了、ファイルは保存されました: ", "处理失败!": "処理に失敗しました！", "input文件夹内文件列表:\\n": "inputフォルダ内のファイルリスト:\\n", "input文件夹为空\\n": "inputフォルダは空です\\n", "results文件夹内文件列表:\\n": "resultsフォルダ内のファイルリスト:\\n", "results文件夹为空\\n": "resultsフォルダは空です\\n", "已删除input文件夹内所有文件": "inputフォルダ内のすべてのファイルを削除しました", "已删除results文件夹内所有文件": "resultsフォルダ内のすべてのファイルを削除しました", "请先选择模型": "まずモデルを選択してください", "仅输出主音轨": "主音声トラックのみを出力", "仅输出次音轨": "サブ音声トラックのみを出力", "已打开下载管理器": "ダウンロードマネージャを開きました", "选择模型": "モデルを選択", "模型名字": "モデル名", "模型类型": "モデルタイプ", "主要提取音轨": "メイントラック", "次要提取音轨": "サブトラック", "下载链接": "ダウンロードリンク", "模型类别": "モデルカテゴリ", "可提取音轨": "音轨を抽出", "模型大小": "モデルサイズ", "模型已安装": "モデルがインストールされています", "无法校验sha256": "sha256を検証できません。", "sha256校验失败": "sha256検証に失敗しました", "模型sha256校验失败, 请重新下载": "モデルのsha256検証に失敗しました、もう一度ダウンロードしてください", "sha256校验成功": "sha256検証に成功しました", "模型未安装": "モデルがインストールされていません", "请选择模型类型": "モデルタイプを選択してください", "已安装": "インストール済み", "请手动删除后重新下载": "手動で削除してから再ダウンロードしてください。", "下载成功": "ダウンロード成功", "下载失败": "ダウンロード失敗", "已安装。请勿重复安装。": "すでにインストールされています。再インストールしないでください。", "已打开": "開かれました", "的下载链接": "のダウンロードリンク", "上传参数文件": "パラメーターファイルをアップロード", "上传参数": "パラメーターをアップロード", "请上传'.yaml'格式的配置文件": "'.yaml'形式の設定ファイルをアップロードしてください", "请上传'ckpt', 'chpt', 'th'格式的模型文件": "'ckpt', 'chpt', 'th'形式のモデルファイルをアップロードしてください", "请输入正确的模型类别和模型类型": "正しいモデルカテゴリとモデルタイプを入力してください", "安装成功。重启WebUI以刷新模型列表": "インストール成功。モデルリストを更新するためにWebUIを再起動してください", "安装失败": "インストール失敗", "请输入选择模型参数": "モデルパラメータを選択してください", "请上传'.json'格式的参数文件": "'.json'形式のパラメーターファイルをアップロードしてください", "请上传'.pth'格式的模型文件": "'.pth'形式のモデルファイルをアップロードしてください", "请输入正确的音轨名称": "正しいトラック名を入力してください", "配置保存成功!": "設定の保存に成功しました！", "非官方模型不支持重置配置!": "非公式モデルでは構成のリセットはサポートされていません！", "配置重置成功!": "設定のリセットに成功しました！", "备份文件不存在!": "バックアップファイルが存在しません！", "选择输出音轨": "出力トラックを選択", "请选择模型": "モデルを選択してください", "请选择输入目录": "入力ディレクトリを選択してください", "请选择输出目录": "出力ディレクトリを選択してください", "请选择GPU": "GPUを選択してください", "处理完成, 结果已保存至: ": "処理が完了しました、結果は次の場所に保存されました: ", "暂无备份文件": "バックアップファイルがありません", "作为下一模型输入(或结果输出)的音轨": "次のモデル入力（または結果出力）としてのトラック", "直接保存至输出目录的音轨(可多选)": "出力ディレクトリに直接保存されるトラック（複数選択可）", "不输出": "出力しない", "请填写预设名称": "プリセット名を入力してください", "预设": "プリセット", "保存成功": "保存に成功しました", "请选择预设": "プリセットを選択してください", "不支持的预设版本: ": "サポートされていないプリセットバージョン：", ", 请重新制作预设。": "、プリセットを再作成してください。", "预设版本不支持": "プリセットバージョンはサポートされていません。", "预设不存在": "プリセットが存在しません", "删除成功": "削除に成功しました", "预设已删除": "プリセットが削除されました", "选择需要恢复的预设流程备份": "復元するプリセットプロセスのバックアップを選択", "已成功恢复备份": "バックアップの復元に成功しました", "设置重置成功, 请重启WebUI刷新! ": "設定のリセットに成功しました、リフレッシュするにはWebUIを再起動してください！", "记录重置成功, 请重启WebUI刷新! ": "記録のリセットに成功しました、リフレッシュするにはWebUIを再起動してください！", "请选择正确的模型目录": "正しいモデルディレクトリを選択してください", "设置保存成功! 请重启WebUI以应用。": "設定の保存に成功しました！適用するにはWebUIを再起動してください。", "当前版本: ": "現在のバージョン: ", ", 发现新版本: ": "、新しいバージョンが見つかりました: ", ", 已是最新版本": "、すでに最新バージョンです", "检查更新失败": "更新チェックに失敗しました", "语言已更改, 重启WebUI生效": "言語が変更されました、WebUIの有効化を再開しました", "成功将端口设置为": "ポートが設定されました: ", ", 重启WebUI生效": "、WebUIの有効化を再開しました", "huggingface.co (需要魔法)": "huggingface.co", "hf-mirror.com (镜像站可直连)": "hf-mirror.com（中国人ユーザー用）", "下载链接已更改": "ダウンロードリンクが変更されました", "公共链接已开启, 重启WebUI生效": "パブリックリンクがオープンし、WebUIの有効化を再開しました", "公共链接已关闭, 重启WebUI生效": "パブリックリンクが閉じられ、WebUIの有効化を再開しました", "已开启局域网分享, 重启WebUI生效": "LAN共有を開始し、WebUIの有効化を再開しました", "已关闭局域网分享, 重启WebUI生效": "LAN共有をオフにし、WebUIの有効化を再起動しました", "已开启自动清理缓存": "キャッシュの自動クリーンアップがオン", "已关闭自动清理缓存": "キャッシュの自動クリーンアップをオフにしました", "已开启调试模式": "デバッグモードが有効になりました", "已关闭调试模式": "デバッグモードが無効になりました", "主题已更改, 重启WebUI生效": "テーマが変更されました、WebUIを再起動して有効にします", "音频设置已保存": "オーディオ設定が保存されました。", "已重命名为": "に名前を変更しました", "选择模型类型": "モデルタイプを選択", "请先选择模型类型": "まずモデルタイプを選択してください", "新模型名称后缀错误!": "新しいモデル名の接尾辞が間違っています！", "模型名字已存在! 请重新命名!": "モデル名は既に存在します！名前を変更してください！", "重命名失败!": "名前変更に失敗しました！", "检测到": "検出しました", "旧版配置, 正在更新至最新版": "旧バージョン設定、最新バージョンに更新中", "成功清理Gradio缓存": "Gradioのキャッシュを正常にクリアしました", "请上传至少一个文件": "少なくとも1つのファイルをアップロードしてください", "单声道": "モノラル", "处理完成, 成功转换: ": "処理完了、正常に変換された数: ", "请先下载SOME预处理模型并放置在tools/SOME_weights文件夹下! ": "まず、SOME前処理モデルをダウンロードし、tools/SOME_weightsフォルダに配置してください！", "请先选择模型保存路径! ": "まずモデル保存パスを選択してください！", "初始模型": "初期モデル", "模型类型错误, 请重新选择": "モデルタイプが間違っています、再選択してください", "配置文件不存在, 请重新选择": "設定ファイルが存在しません、再選択してください", "数据集路径不存在, 请重新选择": "データセットパスが存在しません、再選択してください", "验证集路径不存在, 请重新选择": "検証セットのパスが存在しません、再選択してください", "数据集类型错误, 请重新选择": "データセットタイプが間違っています、再選択してください", "训练启动成功! 请前往控制台查看训练信息! ": "トレーニングが正常に開始されました！トレーニング情報はコンソールで確認してください！", "模型不存在, 请重新选择": "モデルが存在しません、再選択してください", "验证完成! 请打开输出文件夹查看详细结果": "検証完了！詳細な結果は出力フォルダを開いて確認してください", "错误: 无法找到增强配置文件模板, 请检查文件configs/augmentations_template.yaml是否存在。": "エラー: 増強設定ファイルテンプレートが見つかりません、configs/augmentations_template.yamlファイルが存在するか確認してください。", "已开启调试日志": "デバッグログが有効になりました", "已关闭调试日志": "デバッグログが無効になりました", "模型不存在!": "モデルが存在しません！", "输入音频分离": "入力オーディオ分離", "输入文件夹分离": "入力フォルダの分離", "请先选择文件夹!": "まずフォルダを選択してください！", "显存不足, 请尝试减小batchsize值和chunksize值后重试。": "ビデオメモリが不足しています。batchsize値とchunksize値を減らして再試行してください。", "内存不足，请尝试增大虚拟内存后重试。若分离时出现此报错，也可尝试将推理音频裁切短一些，分段分离。": "メモリが不足しています。仮想メモリを増やして再試行してください。分離時にこのエラーが発生した場合、推論オーディオを短くカットしてセグメントで分離を試みることもできます。", "FFmpeg未找到，请检查FFmpeg是否正确安装。若使用的是整合包，请重新安装。": "FFmpegが見つかりません。FFmpegが正しくインストールされているか確認してください。統合パッケージを使用している場合は、再インストールしてください。", "模型损坏，请重新下载并安装模型后重试。": "モデルが破損しています。モデルを再ダウンロードしてインストールしてから再試行してください。", "文件或路径不存在，请根据错误指示检查是否存在该文件。": "ファイルまたはパスが存在しません。エラー指示に従ってそのファイルが存在するか確認してください。", "合奏模式可用于集成不同算法的结果, 具体的文档位于/docs/ensemble.md。目前主要有以下两种合奏方式:<br>1. 从原始音频合奏: 直接上传一个或多个音频文件, 然后选择多个模型进行处理, 将这些处理结果根据选择的合奏模式进行合奏<br>2. 从分离结果合奏: 上传多个已经分离完成的结果音频, 然后选择合奏模式进行合奏": "合奏モードは異なるアルゴリズムの結果を統合するために使用できます。詳細なドキュメントは/docs/ensemble.mdにあります。現在主に次の2種類の合奏方法があります：<br>1. 元の音声からの合奏：1つ以上の音声ファイルをアップロードし、複数のモデルを選択して処理し、選択した合奏モードに基づいて結果を合奏します。<br>2. 分離結果からの合奏：複数の分離済みの結果音声をアップロードし、合奏モードを選択して合奏します。", "从原始音频合奏": "元の音声からの合奏", "从原始音频合奏需要上传至少一个音频文件, 然后选择多个模型先进行分离处理, 然后将这些处理结果根据选择的合奏模式进行合奏。<br>注意, 请确保你的磁盘空间充足, 合奏过程会产生的临时文件仅会在处理结束后删除。": "元の音声からの合奏には、少なくとも1つの音声ファイルをアップロードし、複数のモデルを選択して最初に分離処理を行い、その後選択した合奏モードに基づいて結果を合奏します。<br>注意：ディスク容量を十分に確保してください。合奏プロセスでは一時ファイルが生成され、処理終了後にのみ削除されます。", "制作合奏流程": "合奏プロセスを作成する", "权重": "重み", "添加到合奏流程": "合奏プロセスに追加", "撤销上一步": "前の操作を元に戻す", "全部清空": "すべてクリア", "合奏流程": "合奏プロセス", "保存此合奏流程": "この合奏プロセスを保存", "集成模式": "統合モード", "输出格式": "出力形式", "使用CPU (注意: 使用CPU会导致速度非常慢) ": "CPUを使用する（注意: CPUを使用すると非常に遅くなります）", "使用TTA (测试时增强), 可能会提高质量, 但时间x3": "TTA（テスト時に強化）を使用すると、品質が向上する可能性がありますが、速度が少し遅い", "输出次级音轨 (例如: 合奏人声时, 同时输出伴奏)": "副音軌を出力する (例: 人声を合奏するとき、伴奏も同時に出力)", "输入音频": "入力オーディオ", "上传一个或多个音频文件": "1つ以上の音声ファイルをアップロード", "输入文件夹": "フォルダを入力", "输入目录": "入力ディレクトリ", "选择文件夹": "フォルダを選択", "打开文件夹": "フォルダを開く", "输出目录": "出力ディレクトリ", "从分离结果合奏": "分離結果から合奏", "从分离结果合奏需要上传至少两个音频文件, 这些音频文件是使用不同的模型分离同一段音频的结果。因此, 上传的所有音频长度应该相同。": "分離結果から合奏するには、少なくとも2つの音声ファイルをアップロードする必要があります。これらの音声ファイルは、異なるモデルを使用して同じ音声を分離した結果です。そのため、アップロードするすべての音声の長さは同じである必要があります。", "上传多个音频文件": "複数のオーディオファイルをアップロードする", "权重(以空格分隔, 数量要与上传的音频一致)": "重み（スペースで区切り、アップロードされたオーディオと一致する必要があります）", "运行": "実行", "强制停止": "強制停止", "### 集成模式": "### 統合モード", "1. `avg_wave`: 在1D变体上进行集成, 独立地找到波形的每个样本的平均值<br>2. `median_wave`: 在1D变体上进行集成, 独立地找到波形的每个样本的中位数<br>3. `min_wave`: 在1D变体上进行集成, 独立地找到波形的每个样本的最小绝对值<br>4. `max_wave`: 在1D变体上进行集成, 独立地找到波形的每个样本的最大绝对值<br>5. `avg_fft`: 在频谱图 (短时傅里叶变换 (STFT) 2D变体) 上进行集成, 独立地找到频谱图的每个像素的平均值。平均后使用逆STFT得到原始的1D波形<br>6. `median_fft`: 与avg_fft相同, 但使用中位数代替平均值 (仅在集成3个或更多来源时有用) <br>7. `min_fft`: 与avg_fft相同, 但使用最小函数代替平均值 (减少激进程度) <br>8. `max_fft`: 与avg_fft相同, 但使用最大函数代替平均值 (增加激进程度) ": "1. `avg_wave`: 1Dバリアントでアンサンブルし、波形の各サンプルの平均値を個別に求めます<br>2. `median_wave`: 1Dバリアントでアンサンブルし、波形の各サンプルの中央値を個別に求めます<br>3. `min_wave`: 1Dバリアントでアンサンブルし、波形の各サンプルの最小絶対値を個別に求めます<br>4. `max_wave`: 1Dバリアントでアンサンブルし、波形の各サンプルの最大絶対値を個別に求めます<br>5. `avg_fft`: スペクトログラム（短時間フーリエ変換（STFT）2Dバリアント）でアンサンブルし、スペクトログラムの各ピクセルの平均値を個別に求めます。平均後、逆STFTを使用して元の1D波形を取得します<br>6. `median_fft`: avg_fftと同じですが、平均値の代わりに中央値を使用します（3つ以上のソースを統合する場合にのみ有用）<br>7. `min_fft`: avg_fftと同じですが、平均値の代わりに最小関数を使用します（攻撃性を減少）<br>8. `max_fft`: avg_fftと同じですが、平均値の代わりに最大関数を使用します（攻撃性を増加）", "### 注意事项": "### 注意事項", "1. min_fft可用于进行更保守的合成, 它将减少更激进模型的影响。<br>2. 最好合成等质量的模型。在这种情况下, 它将带来增益。如果其中一个模型质量不好, 它将降低整体质量。<br>3. 在原仓库作者的实验中, 与其他方法相比, avg_wave在SDR分数上总是更好或相等。<br>4. 最终会在输出目录下生成一个`ensemble_<集成模式>.wav`。": "1. min_fftは、より保守的な合成に使用でき、攻撃的なモデルの影響を減少させます。<br>2. 同等品質のモデルを合成するのが最善です。その場合、利点が得られます。もし1つのモデルの品質が悪ければ、全体の品質が低下します。<br>3. 原リポジトリの作者の実験では、他の方法と比較して、avg_waveはSDRスコアで常に優れているか、または同等です。<br>4. 最終的には、出力ディレクトリに`ensemble_<統合モード>.wav`が生成されます。", "下载官方模型": "公式モデルをダウンロード", "点击打开下载管理器": "ダウンロードマネージャを開く", "模型信息": "モデル情報", "打开模型目录": "モデルディレクトリを開く", "自动下载": "自動ダウンロード", "手动下载": "手動ダウンロード", "1. MSST模型默认下载在pretrain/<模型类型>文件夹下。UVR模型默认下载在设置中的UVR模型目录中。<br>2. 下加载进度可以打开终端查看。如果一直卡着不动或者速度很慢, 在确信网络正常的情况下请尝试重启WebUI。<br>3. 若下载失败, 会在模型目录**留下一个损坏的模型**, 请**务必**打开模型目录手动删除! <br>4. 点击“重启WebUI”按钮后, 会短暂性的失去连接, 随后会自动开启一个新网页。": "1. MSSTモデルはデフォルトでpretrain/<モデルタイプ>フォルダにダウンロードされます。UVRモデルは設定のUVRモデルディレクトリにデフォルトでダウンロードされます。<br>2. ロード進捗はターミナルで確認できます。もしずっと止まっているか、速度が非常に遅い場合、ネットワークが正常であることを確認した上でWebUIを再起動してください。<br>3. ダウンロードが失敗した場合、モデルディレクトリに**壊れたモデル**が残りますので、**必ず**モデルディレクトリを開いて手動で削除してください！<br>4. 「WebUIを再起動」ボタンをクリックすると、一時的に接続が失われ、その後新しいページが自動的に開きます。", "### 模型下载链接": "### モデルダウンロードリンク", "1. 自动从Github, Huggingface或镜像站下载模型。<br>2. 你也可以在此整合包下载链接中的All_Models文件夹中找到所有可用的模型并下载。": "1. <PERSON><PERSON><PERSON>、<PERSON><PERSON><PERSON>、またはミラーサイトから自動でモデルをダウンロードします。<br>2. この統合パッケージのダウンロードリンク内のAll_Modelsフォルダで、利用可能なすべてのモデルを見つけてダウンロードすることもできます。", "若自动下载出现报错或下载过慢, 请点击手动下载, 跳转至下载链接。手动下载完成后, 请根据你选择的模型类型放置到对应文件夹内。": "自動ダウンロードでエラーが発生するか、ダウンロードが遅い場合は、手動ダウンロードをクリックしてダウンロードリンクに移動してください。手動ダウンロードが完了したら、選択したモデルタイプに応じて対応するフォルダに配置してください。", "### 当前UVR模型目录: ": "### 現在のUVRモデルディレクトリ:", ", 如需更改, 请前往设置页面。": "、変更が必要な場合は、設定ページにアクセスしてください。", "### 模型安装完成后, 需重启WebUI刷新模型列表": "### モデルのインストールが完了した後、WebUIを再起動してモデルリストを更新する必要があります。", "重启WebUI": "WebUIを再起動", "安装非官方MSST模型": "非公式MSSTモデルをインストール", "你可以从其他途径获取非官方MSST模型, 在此页面完成配置文件设置后, 即可正常使用。<br>注意: 仅支持'.ckpt', '.th', '.chpt'格式的模型。模型显示名字为模型文件名。<br>选择模型类型: 共有三个可选项。依次代表人声相关模型, 多音轨分离模型, 单音轨分离模型。仅用于区分模型大致类型, 可任意选择。<br>选择模型类别: 此选项关系到模型是否能正常推理使用, 必须准确选择!": "非公式のMSSTモデルは他の方法で入手することができます。このページで設定ファイルの設定を完了すれば、正常に使用できます。<br>注意: '.ckpt', '.th', '.chpt'形式のモデルのみがサポートされています。モデル名はファイル名として表示されます。<br>モデルタイプの選択: 3つのオプションがあり、それぞれがボーカル関連モデル、多トラック分離モデル、シングルトラック分離モデルを表します。モデルの大まかなタイプを区別するためにのみ使用され、任意に選択できます。<br>モデルカテゴリーの選択: このオプションは、モデルが正常に推論に使用できるかどうかに関係しますので、正確に選択する必要があります！", "上传非官方MSST模型": "非公式MSSTモデルをアップロード", "上传非官方MSST模型配置文件": "非公式MSSTモデル設定ファイルをアップロード", "选择模型类别": "モデルカテゴリーを選択", "模型下载链接 (非必须，若无，可跳过)": "モデルダウンロードリンク (必須ではありません。無ければスキップ可能)", "安装非官方VR模型": "非公式VRモデルをインストール", "你可以从其他途径获取非官方UVR模型, 在此页面完成配置文件设置后, 即可正常使用。<br>注意: 仅支持'.pth'格式的模型。模型显示名字为模型文件名。": "非公式のUVRモデルは他の方法で入手することができます。このページで設定ファイルの設定を完了すれば、正常に使用できます。<br>注意: '.pth'形式のモデルのみがサポートされています。モデル名はファイル名として表示されます。", "上传非官方VR模型": "非公式VRモデルをアップロード", "主要音轨名称": "主要トラック名", "次要音轨名称": "副次トラック名", "选择模型参数": "モデルパラメーターを選択", "是否为Karaoke模型": "カラオケモデルですか", "是否为BV模型": "BVモデルですか", "是否为VR 5.1模型": "VR 5.1モデルですか", "MSST音频分离原项目地址: [https://github.com/ZFTurbo/Music-Source-Separation-Training](https://github.com/ZFTurbo/Music-Source-Separation-Training)": "MSST音声分離元プロジェクトのアドレス: [https://github.com/ZFTurbo/Music-Source-Separation-Training](https://github.com/ZFTurbo/Music-Source-Separation-Training)", "选择使用的GPU": "使用するGPUを選択", "强制使用CPU推理, 注意: 使用CPU推理速度非常慢!": "CPU推論を強制的に使用します。注意: CPU推論は非常に遅いです！", "使用CPU": "CPUを使用", "[点击展开] 推理参数设置, 不同模型之间参数相互独立": "[クリックして展開] 推論パラメータ設定、異なるモデル間でパラメータは相互に独立しています", "只有在点击保存后才会生效。参数直接写入配置文件, 无法撤销。假如不知道如何设置, 请保持默认值。<br>请牢记自己修改前的参数数值, 防止出现问题以后无法恢复。请确保输入正确的参数, 否则可能会导致模型无法正常运行。<br>假如修改后无法恢复, 请点击``重置``按钮, 这会使得配置文件恢复到默认值。": "保存をクリックした後にのみ有効になります。パラメータは設定ファイルに直接書き込まれ、元に戻すことはできません。設定方法がわからない場合は、デフォルト値を維持してください。<br>問題が発生した場合に戻せないように、変更前のパラメータを必ず覚えておいてください。正しいパラメータを入力してください。そうでないと、モデルが正常に動作しない可能性があります。<br>変更後に戻せない場合は、「リセット」ボタンをクリックしてください。これにより、設定ファイルがデフォルトに戻ります。", "批次大小, 减小此值可以降低显存占用, 此参数对推理效果影响不大": "バッチサイズ。この値を小さくするとGPUメモリ使用量を削減できます。このパラメータは推論結果にはほとんど影響しません。", "重叠数, 增大此值可以提高分离效果, 但会增加处理时间, 建议设置成4": "オーバーラップ数。この値を増やすと分離効果が向上しますが、処理時間が増加します。4に設定することをお勧めします。", "分块大小, 增大此值可以提高分离效果, 但会增加处理时间和显存占用": "分割サイズ。この値を増やすと分離効果が向上しますが、処理時間とGPUメモリ使用量が増加します。", "音频归一化, 对音频进行归一化输入和输出, 部分模型没有此功能": "音声の正規化。音声を正規化して入力および出力します。一部のモデルではこの機能がありません。", "启用TTA, 能小幅提高分离质量, 若使用, 推理时间x3": "TTAを有効にすると、分離品質がわずかに向上しますが、推論時間が3倍になります。", "保存配置": "設定を保存", "重置配置": "設定をリセット", "预设流程允许按照预设的顺序运行多个模型。每一个模型的输出将作为下一个模型的输入。": "プリセットフローでは、プリセットされた順序で複数のモデルを実行できます。各モデルの出力は次のモデルの入力として使用されます。", "使用预设": "プリセットを使用", "该模式下的UVR推理参数将直接沿用UVR分离页面的推理参数, 如需修改请前往UVR分离页面。<br>修改完成后, 还需要任意处理一首歌才能保存参数! ": "このモードでは、UVR分離ページの推論パラメータが直接使用されます。変更が必要な場合は、UVR分離ページに移動してください。<br>変更後、パラメータを保存するには、曲を1曲処理する必要があります。", "将次级输出保存至输出目录的单独文件夹内": "セカンダリ出力を出力ディレクトリの個別のフォルダに保存します", "制作预设": "プリセットを作成", "预设名称": "プリセット名", "请输入预设名称": "プリセット名を入力してください", "添加至流程": "フローに追加", "保存上述预设流程": "上記のプリセットフローを保存", "管理预设": "プリセット管理", "此页面提供查看预设, 删除预设, 备份预设, 恢复预设等功能<br>`model_type`: 模型类型；`model_name`: 模型名称；`input_to_next`: 作为下一模型输入的音轨；`output_to_storage`: 直接保存至输出目录下的direct_output文件夹内的音轨, **不会经过后续流程处理**<br>每次点击删除预设按钮时, 将自动备份预设以免误操作。": "このページでは、プリセットの表示、削除、バックアップ、復元などの機能を提供します<br>`model_type`: モデルの種類；`model_name`: モデル名；`input_to_next`: 次のモデル入力としての音声トラック；`output_to_storage`: 出力ディレクトリのdirect_outputフォルダに直接保存される音声トラック、**後続の処理を経ません**<br>削除プリセットボタンをクリックするたびに、誤操作を防ぐためにプリセットが自動的にバックアップされます。", "删除所选预设": "選択したプリセットを削除", "请先选择预设": "まずプリセットを選択してください", "恢复所选预设": "選択したプリセットを復元する", "打开备份文件夹": "バックアップフォルダを開く", "WebUI设置": "WebUI設定", "GPU信息": "GPU情報", "系统信息": "システム情報", "设置WebUI端口, 0为自动": "WebUIポートを設定します。0は自動", "选择语言": "言語を選択", "选择MSST模型下载链接": "MSSTモデルのダウンロードリンクを選択", "选择WebUI主题": "WebUIテーマを選択", "对本地局域网开放WebUI: 开启后, 同一局域网内的设备可通过'本机IP:端口'的方式访问WebUI。": "ローカルローカルエリアネットワークにWebUIを開放する：オンにすると、同じローカルエリアネットワーク内のデバイスは「ローカルIP:ポート」方式でWebUIにアクセスできる。", "开启公共链接: 开启后, 他人可通过公共链接访问WebUI。链接有效时长为72小时。": "パブリックリンクのオープン：オープンすると、他人はパブリックリンクを介してWebUIにアクセスできます。リンクの有効時間は72時間です。", "自动清理缓存: 开启后, 每次启动WebUI时会自动清理缓存。": "キャッシュの自動クリーンアップ：オンにすると、WebUIが起動するたびにキャッシュが自動的にクリーンアップされます。", "全局调试模式: 向开发者反馈问题时请开启。(该选项支持热切换)": "グローバルデバッグモード: 問題を開発者に報告する際に有効にしてください。（このオプションはホットスワップをサポートします）", "选择UVR模型目录": "UVRモデルディレクトリを選択", "检查更新": "更新を確認", ", 请点击检查更新按钮": "、更新確認ボタンをクリックしてください", "前往Github瞅一眼": "Githubで確認する", "重置WebUI路径记录": "WebUIパス記録をリセット", "重置WebUI设置": "WebUI設定をリセット", "### 选择UVR模型目录": "### UVRモデルディレクトリを選択", "如果你的电脑中有安装UVR5, 你不必重新下载一遍UVR5模型, 只需在下方“选择UVR模型目录”中选择你的UVR5模型目录, 定位到models/VR_Models文件夹。<br>例如: E:/Program Files/Ultimate Vocal Remover/models/VR_Models 点击保存设置或重置设置后, 需要重启WebUI以更新。": "UVR5がコンピュータにインストールされている場合、UVR5モデルを再ダウンロードする必要はありません。下の「UVRモデルディレクトリを選択」でUVR5モデルディレクトリを選択し、models/VR_Modelsフォルダーに移動してください。<br>例えば: E:/Program Files/Ultimate Vocal Remover/models/VR_Models 設定を保存またはリセットした後、WebUIを再起動して更新する必要があります。", "### 检查更新": "### 更新を確認", "从Github检查更新, 需要一定的网络要求。点击检查更新按钮后, 会自动检查是否有最新版本。你可以前往此整合包的下载链接或访问Github仓库下载最新版本。": "Githubから更新を確認するには、一定のネットワーク要件が必要です。更新確認ボタンをクリックすると、自動的に最新バージョンがあるかどうかが確認されます。この統合パッケージのダウンロードリンクにアクセスするか、Githubリポジトリで最新バージョンをダウンロードできます。", "### 重置WebUI路径记录": "### WebUIパス記録をリセット", "将所有输入输出目录重置为默认路径, 预设/模型/配置文件以及上面的设置等**不会重置**, 无需担心。重置WebUI设置后, 需要重启WebUI。": "すべての入力出力ディレクトリをデフォルトパスにリセットします。プリセット/モデル/設定ファイルおよび上記の設定などは**リセットされません**ので、心配する必要はありません。WebUI設定をリセットした後、WebUIを再起動する必要があります。", "### 重置WebUI设置": "### WebUI設定をリセット", "仅重置WebUI设置, 例如UVR模型路径, WebUI端口等。重置WebUI设置后, 需要重启WebUI。": "WebUI設定のみをリセットします。例えば、UVRモデルパス、WebUIポートなどです。WebUI設定をリセットした後、WebUIを再起動する必要があります。", "### 重启WebUI": "### WebUIを再起動", "点击 “重启WebUI” 按钮后, 会短暂性的失去连接, 随后会自动开启一个新网页。": "「WebUIを再起動」ボタンをクリックすると、一時的に接続が失われ、その後自動的に新しいウェブページが開きます。", "音频输出设置": "音声出力設定", "此页面支持用户自定义修改MSST/VR推理后输出音频的质量。输出音频的**采样率, 声道数与模型支持的参数有关, 无法更改**。<br>修改完成后点击保存设置即可生效。": "このページでは、MSST/VR推論後の音声出力品質をカスタマイズして変更できます。出力音声の**サンプリングレート、チャンネル数はモデルがサポートするパラメータに依存し、変更できません**。<br>変更後、[設定を保存] をクリックして有効にします。", "输出wav位深度": "出力wavのビット深度", "输出flac位深度": "出力flacのビット深度", "输出mp3比特率(bps)": "出力mp3のビットレート（bps）", "保存设置": "設定を保存", "模型改名": "モデルの名前変更", "此页面支持用户自定义修改模型名字, 以便记忆和使用。修改完成后, 需要重启WebUI以刷新模型列表。<br>【注意】此操作不可逆 (无法恢复至默认命名), 请谨慎命名。输入新模型名字时, 需保留后缀!": "このページでは、記憶して使用するためのモデル名の変更をユーザーがカスタマイズすることができます。変更が完了したら、モデルリストを更新するためにWebUIを再起動する必要があります。<br>【注意】この操作は可逆的ではありません（デフォルトの名前に戻すことはできません）。慎重に名前を付けてください。新しいモデル名を入力する場合は、接尾辞を保持する必要があります！", "新模型名": "新規モデル名", "请输入新模型名字, 需保留后缀!": "接尾辞を保持するには、新しいモデル名を入力してください！", "确认修改": "変更の確認", "立体声": "ステレオ", "音频格式转换": "音声フォーマット変換", "上传一个或多个音频文件并将其转换为指定格式。<br>支持的格式包括 .mp3, .flac, .wav, .ogg, .m4a, .wma, .aac...等等。<br>**不支持**网易云音乐/QQ音乐等加密格式, 如.ncm, .qmc等。": "1つ以上の音声ファイルをアップロードし、指定されたフォーマットに変換します。<br>サポートされているフォーマットには、.mp3, .flac, .wav, .ogg, .m4a, .wma, .aacなどがあります。<br>**暗号化フォーマットはサポートされていません**。", "选择或输入音频输出格式": "音声出力形式を選択または入力", "选择音频输出目录": "音声出力ディレクトリを選択", "输出音频采样率(Hz)": "出力オーディオのサンプリングレート（Hz）", "输出音频声道数": "出力オーディオのチャンネル数", "输出ogg比特率(bps)": "出力oggのビットレート（bps）", "转换音频": "音声を変換", "合并音频": "音声を結合", "点击合并音频按钮后, 将自动把输入文件夹中的所有音频文件合并为一整个音频文件<br>合并后的音频会保存至输出目录中, 文件名为merged_audio_<文件夹名字>.wav": "音声の結合ボタンをクリックすると、入力フォルダ内のすべての音声ファイルが自動的に1つの音声ファイルに結合されます。<br>結合された音声は出力ディレクトリにmerged_audio_<フォルダ名>.wavという名前で保存されます。", "计算SDR": "SDRを計算する", "上传两个**wav音频文件**并计算它们的[SDR](https://www.aicrowd.com/challenges/music-demixing-challenge-ismir-2021#evaluation-metric)。<br>SDR是一个用于评估模型质量的数值。数值越大, 模型算法结果越好。": "**wavオーディオファイル**を2つアップロードし、それらの[SDR](https://www.aicrowd.com/challenges/music-demixing-challenge-ismir-2021#evaluation-metric)を計算します。<br>SDRはモデルの品質を評価するための指標です。数値が大きいほど、モデルアルゴリズムの結果が優れています。", "参考音频": "参照音声", "待估音频": "評価対象の音声", "歌声转MIDI": "ボーカルをMIDIに変換", "歌声转MIDI功能使用开源项目[SOME](https://github.com/openvpi/SOME/), 可以将分离得到的**干净的歌声**转换成.mid文件。<br>【必须】若想要使用此功能, 请先下载权重文件[model_steps_64000_simplified.ckpt](https://hf-mirror.com/Sucial/MSST-WebUI/resolve/main/SOME_weights/model_steps_64000_simplified.ckpt)并将其放置在程序目录下的`tools/SOME_weights`文件夹内。文件命名不可随意更改!": "歌声をMIDIに変換する機能は、オープンソースプロジェクト[SOME](https://github.com/openvpi/SOME/)を使用します。分離された**きれいな歌声**を.midファイルに変換できます。<br>【必須】この機能を使用するには、まず重みファイル[model_steps_64000_simplified.ckpt](https://huggingface.co/Sucial/MSST-WebUI/resolve/main/SOME_weights/model_steps_64000_simplified.ckpt)をダウンロードし、プログラムディレクトリ内の`tools/SOME_weights`フォルダに配置してください。ファイル名を変更しないでください！", "如果不知道如何测量歌曲BPM, 可以尝试这两个在线测量工具: [bpmdetector](https://bpmdetector.kniffen.dev/) | [key-bpm-finder](https://vocalremover.org/zh/key-bpm-finder), 测量时建议上传原曲或伴奏, 若干声可能导致测量结果不准确。": "曲BPMの測定方法がわからない場合は、[bpmdetector](https://bpmdetector.kniffen.dev/) | [key-bpm-finder](https://vocalremover.org/zh/key-bpm-finder)、測定時に原曲や伴奏をアップロードすることを提案し、いくつかの音が測定結果を不正確にする可能性がある。", "上传音频": "音声をアップロード", "输入音频BPM": "オーディオのBPMを入力する", "开始转换": "変換を開始する", "1. 音频BPM (每分钟节拍数) 可以通过MixMeister BPM Analyzer等软件测量获取。<br>2. 为保证MIDI提取质量, 音频文件请采用干净清晰无混响底噪人声。<br>3. 输出MIDI不带歌词信息, 需要用户自行添加歌词。<br>4. 实际使用体验中部分音符会出现断开的现象, 需自行修正。SOME的模型主要面向DiffSinger唱法模型自动标注, 比正常用户在创作中需要的MIDI更加精细, 因而可能导致模型倾向于对音符进行切分。<br>5. 提取的MIDI没有量化/没有对齐节拍/不适配BPM, 需自行到各编辑器中手动调整。": "1. オーディオBPM（毎分ビート数）は、MixMeister BPM Analyzerなどのソフトウェアで測定できます。<br>2. MIDI抽出の品質を確保するために、オーディオファイルにはクリーンで明瞭なリバーブなしのボーカルを使用してください。<br>3. 出力されるMIDIには歌詞情報が含まれていないため、ユーザー自身で歌詞を追加する必要があります。<br>4. 実際の使用体験では、いくつかの音符が切断されることがあります。これを自分で修正する必要があります。SOMEのモデルは主にDiffSingerの歌唱モデルの自動ラベリングに焦点を当てているため、通常のユーザーが作成するMIDIよりも精密であるため、モデルが音符を分割する傾向があります。<br>5. 抽出されたMIDIには量子化/ビートに合わせること/ BPMの調整がされていないため、各エディターで手動で調整する必要があります。", "此页面提供数据集制作教程, 训练参数选择, 以及一键训练。有关配置文件的修改和数据集文件夹的详细说明请参考MSST原项目: [https://github.com/ZFTurbo/Music-Source-Separation-Training](https://github.com/ZFTurbo/Music-Source-Separation-Training)<br>在开始下方的模型训练之前, 请先进行训练数据的制作。<br>说明: 数据集类型即训练集制作Step 1中你选择的类型, 1: Type1; 2: Type2; 3: Type3; 4: Type4, 必须与你的数据集类型相匹配。": "このページでは、データセットの作成チュートリアル、トレーニングパラメーターの選択、およびワンクリックトレーニングを提供します。設定ファイルの変更やデータセットフォルダの詳細については、MSSTの元プロジェクトを参照してください: [https://github.com/ZFTurbo/Music-Source-Separation-Training](https://github.com/ZFTurbo/Music-Source-Separation-Training)<br>以下のモデルトレーニングを開始する前に、トレーニングデータの作成を行ってください。<br>説明: データセットタイプは、トレーニングセット作成のStep 1で選択したタイプです。1: Type1; 2: Type2; 3: Type3; 4: Type4で、データセットタイプと一致している必要があります。", "训练": "トレーニング", "选择训练模型类型": "トレーニングモデルの種類を選択", "配置文件路径": "設定ファイルパス", "请输入配置文件路径或选择配置文件": "設定ファイルパスを入力するか、設定ファイルを選択してください", "选择配置文件": "設定ファイルを選択", "数据集类型": "データセットの種類", "数据集路径": "データセットパス", "请输入或选择数据集文件夹": "データセットフォルダを入力または選択してください", "选择数据集文件夹": "データセットフォルダを選択", "验证集路径": "検証セットパス", "请输入或选择验证集文件夹": "検証セットフォルダを入力または選択してください", "选择验证集文件夹": "検証セットフォルダを選択", "模型保存路径": "モデル保存パス", "请输入或选择模型保存文件夹": "モデル保存フォルダを入力または選択してください", "选择初始模型, 若无初始模型, 留空或选择None即可": "初期モデルを選択します。初期モデルがない場合は、空白のままにするかNoneを選択してください。", "刷新初始模型列表": "初期モデルリストを更新", "训练参数设置": "トレーニングパラメータの設定", "num_workers: 数据集读取线程数, 0为自动": "num_workers: データセット読み取りスレッド数、0は自動", "随机数种子, 0为随机": "ランダムシード、0はランダム", "是否将加载的数据放置在固定内存中, 默认为否": "ロードされたデータを固定メモリに配置するかどうか、デフォルトはいいえ", "是否使用加速训练, 对于多显卡用户会加快训练": "Acceleration Trainingを使用するかどうかは、マルチグラフィックスカードユーザーにとって迅速なトレーニングになります", "是否在训练前验证模型, 默认为否": "訓練前にモデルを検証するかどうか、デフォルトはいいえ", "是否使用MultiSTFT Loss, 默认为否": "MultiSTFT Lossを使用するかどうか、デフォルトはいいえ", "是否使用MSE loss, 默认为否": "MSE Lossを使用するかどうか、デフォルトはいいえ", "是否使用L1 loss, 默认为否": "L1 Lossを使用するかどうか、デフォルトはいいえ", "选择输出的评估指标": "出力する評価指標を選択", "选择调度器使用的评估指标": "スケジューラが使用する評価指標を選択", "保存上述训练配置": "上記のトレーニング設定を保存", "开始训练": "トレーニングを開始", "点击开始训练后, 请到终端查看训练进度或报错, 下方不会输出报错信息, 想要停止训练可以直接关闭终端。在训练过程中, 你也可以关闭网页, 仅**保留终端**。": "トレーニングを開始した後、ターミナルでトレーニングの進行状況やエラーメッセージを確認してください。以下にはエラーメッセージは出力されません。トレーニングを停止したい場合は、ターミナルを直接閉じてください。トレーニング中に、ウェブページを閉じて**ターミナルを保持する**ことができます。", "验证": "検証", "此页面用于手动验证模型效果, 测试验证集, 输出SDR测试信息。输出的信息会存放在输出文件夹的results.txt中。<br>下方参数将自动加载训练页面的参数, 在训练页面点击保存训练参数后, 重启WebUI即可自动加载。当然你也可以手动输入参数。<br>": "このページはモデルの効果を手動で検証し、検証セットをテストし、SDRテスト情報を出力するためのものです。出力された情報は出力フォルダーのresults.txtに保存されます。<br>以下のパラメーターは、トレーニングページのパラメーターを自動的に読み込み、トレーニングページでトレーニングパラメーターを保存した後、WebUIを再起動することで自動的に読み込まれます。もちろん、手動でパラメーターを入力することもできます。<br>", "模型路径": "モデルパス", "请输入或选择模型文件": "モデルファイルを入力または選択してください", "选择模型文件": "モデルファイルを選択してください", "验证参数设置": "検証パラメータの設定", "选择验证集音频格式": "検証セットのオーディオフォーマットを選択してください", "验证集读取线程数, 0为自动": "検証セット読み取りスレッド数、0は自動", "开始验证": "検証を開始する", "训练集制作指南": "トレーニングセット作成ガイド", "Step 1: 数据集制作": "ステップ 1: データセット作成", "请**任选下面四种类型之一**制作数据集文件夹, 并按照给出的目录层级放置你的训练数据。完成后, 记录你的数据集**文件夹路径**以及你选择的**数据集类型**, 以便后续使用。": "以下の4つのタイプのいずれかを**選択して**データセットフォルダーを作成し、指定されたディレクトリ階層にトレーニングデータを配置してください。作成が完了したら、データセットの**フォルダーパス**と選択した**データセットタイプ**を記録して、後で使用できるようにしてください。", "不同的文件夹。每个文件夹包含所需的所有stems, 格式为stem_name.wav。与MUSDBHQ18数据集相同。在最新的代码版本中, 可以使用flac替代wav。<br>例如: ": "異なるフォルダー。各フォルダーには、必要なすべてのステムが含まれており、形式はstem_name.wavです。MUSDBHQ18データセットと同じです。最新のコードバージョンでは、wavの代わりにflacを使用できます。<br>例えば:", "每个文件夹是stem_name。文件夹中包含仅由所需stem组成的wav文件。<br>例如: ": "各フォルダーはstem_nameです。フォルダーには、必要なステムのみで構成されるwavファイルが含まれています。<br>例えば:", "可以提供以下结构的CSV文件 (或CSV文件列表) <br>例如: ": "以下の構造のCSVファイル（またはCSVファイルのリスト）を提供できます。<br>例えば:", "与类型1相同, 但在训练过程中所有乐器都将来自歌曲的相同位置。<br>例如: ": "タイプ1と同じですが、トレーニング中はすべての楽器が曲の同じ位置から来ることになります。<br>例えば:", "Step 2: 验证集制作": "ステップ 2: 検証セット作成", "验证集制作。验证数据集**必须**与上面数据集制作的Type 1(MUSDB)数据集**结构相同** (**无论你使用哪种类型的数据集进行训练**) , 此外每个文件夹还必须包含每首歌的mixture.wav, mixture.wav是所有stem的总和<br>例如: ": "検証セット作成。検証データセットは、上記のデータセット作成タイプ1（MUSDB）データセットと**構造が同じでなければなりません**（**どのタイプのデータセットを使用してトレーニングを行っても**）、さらに各フォルダーには、各曲のmixture.wavが含まれている必要があります。mixture.wavはすべてのステムの合計です。<br>例えば:", "Step 3: 选择并修改修改配置文件": "ステップ 3: 設定ファイルの選択と修正", "请先明确你想要训练的模型类型, 然后选择对应的配置文件进行修改。<br>目前有以下几种模型类型: ": "まず、トレーニングしたいモデルのタイプを明確にし、それに対応する設定ファイルを選択して修正してください。<br>現在、以下のモデルタイプがあります:", "<br>确定好模型类型后, 你可以前往整合包根目录中的configs_backup文件夹下找到对应的配置文件模板。复制一份模板, 然后根据你的需求进行修改。修改完成后记下你的配置文件路径, 以便后续使用。<br>特别说明: config_musdb18_xxx.yaml是针对MUSDB18数据集的配置文件。<br>": "<br>モデルタイプを決定した後、統合パッケージのルートディレクトリにあるconfigs_backupフォルダーに移動し、対応する設定ファイルテンプレートを見つけることができます。テンプレートをコピーし、ニーズに応じて修正してください。修正が完了したら、設定ファイルのパスを記録し、後で使用できるようにしてください。<br>特記: config_musdb18_xxx.yamlはMUSDB18データセット用の設定ファイルです。<br>", "打开配置文件模板文件夹": "設定ファイルテンプレートフォルダーを開く", "你可以使用下表根据你的GPU选择用于训练的BS_Roformer模型的batch_size参数。表中提供的批量大小值适用于单个GPU。如果你有多个GPU, 则需要将该值乘以GPU的数量。": "以下の表を使用して、GPUに応じてトレーニング用のBS_Roformerモデルのbatch_sizeパラメーターを選択できます。表に示されたバッチサイズの値は単一のGPUに適しています。複数のGPUがある場合は、その値をGPUの数だけ掛ける必要があります。", "Step 4: 数据增强": "ステップ 4: データ拡張", "数据增强可以动态更改stem, 通过从旧样本创建新样本来增加数据集的大小。现在, 数据增强的控制在配置文件中进行。下面是一个包含所有可用数据增强的完整配置示例。你可以将其复制到你的配置文件中以使用数据增强。<br>注意:<br>1. 要完全禁用所有数据增强, 可以从配置文件中删除augmentations部分或将enable设置为false。<br>2. 如果要禁用某些数据增强, 只需将其设置为0。<br>3. all部分中的数据增强应用于所有stem。<br>4. vocals/bass等部分中的数据增强仅应用于相应的stem。你可以为training.instruments中给出的所有stem创建这样的部分。": "データ拡張はstemを動的に変更し、古いサンプルから新しいサンプルを作成することでデータセットのサイズを増加させることができます。現在、データ拡張の制御は設定ファイルで行われます。以下は、すべての利用可能なデータ拡張を含む完全な設定例です。これを設定ファイルにコピーしてデータ拡張を使用できます。<br>注意:<br>1. すべてのデータ拡張を完全に無効にするには、設定ファイルからaugmentationsセクションを削除するか、enableをfalseに設定します。<br>2. 特定のデータ拡張を無効にするには、その値を0に設定するだけです。<br>3. allセクションのデータ拡張はすべてのstemに適用されます。<br>4. vocals/bassなどのセクションのデータ拡張は、対応するstemにのみ適用されます。training.instrumentsで指定されたすべてのstemのためにこのようなセクションを作成できます。", "说明: 本整合包仅融合了UVR的VR Architecture模型, MDX23C和HtDemucs类模型可以直接使用前面的MSST音频分离。<br>UVR分离使用项目: [https://github.com/nomadkaraoke/python-audio-separator](https://github.com/nomadkaraoke/python-audio-separator) 并进行了优化。": "説明: この統合パッケージにはUVRのVR Architectureモデルのみが統合されています。MDX23CとHtDemucsタイプのモデルは、前述のMSSTオーディオ分離を直接使用できます。<br>UVR分離プロジェクト: [https://github.com/nomadkaraoke/python-audio-separator](https://github.com/nomadkaraoke/python-audio-separator) さらに最適化されています。", "Window Size: 窗口大小, 用于平衡速度和质量, 默认为512": "ウィンドウサイズ: 速度と品質のバランスを取るために使用します。デフォルトは512です。", "Aggression: 主干提取强度, 范围-100-100, 人声请选5": "Aggression: メインエクストラクションの強度、範囲-100-100、ボーカルは5を選択", "[点击展开] 以下是一些高级设置, 一般保持默认即可": "[クリックして展開] 以下は高度な設定です。通常はデフォルトのままで使用してください。", "批次大小, 减小此值可以降低显存占用": "バッチサイズ。この値を小さくするとGPUメモリ使用量を削減できます。", "后处理特征阈值, 取值为0.1-0.3, 默认0.2": "後処理特徴のしきい値。値は0.1-0.3、デフォルトは0.2です。", "次级输出使用频谱而非波形进行反转, 可能会提高质量, 但速度稍慢": "副出力は波形ではなくスペクトルを使用して逆変換します。品質が向上する可能性がありますが、速度がやや遅くなります。", "启用“测试时增强”, 可能会提高质量, 但速度稍慢": "「テスト時の強化」を有効にすると、品質が向上する可能性がありますが、速度がやや遅くなります。", "将输出音频缺失的频率范围镜像输出, 作用不大": "出力音声の欠落している周波数範囲をミラーリング出力しますが、効果はほとんどありません。", "识别人声输出中残留的人工痕迹, 可改善某些歌曲的分离效果": "人声出力に残る人工的な痕跡を識別し、一部の曲の分離効果を改善できます。", "正在启动WebUI, 请稍等...": "WebUIを起動中です、お待ちください...", "若启动失败, 请尝试以管理员身份运行此程序": "起動に失敗した場合は、このプログラムを管理者として実行してみてください", "WebUI运行过程中请勿关闭此窗口!": "WebUIが実行中にこのウィンドウを閉じないでください！", "检测到CUDA, 设备信息: ": "CUDAが検出されました、デバイス情報: ", "使用MPS": "MPSを使用", "检测到MPS, 使用MPS": "MPSが検出されました、MPSを使用します", "无可用的加速设备, 使用CPU": "利用可能な加速デバイスがありません、CPUを使用", "\\033[33m未检测到可用的加速设备, 使用CPU\\033[0m": "\\033[33m利用可能な加速デバイスが検出されません、CPUを使用します\\033[0m", "\\033[33m如果你使用的是NVIDIA显卡, 请更新显卡驱动至最新版后重试\\033[0m": "\\033[33mNVIDIAグラフィックカードを使用している場合は、ドライバを最新バージョンに更新して再試行してください\\033[0m", "模型下载失败, 请重试!": "モデルのダウンロードに失敗しました、もう一度お試しください！", "作者: [Github@KitsuneX07](https://github.com/KitsuneX07) | [Github@SUC-DriverOld](https://github.com/SUC-DriverOld), [点击前往教程文档](https://r1kc63iz15l.feishu.cn/wiki/JSp3wk7zuinvIXkIqSUcCXY1nKc)": "作者: [Github@KitsuneX07](https://github.com/KitsuneX07) | [Github@SUC-DriverOld](https://github.com/SUC-DriverOld)", "**请将需要处理的音频放置到input文件夹内, 处理完成后的音频将会保存到results文件夹内! 云端输入输出目录不可更改!**": "**処理するオーディオをinputフォルダに配置し、処理が完了するとオーディオはresultsフォルダに保存されます！クラウドの入出力ディレクトリは変更できません！**", "文件管理": "ファイル管理", "文件管理页面是云端WebUI特有的页面, 用于上传, 下载, 删除文件。<br>1. 上传文件: 将文件上传到input文件夹内。可以勾选是否自动解压zip文件<br>2. 下载文件: 以zip格式打包results文件夹内的文件, 输出至WebUI以供下载。注意: 打包不会删除results文件夹, 若打包后不再需要分离结果, 请点击按钮手动删除。<br>3. 删除文件: 删除input和results文件夹内的文件。": "ファイル管理ページはクラウドWebUI特有のページで、ファイルのアップロード、ダウンロード、削除を行います。<br>1. ファイルのアップロード: ファイルをinputフォルダにアップロードします。zipファイルの自動解凍を選択することができます。<br>2. ファイルのダウンロード: resultsフォルダ内のファイルをzip形式でパッケージ化し、WebUIでダウンロードできるように出力します。注意: パッケージ化はresultsフォルダを削除しません。パッケージ化後に結果を分離する必要がない場合は、ボタンをクリックして手動で削除してください。<br>3. ファイルの削除: inputおよびresultsフォルダ内のファイルを削除します。", "删除input文件夹内所有文件": "inputフォルダ内のすべてのファイルを削除", "删除results文件夹内所有文件": "resultsフォルダ内のすべてのファイルを削除", "刷新input和results文件列表": "inputおよびresultsフォルダのファイルリストを更新", "打包results文件夹内所有文件": "resultsフォルダ内のすべてのファイルをパッケージ化", "上传一个或多个文件至input文件夹": "1つ以上のファイルをinputフォルダにアップロード", "自动解压zip文件(仅支持zip, 压缩包内文件名若含有非ASCII字符, 解压后文件名可能为乱码)": "zipファイルを自動解凍(対応しているのはzipのみ、圧縮ファイル内のファイル名に非ASCII文字が含まれている場合、解凍後のファイル名が文字化けすることがあります)", "上传文件": "ファイルのアップロード", "input和results文件列表": "inputおよびresultsフォルダのファイルリスト", "请先点击刷新按钮": "最初に更新ボタンをクリックしてください", "下载results文件夹内所有文件": "resultsフォルダ内のすべてのファイルをダウンロード", "Window Size: 窗口大小, 用于平衡速度和质量": "Window Size: ウィンドウサイズ、速度と品質のバランスに使用", "初始模型: 继续训练或微调模型训练时, 请选择初始模型, 否则将从头开始训练! ": "初期モデル: トレーニングを続行するか、モデルを微調整する場合は、初期モデルを選択してください。それ以外の場合は、最初からトレーニングを開始します。", "点击合并音频按钮后, 将自动把输入文件夹中的所有音频文件合并为一整个音频文件<br>目前支持的格式包括 .mp3, .flac, .wav, .ogg, m4a 这五种<br>合并后的音频会保存至输出目录中, 文件名为merged_audio_<文件夹名字>.wav": "音声の結合ボタンをクリックすると、入力フォルダ内のすべての音声ファイルが自動的に1つの音声ファイルに結合されます<br>現在サポートされている形式は .mp3、.flac、.wav、.ogg、m4a の5種類です<br>結合された音声は出力ディレクトリにmerged_audio_<フォルダ名>.wavという名前で保存されます。"}