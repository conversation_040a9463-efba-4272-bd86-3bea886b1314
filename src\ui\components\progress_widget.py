"""
进度显示组件
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QProgressBar, 
    QPushButton, QFrame, QTextEdit
)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QFont

from ...utils.constants import Colors, Styles


class ProgressWidget(QWidget):
    """进度显示组件"""
    
    # 信号
    cancel_requested = Signal()  # 取消请求信号
    
    def __init__(self):
        super().__init__()
        self.current_task = None
        self.is_processing = False
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(Styles.MARGIN_NORMAL)
        
        # 任务信息区域
        self.create_task_info_section(layout)
        
        # 进度条区域
        self.create_progress_section(layout)
        
        # 详细信息区域
        self.create_details_section(layout)
        
        # 控制按钮区域
        self.create_control_section(layout)
        
        # 初始状态为隐藏
        self.setVisible(False)
        
    def create_task_info_section(self, layout):
        """创建任务信息区域"""
        info_frame = QFrame()
        info_frame.setStyleSheet(f"""
        QFrame {{
            background-color: {Colors.SECONDARY_BG};
            border: 1px solid {Colors.BORDER};
            border-radius: {Styles.INPUT_RADIUS};
            padding: {Styles.PADDING_NORMAL}px;
        }}
        """)
        
        info_layout = QVBoxLayout(info_frame)
        info_layout.setContentsMargins(0, 0, 0, 0)
        info_layout.setSpacing(Styles.MARGIN_SMALL)
        
        # 任务标题
        self.task_title_label = QLabel("处理任务")
        self.task_title_label.setStyleSheet(f"""
        QLabel {{
            color: {Colors.FOREGROUND};
            font-weight: bold;
            font-size: {Styles.FONT_SIZE_LARGE}px;
        }}
        """)
        info_layout.addWidget(self.task_title_label)
        
        # 当前步骤
        self.current_step_label = QLabel("准备中...")
        self.current_step_label.setStyleSheet(f"""
        QLabel {{
            color: {Colors.SECONDARY_TEXT};
            font-size: {Styles.FONT_SIZE_NORMAL}px;
        }}
        """)
        info_layout.addWidget(self.current_step_label)
        
        layout.addWidget(info_frame)
        
    def create_progress_section(self, layout):
        """创建进度条区域"""
        progress_frame = QFrame()
        progress_layout = QVBoxLayout(progress_frame)
        progress_layout.setContentsMargins(0, 0, 0, 0)
        progress_layout.setSpacing(Styles.MARGIN_SMALL)
        
        # 总体进度
        overall_layout = QHBoxLayout()
        overall_label = QLabel("总体进度:")
        overall_label.setStyleSheet(f"""
        QLabel {{
            color: {Colors.FOREGROUND};
            font-size: {Styles.FONT_SIZE_NORMAL}px;
            min-width: 80px;
        }}
        """)
        
        self.overall_progress = QProgressBar()
        self.overall_progress.setRange(0, 100)
        self.overall_progress.setValue(0)
        self.overall_progress.setStyleSheet(f"""
        QProgressBar {{
            border: 1px solid {Colors.BORDER};
            border-radius: {Styles.INPUT_RADIUS};
            background-color: {Colors.SECONDARY_BG};
            text-align: center;
            color: {Colors.FOREGROUND};
            font-size: {Styles.FONT_SIZE_SMALL}px;
            height: 20px;
        }}
        QProgressBar::chunk {{
            background-color: {Colors.ACCENT};
            border-radius: {Styles.INPUT_RADIUS};
        }}
        """)
        
        self.overall_percent_label = QLabel("0%")
        self.overall_percent_label.setStyleSheet(f"""
        QLabel {{
            color: {Colors.ACCENT};
            font-weight: bold;
            font-size: {Styles.FONT_SIZE_NORMAL}px;
            min-width: 40px;
        }}
        """)
        
        overall_layout.addWidget(overall_label)
        overall_layout.addWidget(self.overall_progress)
        overall_layout.addWidget(self.overall_percent_label)
        progress_layout.addLayout(overall_layout)
        
        # 当前步骤进度
        step_layout = QHBoxLayout()
        step_label = QLabel("当前步骤:")
        step_label.setStyleSheet(f"""
        QLabel {{
            color: {Colors.FOREGROUND};
            font-size: {Styles.FONT_SIZE_NORMAL}px;
            min-width: 80px;
        }}
        """)
        
        self.step_progress = QProgressBar()
        self.step_progress.setRange(0, 100)
        self.step_progress.setValue(0)
        self.step_progress.setStyleSheet(f"""
        QProgressBar {{
            border: 1px solid {Colors.BORDER};
            border-radius: {Styles.INPUT_RADIUS};
            background-color: {Colors.SECONDARY_BG};
            text-align: center;
            color: {Colors.FOREGROUND};
            font-size: {Styles.FONT_SIZE_SMALL}px;
            height: 16px;
        }}
        QProgressBar::chunk {{
            background-color: {Colors.SUCCESS};
            border-radius: {Styles.INPUT_RADIUS};
        }}
        """)
        
        self.step_percent_label = QLabel("0%")
        self.step_percent_label.setStyleSheet(f"""
        QLabel {{
            color: {Colors.SUCCESS};
            font-weight: bold;
            font-size: {Styles.FONT_SIZE_NORMAL}px;
            min-width: 40px;
        }}
        """)
        
        step_layout.addWidget(step_label)
        step_layout.addWidget(self.step_progress)
        step_layout.addWidget(self.step_percent_label)
        progress_layout.addLayout(step_layout)
        
        layout.addWidget(progress_frame)
        
    def create_details_section(self, layout):
        """创建详细信息区域"""
        details_frame = QFrame()
        details_frame.setStyleSheet(f"""
        QFrame {{
            background-color: {Colors.SECONDARY_BG};
            border: 1px solid {Colors.BORDER};
            border-radius: {Styles.INPUT_RADIUS};
        }}
        """)
        
        details_layout = QVBoxLayout(details_frame)
        details_layout.setContentsMargins(Styles.PADDING_SMALL, Styles.PADDING_SMALL,
                                         Styles.PADDING_SMALL, Styles.PADDING_SMALL)
        
        # 详细信息标题
        details_title = QLabel("处理详情:")
        details_title.setStyleSheet(f"""
        QLabel {{
            color: {Colors.FOREGROUND};
            font-weight: bold;
            font-size: {Styles.FONT_SIZE_NORMAL}px;
        }}
        """)
        details_layout.addWidget(details_title)
        
        # 详细信息文本
        self.details_text = QTextEdit()
        self.details_text.setMaximumHeight(100)
        self.details_text.setReadOnly(True)
        self.details_text.setStyleSheet(f"""
        QTextEdit {{
            background-color: {Colors.INPUT_BG};
            border: 1px solid {Colors.BORDER};
            border-radius: {Styles.INPUT_RADIUS};
            padding: {Styles.PADDING_SMALL}px;
            color: {Colors.FOREGROUND};
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: {Styles.FONT_SIZE_SMALL}px;
        }}
        """)
        details_layout.addWidget(self.details_text)
        
        layout.addWidget(details_frame)
        
    def create_control_section(self, layout):
        """创建控制按钮区域"""
        control_frame = QFrame()
        control_layout = QHBoxLayout(control_frame)
        control_layout.setContentsMargins(0, 0, 0, 0)
        
        # 时间信息
        self.time_info_label = QLabel("预计剩余时间: --")
        self.time_info_label.setStyleSheet(f"""
        QLabel {{
            color: {Colors.SECONDARY_TEXT};
            font-size: {Styles.FONT_SIZE_SMALL}px;
        }}
        """)
        
        # 取消按钮
        self.cancel_button = QPushButton("取消处理")
        self.cancel_button.setStyleSheet(f"""
        QPushButton {{
            background-color: {Colors.ERROR};
            border: none;
            border-radius: {Styles.BUTTON_RADIUS};
            padding: {Styles.PADDING_SMALL}px {Styles.PADDING_NORMAL}px;
            color: white;
            font-weight: bold;
            font-size: {Styles.FONT_SIZE_NORMAL}px;
        }}
        QPushButton:hover {{
            background-color: #DC2626;
        }}
        QPushButton:pressed {{
            background-color: #B91C1C;
        }}
        """)
        self.cancel_button.clicked.connect(self.request_cancel)
        
        control_layout.addWidget(self.time_info_label)
        control_layout.addStretch()
        control_layout.addWidget(self.cancel_button)
        
        layout.addWidget(control_frame)
        
    def start_task(self, task_name: str):
        """开始任务"""
        self.current_task = task_name
        self.is_processing = True
        
        # 更新显示
        self.task_title_label.setText(task_name)
        self.current_step_label.setText("初始化...")
        self.overall_progress.setValue(0)
        self.step_progress.setValue(0)
        self.overall_percent_label.setText("0%")
        self.step_percent_label.setText("0%")
        self.details_text.clear()
        self.time_info_label.setText("预计剩余时间: 计算中...")
        
        # 显示组件
        self.setVisible(True)
        
    def update_overall_progress(self, value: int, message: str = ""):
        """更新总体进度"""
        self.overall_progress.setValue(value)
        self.overall_percent_label.setText(f"{value}%")
        
        if message:
            self.current_step_label.setText(message)
            
    def update_step_progress(self, value: int, step_name: str = ""):
        """更新步骤进度"""
        self.step_progress.setValue(value)
        self.step_percent_label.setText(f"{value}%")
        
        if step_name:
            self.current_step_label.setText(step_name)
            
    def add_detail_message(self, message: str):
        """添加详细信息"""
        self.details_text.append(message)
        # 自动滚动到底部
        scrollbar = self.details_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
        
    def update_time_estimate(self, remaining_seconds: int):
        """更新剩余时间估计"""
        if remaining_seconds > 0:
            minutes = remaining_seconds // 60
            seconds = remaining_seconds % 60
            self.time_info_label.setText(f"预计剩余时间: {minutes:02d}:{seconds:02d}")
        else:
            self.time_info_label.setText("预计剩余时间: 即将完成")
            
    def finish_task(self, success: bool = True, message: str = ""):
        """完成任务"""
        self.is_processing = False
        
        if success:
            self.overall_progress.setValue(100)
            self.step_progress.setValue(100)
            self.overall_percent_label.setText("100%")
            self.step_percent_label.setText("100%")
            self.current_step_label.setText("处理完成")
            self.time_info_label.setText("任务已完成")
            
            if message:
                self.add_detail_message(f"✅ {message}")
        else:
            self.current_step_label.setText("处理失败")
            self.time_info_label.setText("任务已停止")
            
            if message:
                self.add_detail_message(f"❌ {message}")
                
        # 更新按钮
        self.cancel_button.setText("关闭")
        
    def request_cancel(self):
        """请求取消"""
        if self.is_processing:
            self.cancel_requested.emit()
            self.cancel_button.setText("取消中...")
            self.cancel_button.setEnabled(False)
        else:
            # 隐藏组件
            self.setVisible(False)
            
    def reset(self):
        """重置状态"""
        self.current_task = None
        self.is_processing = False
        self.cancel_button.setText("取消处理")
        self.cancel_button.setEnabled(True)
        self.setVisible(False)
