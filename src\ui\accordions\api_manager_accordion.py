"""
API管理手风琴
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QLabel, QLineEdit, QCheckBox,
    QHBoxLayout, QPushButton, QTextEdit, QFrame, QGridLayout
)
from PySide6.QtCore import Qt

from .base_accordion import BaseAccordion
from ...utils.constants import Colors, Styles, Layout, API
from ..components.console_widget import ConsoleWidget


class ApiManagerAccordion(BaseAccordion):
    """API管理手风琴"""
    
    def __init__(self):
        super().__init__("API管理", "⚙️")
        self.setup_content()
        
    def setup_content(self):
        """设置内容"""
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(Styles.MARGIN_NORMAL)
        
        # API配置区域
        self.create_api_config_section(layout)
        
        # 控制台输出区域
        self.create_console_section(layout)
        
        # 控制按钮区域
        self.create_control_buttons_section(layout)
        
        self.set_content_widget(content_widget)
        
    def create_api_config_section(self, layout):
        """创建API配置区域"""
        config_frame = QFrame()
        config_frame.setStyleSheet(f"""
        QFrame {{
            background-color: {Colors.SECONDARY_BG};
            border: 1px solid {Colors.BORDER};
            border-radius: {Styles.INPUT_RADIUS};
            padding: {Styles.PADDING_NORMAL}px;
        }}
        """)
        
        config_layout = QVBoxLayout(config_frame)
        config_layout.setSpacing(Styles.MARGIN_SMALL)
        
        # 本地API配置
        local_label = QLabel("本地API配置:")
        local_label.setStyleSheet(f"font-weight: bold; color: {Colors.FOREGROUND};")
        config_layout.addWidget(local_label)
        
        # 本地API URL
        url_layout = QHBoxLayout()
        url_label = QLabel("API地址:")
        url_label.setMinimumWidth(80)
        self.local_url_input = QLineEdit(f"http://localhost:{API.DEFAULT_LOCAL_PORT}")
        url_layout.addWidget(url_label)
        url_layout.addWidget(self.local_url_input)
        config_layout.addLayout(url_layout)
        
        # Python环境路径
        python_layout = QHBoxLayout()
        python_label = QLabel("Python路径:")
        python_label.setMinimumWidth(80)
        self.python_path_input = QLineEdit("./workenv/python.exe")
        python_layout.addWidget(python_label)
        python_layout.addWidget(self.python_path_input)
        config_layout.addLayout(python_layout)
        
        # 启动选项
        options_layout = QHBoxLayout()
        self.auto_start_checkbox = QCheckBox("随软件启动API")
        self.auto_start_checkbox.setChecked(True)
        self.cloud_fallback_checkbox = QCheckBox("启用云端回退")
        self.cloud_fallback_checkbox.setChecked(True)
        options_layout.addWidget(self.auto_start_checkbox)
        options_layout.addWidget(self.cloud_fallback_checkbox)
        config_layout.addLayout(options_layout)
        
        # 云端API配置
        cloud_label = QLabel("云端API配置:")
        cloud_label.setStyleSheet(f"font-weight: bold; color: {Colors.FOREGROUND}; margin-top: {Styles.MARGIN_NORMAL}px;")
        config_layout.addWidget(cloud_label)
        
        # 云端API URL
        cloud_url_layout = QHBoxLayout()
        cloud_url_label = QLabel("云端地址:")
        cloud_url_label.setMinimumWidth(80)
        self.cloud_url_input = QLineEdit("https://api.example.com")
        cloud_url_layout.addWidget(cloud_url_label)
        cloud_url_layout.addWidget(self.cloud_url_input)
        config_layout.addLayout(cloud_url_layout)
        
        layout.addWidget(config_frame)
        
    def create_console_section(self, layout):
        """创建控制台输出区域"""
        # 使用专用的控制台组件
        self.console_widget = ConsoleWidget()
        self.console_widget.setMaximumHeight(Layout.CONSOLE_HEIGHT)

        # 添加示例输出
        self.console_widget.add_info("API服务准备就绪")
        self.console_widget.add_info("等待连接...")

        layout.addWidget(self.console_widget)
        
    def create_control_buttons_section(self, layout):
        """创建控制按钮区域"""
        button_frame = QFrame()
        button_layout = QGridLayout(button_frame)
        button_layout.setSpacing(Styles.MARGIN_SMALL)
        
        # 启动/停止按钮
        self.start_button = QPushButton("🚀 启动API")
        self.start_button.setStyleSheet(f"""
        QPushButton {{
            background-color: {Colors.SUCCESS};
            border: none;
            border-radius: {Styles.BUTTON_RADIUS};
            padding: {Styles.PADDING_NORMAL}px;
            color: white;
            font-weight: bold;
            font-size: {Styles.FONT_SIZE_NORMAL}px;
        }}
        QPushButton:hover {{
            background-color: #059669;
        }}
        QPushButton:pressed {{
            background-color: #047857;
        }}
        """)
        
        self.stop_button = QPushButton("⏹ 停止API")
        self.stop_button.setEnabled(False)
        self.stop_button.setStyleSheet(f"""
        QPushButton {{
            background-color: {Colors.ERROR};
            border: none;
            border-radius: {Styles.BUTTON_RADIUS};
            padding: {Styles.PADDING_NORMAL}px;
            color: white;
            font-weight: bold;
            font-size: {Styles.FONT_SIZE_NORMAL}px;
        }}
        QPushButton:hover {{
            background-color: #DC2626;
        }}
        QPushButton:pressed {{
            background-color: #B91C1C;
        }}
        QPushButton:disabled {{
            background-color: {Colors.SECONDARY_TEXT};
            color: {Colors.BORDER};
        }}
        """)
        
        # 测试连接按钮
        self.test_button = QPushButton("🔗 测试连接")
        self.test_button.setStyleSheet(f"""
        QPushButton {{
            background-color: {Colors.ACCENT};
            border: none;
            border-radius: {Styles.BUTTON_RADIUS};
            padding: {Styles.PADDING_NORMAL}px;
            color: white;
            font-weight: bold;
            font-size: {Styles.FONT_SIZE_NORMAL}px;
        }}
        QPushButton:hover {{
            background-color: {Colors.ACCENT_HOVER};
        }}
        QPushButton:pressed {{
            background-color: {Colors.ACCENT_PRESSED};
        }}
        """)
        
        # 清空日志按钮
        self.clear_button = QPushButton("🗑 清空日志")
        self.clear_button.setStyleSheet(f"""
        QPushButton {{
            background-color: {Colors.WARNING};
            border: none;
            border-radius: {Styles.BUTTON_RADIUS};
            padding: {Styles.PADDING_NORMAL}px;
            color: white;
            font-weight: bold;
            font-size: {Styles.FONT_SIZE_NORMAL}px;
        }}
        QPushButton:hover {{
            background-color: #D97706;
        }}
        QPushButton:pressed {{
            background-color: #B45309;
        }}
        """)
        
        # 布局按钮
        button_layout.addWidget(self.start_button, 0, 0)
        button_layout.addWidget(self.stop_button, 0, 1)
        button_layout.addWidget(self.test_button, 1, 0)
        button_layout.addWidget(self.clear_button, 1, 1)
        
        # 连接信号
        self.start_button.clicked.connect(self.start_api)
        self.stop_button.clicked.connect(self.stop_api)
        self.test_button.clicked.connect(self.test_connection)
        self.clear_button.clicked.connect(self.clear_console)
        
        layout.addWidget(button_frame)
        
    def start_api(self):
        """启动API服务"""
        self.console_widget.add_info("正在启动API服务...")
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        # TODO: 实现实际的API启动逻辑
        self.console_widget.add_success("API服务启动成功")

    def stop_api(self):
        """停止API服务"""
        self.console_widget.add_info("正在停止API服务...")
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        # TODO: 实现实际的API停止逻辑
        self.console_widget.add_info("API服务已停止")

    def test_connection(self):
        """测试连接"""
        url = self.local_url_input.text()
        self.console_widget.add_info(f"测试连接到: {url}")
        # TODO: 实现实际的连接测试逻辑
        self.console_widget.add_success("连接测试成功")

    def clear_console(self):
        """清空控制台"""
        self.console_widget.clear_console()
