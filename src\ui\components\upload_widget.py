"""
音频文件上传组件
"""

from PySide6.QtWidgets import (
    QFrame, QVBoxLayout, QLabel, QComboBox, QPushButton, 
    QHBoxLayout, QFileDialog, QProgressBar
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QDragEnterEvent, QDropEvent

from ...utils.constants import Colors, Styles
from .audio_player import AudioPlayer
from .waveform_widget import WaveformWidget
from .progress_widget import ProgressWidget


class UploadWidget(QFrame):
    """音频文件上传组件"""
    
    # 信号
    file_uploaded = Signal(str)  # 文件上传信号
    processing_started = Signal()  # 开始处理信号
    
    def __init__(self):
        super().__init__()
        self.setProperty("class", "upload-widget")
        self.current_file = None
        self.init_ui()
        self.setup_drag_drop()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(Styles.PADDING_LARGE, Styles.PADDING_LARGE,
                                 Styles.PADDING_LARGE, Styles.PADDING_LARGE)
        layout.setSpacing(Styles.MARGIN_NORMAL)
        
        # 上传区域
        self.create_upload_area(layout)
        
        # 配置区域
        self.create_config_area(layout)
        
        # 处理按钮
        self.create_action_button(layout)
        
        # 音频播放器和波形显示
        self.create_audio_section(layout)

        # 进度条
        self.create_progress_bar(layout)
        
        # 设置样式
        self.setStyleSheet(f"""
        QFrame[class="upload-widget"] {{
            background-color: {Colors.CARD_BG};
            border: 2px dashed {Colors.ACCENT};
            border-radius: {Styles.CARD_RADIUS};
            padding: {Styles.PADDING_LARGE}px;
        }}
        
        QFrame[class="upload-widget"]:hover {{
            border-color: {Colors.ACCENT_HOVER};
            background-color: {Colors.SECONDARY_BG};
        }}
        """)
        
    def create_upload_area(self, layout):
        """创建上传区域 - 减少高度并支持点击上传"""
        upload_frame = QFrame()
        upload_frame.setFixedHeight(80)  # 固定高度为原来的一半
        upload_frame.setStyleSheet(f"""
        QFrame {{
            border: 2px dashed {Colors.ACCENT};
            border-radius: {Styles.CARD_RADIUS};
            background-color: transparent;
            padding: {Styles.PADDING_NORMAL}px;
        }}
        QFrame:hover {{
            background-color: {Colors.SECONDARY_BG};
            cursor: pointer;
        }}
        """)
        upload_layout = QHBoxLayout(upload_frame)  # 改为水平布局以节省空间
        upload_layout.setAlignment(Qt.AlignCenter)
        upload_layout.setSpacing(Styles.MARGIN_SMALL)

        # 上传图标 (缩小尺寸)
        icon_label = QLabel("📤")
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet(f"""
        QLabel {{
            font-size: 32px;
            color: {Colors.ACCENT};
        }}
        """)
        upload_layout.addWidget(icon_label)

        # 主提示文字 (参考HTML版本)
        self.main_text = QLabel("在此处选择或拖拽音频文件")
        self.main_text.setAlignment(Qt.AlignCenter)
        self.main_text.setStyleSheet(f"""
        QLabel {{
            font-size: {Styles.FONT_SIZE_NORMAL}px;
            color: {Colors.ACCENT};
        }}
        """)
        upload_layout.addWidget(self.main_text)

        # 使整个区域可点击
        upload_frame.mousePressEvent = self.on_upload_area_clicked

        layout.addWidget(upload_frame)

    def on_upload_area_clicked(self, event):
        """点击上传区域时触发文件选择"""
        self.browse_file()

    def create_config_area(self, layout):
        """创建配置区域 - 参考HTML版本网格布局"""
        config_frame = QFrame()
        config_layout = QVBoxLayout(config_frame)
        config_layout.setSpacing(Styles.MARGIN_NORMAL)

        # 创建网格布局容器
        grid_container = QFrame()
        grid_layout = QHBoxLayout(grid_container)
        grid_layout.setSpacing(Styles.MARGIN_LARGE)

        # 处理模式选择
        mode_container = QFrame()
        mode_layout = QVBoxLayout(mode_container)
        mode_layout.setContentsMargins(0, 0, 0, 0)
        mode_layout.setSpacing(Styles.MARGIN_SMALL)

        mode_label = QLabel("处理模式:")
        mode_label.setProperty("class", "label-text")
        self.mode_combo = QComboBox()
        self.mode_combo.addItems(["完整模式", "快速模式"])
        self.mode_combo.setCurrentText("完整模式")
        self.mode_combo.setProperty("class", "input-field")

        mode_layout.addWidget(mode_label)
        mode_layout.addWidget(self.mode_combo)
        grid_layout.addWidget(mode_container)

        # 输出格式选择
        format_container = QFrame()
        format_layout = QVBoxLayout(format_container)
        format_layout.setContentsMargins(0, 0, 0, 0)
        format_layout.setSpacing(Styles.MARGIN_SMALL)

        format_label = QLabel("输出格式:")
        format_label.setProperty("class", "label-text")
        self.format_combo = QComboBox()
        self.format_combo.addItems(["WAV", "MP3"])
        self.format_combo.setCurrentText("WAV")
        self.format_combo.setProperty("class", "input-field")

        format_layout.addWidget(format_label)
        format_layout.addWidget(self.format_combo)
        grid_layout.addWidget(format_container)

        config_layout.addWidget(grid_container)
        layout.addWidget(config_frame)

    def create_audio_section(self, layout):
        """创建音频播放和波形显示区域"""
        # 音频播放器
        self.audio_player = AudioPlayer()
        self.audio_player.setVisible(False)  # 初始隐藏
        layout.addWidget(self.audio_player)

        # 波形显示
        self.waveform_widget = WaveformWidget()
        self.waveform_widget.setVisible(False)  # 初始隐藏
        layout.addWidget(self.waveform_widget)

        # 进度显示
        self.progress_widget = ProgressWidget()
        layout.addWidget(self.progress_widget)

        # 连接信号
        self.waveform_widget.position_clicked.connect(self.on_waveform_clicked)
        self.progress_widget.cancel_requested.connect(self.on_cancel_processing)

    def create_action_button(self, layout):
        """创建处理按钮 - 参考HTML版本样式"""
        # 按钮容器
        button_container = QFrame()
        button_layout = QVBoxLayout(button_container)
        button_layout.setSpacing(Styles.MARGIN_NORMAL)

        # 一键翻唱按钮
        self.process_button = QPushButton("🚀 一键翻唱")
        self.process_button.setEnabled(False)
        self.process_button.setStyleSheet(f"""
        QPushButton {{
            background-color: {Colors.ACCENT};
            border: none;
            border-radius: {Styles.BUTTON_RADIUS};
            padding: {Styles.PADDING_LARGE}px;
            color: white;
            font-weight: bold;
            font-size: {Styles.FONT_SIZE_TITLE}px;
            min-height: 48px;
        }}
        QPushButton:hover {{
            background-color: {Colors.ACCENT_HOVER};
        }}
        QPushButton:pressed {{
            background-color: {Colors.ACCENT_PRESSED};
        }}
        QPushButton:disabled {{
            background-color: {Colors.SECONDARY_TEXT};
            color: {Colors.BORDER};
        }}
        """)
        self.process_button.clicked.connect(self.start_processing)
        button_layout.addWidget(self.process_button)

        # 状态标签 (参考HTML版本)
        self.status_label = QLabel("状态: 空闲")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet(f"""
        QLabel {{
            color: {Colors.SECONDARY_TEXT};
            font-size: {Styles.FONT_SIZE_SMALL}px;
            margin-top: {Styles.MARGIN_NORMAL}px;
        }}
        """)
        button_layout.addWidget(self.status_label)

        layout.addWidget(button_container)
        
    def create_progress_bar(self, layout):
        """创建进度条"""
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet(f"""
        QProgressBar {{
            border: 1px solid {Colors.BORDER};
            border-radius: {Styles.INPUT_RADIUS};
            background-color: {Colors.SECONDARY_BG};
            text-align: center;
            color: {Colors.FOREGROUND};
            font-size: {Styles.FONT_SIZE_SMALL}px;
        }}
        QProgressBar::chunk {{
            background-color: {Colors.ACCENT};
            border-radius: {Styles.INPUT_RADIUS};
        }}
        """)
        
        layout.addWidget(self.progress_bar)
        
    def setup_drag_drop(self):
        """设置拖拽功能"""
        self.setAcceptDrops(True)
        
    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()
            if urls and self.is_audio_file(urls[0].toLocalFile()):
                event.acceptProposedAction()
                self.setStyleSheet(f"""
                QFrame[class="upload-widget"] {{
                    background-color: {Colors.SECONDARY_BG};
                    border: 2px dashed {Colors.ACCENT_HOVER};
                    border-radius: {Styles.CARD_RADIUS};
                }}
                """)
        
    def dragLeaveEvent(self, event):
        """拖拽离开事件"""
        self.setStyleSheet(f"""
        QFrame[class="upload-widget"] {{
            background-color: {Colors.CARD_BG};
            border: 2px dashed {Colors.ACCENT};
            border-radius: {Styles.CARD_RADIUS};
        }}
        """)
        
    def dropEvent(self, event: QDropEvent):
        """拖拽放下事件"""
        urls = event.mimeData().urls()
        if urls:
            file_path = urls[0].toLocalFile()
            if self.is_audio_file(file_path):
                self.load_file(file_path)
                event.acceptProposedAction()
        
        # 恢复样式
        self.dragLeaveEvent(event)
        
    def is_audio_file(self, file_path: str) -> bool:
        """检查是否为音频文件"""
        audio_extensions = ['.wav', '.mp3', '.flac', '.ogg', '.m4a', '.aac']
        return any(file_path.lower().endswith(ext) for ext in audio_extensions)
        
    def browse_file(self):
        """浏览文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择音频文件",
            "",
            "音频文件 (*.wav *.mp3 *.flac *.ogg *.m4a *.aac);;所有文件 (*)"
        )
        
        if file_path:
            self.load_file(file_path)
            
    def load_file(self, file_path: str):
        """加载文件 - 简化版本，不显示文件信息"""
        self.current_file = file_path

        # 简单更新显示，不显示文件名
        self.main_text.setText("音频文件已选择")
        self.process_button.setEnabled(True)

        # 更新状态标签
        self.status_label.setText("状态: 文件已加载")

        # 不显示音频播放器和波形显示
        # self.audio_player.setVisible(False)
        # self.waveform_widget.setVisible(False)

        # 发送信号
        self.file_uploaded.emit(file_path)
        
    def start_processing(self):
        """开始处理"""
        if self.current_file:
            self.process_button.setEnabled(False)
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)

            # 发送信号
            self.processing_started.emit()

            # 启动进度显示
            self.start_processing_with_progress()

            # TODO: 实现实际的处理逻辑
            print(f"开始处理文件: {self.current_file}")
            print(f"处理模式: {self.mode_combo.currentText()}")
            print(f"输出格式: {self.format_combo.currentText()}")
            
    def update_progress(self, value: int):
        """更新进度"""
        self.progress_bar.setValue(value)
        
    def on_waveform_clicked(self, relative_position: float):
        """处理波形点击事件"""
        if self.audio_player and self.audio_player.get_current_file():
            # 计算绝对位置（毫秒）
            duration_ms = self.waveform_widget.get_duration() * 1000
            position_ms = int(duration_ms * relative_position)

            # 设置播放位置
            self.audio_player.media_player.setPosition(position_ms)

    def on_cancel_processing(self):
        """处理取消处理请求"""
        # TODO: 实现实际的取消逻辑
        print("用户请求取消处理")
        self.progress_widget.finish_task(False, "用户取消了处理")

    def start_processing_with_progress(self):
        """开始处理并显示进度"""
        if self.current_file:
            # 启动进度显示
            self.progress_widget.start_task("AI音频翻唱处理")

            # 模拟处理步骤
            self.simulate_processing()

    def simulate_processing(self):
        """模拟处理过程（实际应用中应该连接到真实的API）"""
        # 这里应该连接到实际的API处理
        steps = [
            ("音频标准化", 10),
            ("音频分离", 30),
            ("F0提取", 50),
            ("音色转换", 80),
            ("混响处理", 95),
            ("完成", 100)
        ]

        for step_name, progress in steps:
            self.progress_widget.update_overall_progress(progress, step_name)
            self.progress_widget.add_detail_message(f"正在执行: {step_name}")

        self.progress_widget.finish_task(True, "音频处理完成")

    def reset_state(self):
        """重置状态"""
        self.current_file = None
        self.main_text.setText("在此处选择或拖拽音频文件")
        self.process_button.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.progress_bar.setValue(0)
        self.status_label.setText("状态: 空闲")

        # 隐藏音频组件
        self.audio_player.setVisible(False)
        self.waveform_widget.setVisible(False)
        self.progress_widget.setVisible(False)
