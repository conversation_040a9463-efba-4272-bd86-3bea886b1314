"""
音频播放器组件
"""

import os
from pathlib import Path
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, 
    QSlider, QFrame, QSizePolicy
)
from PySide6.QtCore import Qt, QTimer, Signal, QUrl
from PySide6.QtMultimedia import QMediaPlayer, QAudioOutput
from PySide6.QtGui import QFont

from ...utils.constants import Colors, Styles


class AudioPlayer(QWidget):
    """音频播放器组件"""
    
    # 信号
    position_changed = Signal(int)  # 播放位置改变
    duration_changed = Signal(int)  # 总时长改变
    state_changed = Signal(int)     # 播放状态改变
    
    def __init__(self):
        super().__init__()
        self.current_file = None
        self.is_seeking = False
        self.init_ui()
        self.setup_media_player()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(Styles.MARGIN_NORMAL)
        
        # 文件信息区域
        self.create_file_info_section(layout)
        
        # 控制按钮区域
        self.create_control_buttons_section(layout)
        
        # 进度条区域
        self.create_progress_section(layout)
        
        # 时间显示区域
        self.create_time_section(layout)
        
    def create_file_info_section(self, layout):
        """创建文件信息区域"""
        info_frame = QFrame()
        info_frame.setStyleSheet(f"""
        QFrame {{
            background-color: {Colors.SECONDARY_BG};
            border: 1px solid {Colors.BORDER};
            border-radius: {Styles.INPUT_RADIUS};
            padding: {Styles.PADDING_SMALL}px;
        }}
        """)
        
        info_layout = QVBoxLayout(info_frame)
        info_layout.setContentsMargins(Styles.PADDING_SMALL, Styles.PADDING_SMALL,
                                      Styles.PADDING_SMALL, Styles.PADDING_SMALL)
        info_layout.setSpacing(2)
        
        # 文件名
        self.file_name_label = QLabel("未选择文件")
        self.file_name_label.setStyleSheet(f"""
        QLabel {{
            color: {Colors.FOREGROUND};
            font-weight: bold;
            font-size: {Styles.FONT_SIZE_NORMAL}px;
        }}
        """)
        info_layout.addWidget(self.file_name_label)
        
        # 文件信息
        self.file_info_label = QLabel("--")
        self.file_info_label.setStyleSheet(f"""
        QLabel {{
            color: {Colors.SECONDARY_TEXT};
            font-size: {Styles.FONT_SIZE_SMALL}px;
        }}
        """)
        info_layout.addWidget(self.file_info_label)
        
        layout.addWidget(info_frame)
        
    def create_control_buttons_section(self, layout):
        """创建控制按钮区域"""
        control_frame = QFrame()
        control_layout = QHBoxLayout(control_frame)
        control_layout.setContentsMargins(0, 0, 0, 0)
        control_layout.setSpacing(Styles.MARGIN_SMALL)
        
        # 播放/暂停按钮
        self.play_button = QPushButton("▶")
        self.play_button.setFixedSize(40, 40)
        self.play_button.setStyleSheet(f"""
        QPushButton {{
            background-color: {Colors.ACCENT};
            border: none;
            border-radius: 20px;
            color: white;
            font-size: 16px;
            font-weight: bold;
        }}
        QPushButton:hover {{
            background-color: {Colors.ACCENT_HOVER};
        }}
        QPushButton:pressed {{
            background-color: {Colors.ACCENT_PRESSED};
        }}
        QPushButton:disabled {{
            background-color: {Colors.SECONDARY_TEXT};
            color: {Colors.BORDER};
        }}
        """)
        self.play_button.clicked.connect(self.toggle_playback)
        self.play_button.setEnabled(False)
        
        # 停止按钮
        self.stop_button = QPushButton("⏹")
        self.stop_button.setFixedSize(35, 35)
        self.stop_button.setStyleSheet(f"""
        QPushButton {{
            background-color: {Colors.ERROR};
            border: none;
            border-radius: 17px;
            color: white;
            font-size: 14px;
            font-weight: bold;
        }}
        QPushButton:hover {{
            background-color: #DC2626;
        }}
        QPushButton:pressed {{
            background-color: #B91C1C;
        }}
        QPushButton:disabled {{
            background-color: {Colors.SECONDARY_TEXT};
            color: {Colors.BORDER};
        }}
        """)
        self.stop_button.clicked.connect(self.stop_playback)
        self.stop_button.setEnabled(False)
        
        # 音量控制
        volume_label = QLabel("🔊")
        volume_label.setStyleSheet(f"color: {Colors.FOREGROUND}; font-size: 14px;")
        
        self.volume_slider = QSlider(Qt.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(70)
        self.volume_slider.setMaximumWidth(100)
        self.volume_slider.setStyleSheet(f"""
        QSlider::groove:horizontal {{
            background-color: {Colors.SECONDARY_BG};
            height: 4px;
            border-radius: 2px;
        }}
        QSlider::handle:horizontal {{
            background-color: {Colors.ACCENT};
            border: 1px solid {Colors.ACCENT};
            width: 12px;
            height: 12px;
            border-radius: 6px;
            margin: -4px 0;
        }}
        QSlider::sub-page:horizontal {{
            background-color: {Colors.ACCENT};
            border-radius: 2px;
        }}
        """)
        self.volume_slider.valueChanged.connect(self.set_volume)
        
        # 布局控制按钮
        control_layout.addWidget(self.play_button)
        control_layout.addWidget(self.stop_button)
        control_layout.addStretch()
        control_layout.addWidget(volume_label)
        control_layout.addWidget(self.volume_slider)
        
        layout.addWidget(control_frame)
        
    def create_progress_section(self, layout):
        """创建进度条区域"""
        self.progress_slider = QSlider(Qt.Horizontal)
        self.progress_slider.setRange(0, 0)
        self.progress_slider.setStyleSheet(f"""
        QSlider::groove:horizontal {{
            background-color: {Colors.SECONDARY_BG};
            height: 6px;
            border-radius: 3px;
            border: 1px solid {Colors.BORDER};
        }}
        QSlider::handle:horizontal {{
            background-color: {Colors.ACCENT};
            border: 2px solid {Colors.ACCENT};
            width: 16px;
            height: 16px;
            border-radius: 8px;
            margin: -5px 0;
        }}
        QSlider::sub-page:horizontal {{
            background-color: {Colors.ACCENT};
            border-radius: 3px;
        }}
        """)
        self.progress_slider.sliderPressed.connect(self.on_seek_start)
        self.progress_slider.sliderReleased.connect(self.on_seek_end)
        self.progress_slider.valueChanged.connect(self.on_seek_position)
        
        layout.addWidget(self.progress_slider)
        
    def create_time_section(self, layout):
        """创建时间显示区域"""
        time_frame = QFrame()
        time_layout = QHBoxLayout(time_frame)
        time_layout.setContentsMargins(0, 0, 0, 0)
        
        # 当前时间
        self.current_time_label = QLabel("00:00")
        self.current_time_label.setStyleSheet(f"""
        QLabel {{
            color: {Colors.FOREGROUND};
            font-size: {Styles.FONT_SIZE_SMALL}px;
            font-family: 'Consolas', 'Monaco', monospace;
        }}
        """)
        
        # 总时长
        self.total_time_label = QLabel("00:00")
        self.total_time_label.setStyleSheet(f"""
        QLabel {{
            color: {Colors.SECONDARY_TEXT};
            font-size: {Styles.FONT_SIZE_SMALL}px;
            font-family: 'Consolas', 'Monaco', monospace;
        }}
        """)
        
        time_layout.addWidget(self.current_time_label)
        time_layout.addStretch()
        time_layout.addWidget(self.total_time_label)
        
        layout.addWidget(time_frame)
        
    def setup_media_player(self):
        """设置媒体播放器"""
        self.media_player = QMediaPlayer()
        self.audio_output = QAudioOutput()
        self.media_player.setAudioOutput(self.audio_output)
        
        # 连接信号
        self.media_player.positionChanged.connect(self.on_position_changed)
        self.media_player.durationChanged.connect(self.on_duration_changed)
        self.media_player.playbackStateChanged.connect(self.on_state_changed)
        
        # 设置初始音量
        self.audio_output.setVolume(0.7)
        
    def load_file(self, file_path: str):
        """加载音频文件"""
        if not os.path.exists(file_path):
            return False
            
        self.current_file = file_path
        file_name = Path(file_path).name
        
        # 更新文件信息显示
        self.file_name_label.setText(file_name)
        self.file_info_label.setText(f"文件路径: {file_path}")
        
        # 加载媒体文件
        media_url = QUrl.fromLocalFile(file_path)
        self.media_player.setSource(media_url)
        
        # 启用控制按钮
        self.play_button.setEnabled(True)
        self.stop_button.setEnabled(True)
        
        return True
        
    def toggle_playback(self):
        """切换播放/暂停"""
        if self.media_player.playbackState() == QMediaPlayer.PlayingState:
            self.media_player.pause()
        else:
            self.media_player.play()
            
    def stop_playback(self):
        """停止播放"""
        self.media_player.stop()
        
    def set_volume(self, value):
        """设置音量"""
        volume = value / 100.0
        self.audio_output.setVolume(volume)
        
    def on_position_changed(self, position):
        """播放位置改变"""
        if not self.is_seeking:
            self.progress_slider.setValue(position)
            
        # 更新时间显示
        self.current_time_label.setText(self.format_time(position))
        self.position_changed.emit(position)
        
    def on_duration_changed(self, duration):
        """总时长改变"""
        self.progress_slider.setRange(0, duration)
        self.total_time_label.setText(self.format_time(duration))
        self.duration_changed.emit(duration)
        
    def on_state_changed(self, state):
        """播放状态改变"""
        if state == QMediaPlayer.PlayingState:
            self.play_button.setText("⏸")
        else:
            self.play_button.setText("▶")
            
        self.state_changed.emit(state)
        
    def on_seek_start(self):
        """开始拖拽进度条"""
        self.is_seeking = True
        
    def on_seek_end(self):
        """结束拖拽进度条"""
        self.is_seeking = False
        position = self.progress_slider.value()
        self.media_player.setPosition(position)
        
    def on_seek_position(self, position):
        """拖拽进度条位置"""
        if self.is_seeking:
            self.current_time_label.setText(self.format_time(position))
            
    def format_time(self, ms):
        """格式化时间显示"""
        seconds = ms // 1000
        minutes = seconds // 60
        seconds = seconds % 60
        return f"{minutes:02d}:{seconds:02d}"
        
    def get_current_file(self):
        """获取当前文件路径"""
        return self.current_file
        
    def is_playing(self):
        """检查是否正在播放"""
        return self.media_player.playbackState() == QMediaPlayer.PlayingState
