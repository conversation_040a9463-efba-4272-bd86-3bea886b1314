# 木偶AI翻唱项目 - 部署与运维指南

## 1. 环境要求

### 1.1 硬件要求

**最低配置**:
- CPU: Intel i5-8400 / AMD Ryzen 5 2600
- 内存: 8GB RAM
- 存储: 10GB 可用空间
- 网络: 稳定的互联网连接

**推荐配置**:
- CPU: Intel i7-10700K / AMD Ryzen 7 3700X
- 内存: 16GB RAM
- GPU: NVIDIA GTX 1660 Ti / RTX 3060 (6GB+ VRAM)
- 存储: 20GB+ SSD空间
- 网络: 高速宽带连接

**GPU支持**:
- CUDA 11.8+ 兼容的NVIDIA显卡
- 最少4GB显存用于基础推理
- 8GB+显存用于高质量处理

### 1.2 软件环境

**操作系统**:
- Windows 10/11 (64位)
- Linux Ubuntu 18.04+ (实验性支持)
- macOS 10.15+ (CPU模式)

**Python环境**:
- Python 3.8 - 3.10
- pip 21.0+
- 虚拟环境管理工具

**系统依赖**:
- FFmpeg 4.4+
- Microsoft Visual C++ 2019 Redistributable
- CUDA Toolkit 11.8+ (GPU模式)

## 2. 安装部署

### 2.1 源码部署

#### 步骤1: 环境准备
```bash
# 创建虚拟环境
python -m venv workenv
workenv\Scripts\activate  # Windows
# source workenv/bin/activate  # Linux/macOS

# 升级pip
python -m pip install --upgrade pip
```

#### 步骤2: 安装依赖
```bash
# 安装PyTorch (CUDA版本)
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# 安装MSST依赖
cd msst
pip install -r requirements.txt

# 安装GUI依赖
pip install PyQt5 pyqtgraph pydub requests pillow numpy soundfile
```

#### 步骤3: 配置FFmpeg
```bash
# Windows: 下载FFmpeg并解压到ffmpeg/bin/目录
# Linux: sudo apt install ffmpeg
# macOS: brew install ffmpeg
```

#### 步骤4: 下载预训练模型
```bash
# 创建模型目录
mkdir -p pretrain/contentvec
mkdir -p pretrain/rmvpe
mkdir -p pretrain/Vocoder

# 下载必要的预训练模型
# - ContentVec编码器
# - RMVPE F0提取器
# - NSF-HiFiGAN声码器
```

### 2.2 打包部署

#### 使用内置打包脚本
```bash
# 运行打包脚本
python 打包单exe脚本.py

# 生成的文件结构
dist/
├── 木偶AI翻唱.exe     # 主程序 (~100MB)
└── 分发包/
    ├── workenv/        # Python环境
    ├── msst/          # 音频分离模块
    ├── ffmpeg/        # FFmpeg工具
    ├── pretrain/      # 预训练模型
    └── models/        # 用户模型
```

#### 分发包结构
```
木偶AI翻唱_v1.0/
├── 木偶AI翻唱.exe
├── workenv/           # 完整Python环境 (~2GB)
├── msst/             # MSST分离框架
├── main_reflow.py    # 音色转换脚本
├── ffmpeg/           # 音频处理工具
├── pretrain/         # 预训练模型 (~5GB)
├── models/           # 用户模型目录
├── temp/             # 临时文件目录
├── cache/            # 缓存目录
├── logs/             # 日志目录
└── README.md         # 使用说明
```

## 3. 配置管理

### 3.1 模型配置

#### 添加新模型
```bash
# 1. 将模型文件复制到models目录
cp your_model.pt models/
cp your_model.yaml models/

# 2. 验证配置文件格式
python -c "
import yaml
with open('models/your_model.yaml', 'r') as f:
    config = yaml.safe_load(f)
    print('配置验证通过')
"

# 3. 重启应用以加载新模型
```

#### 模型配置模板
```yaml
# models/template.yaml
data:
  sampling_rate: 44100
  block_size: 512
  encoder: contentvec768l12tta2x
  encoder_ckpt: pretrain/contentvec/checkpoint_best_legacy_500.pt
  f0_extractor: rmvpe
  f0_min: 65
  f0_max: 800

model:
  type: RectifiedFlow
  n_spk: 1
  use_pitch_aug: true
  n_chans: 1024
  n_layers: 6

vocoder:
  type: nsf-hifigan
  ckpt: pretrain/Vocoder/nsf_hifigan/model
```

### 3.2 MSST配置

#### 修改分离设置
```json
// msst/data/webui_config.json
{
    "inference": {
        "device": "cuda",           // "cuda" | "cpu"
        "output_format": "wav",     // "wav" | "mp3"
        "force_cpu": false,
        "use_tta": false,           // 测试时增强
        "extra_output_dir": true
    },
    "settings": {
        "auto_clean_cache": true,
        "debug": false,
        "theme": "theme_blue.json"
    }
}
```

## 4. 运维监控

### 4.1 日志管理

#### 日志文件位置
```
logs/
├── muou_ai_cover_2024-01-01_12-00-00.log  # 主应用日志
├── msst_inference.log                      # MSST推理日志
├── reflow_conversion.log                   # 音色转换日志
└── error.log                              # 错误日志
```

#### 日志级别配置
```python
# 修改日志级别
import logging
logging.basicConfig(level=logging.DEBUG)  # DEBUG | INFO | WARNING | ERROR
```

### 4.2 性能监控

#### 系统资源监控
```python
import psutil
import GPUtil

def monitor_resources():
    # CPU使用率
    cpu_percent = psutil.cpu_percent(interval=1)
    
    # 内存使用
    memory = psutil.virtual_memory()
    memory_percent = memory.percent
    
    # GPU使用率
    gpus = GPUtil.getGPUs()
    if gpus:
        gpu_percent = gpus[0].load * 100
        gpu_memory = gpus[0].memoryUtil * 100
    
    print(f"CPU: {cpu_percent}%, Memory: {memory_percent}%, GPU: {gpu_percent}%")
```

#### 处理时间统计
```python
# 在main_reflow.py中添加计时
import time

start_time = time.time()
# ... 处理逻辑 ...
processing_time = time.time() - start_time
print(f"Processing completed in {processing_time:.2f} seconds")
```

### 4.3 错误处理

#### 常见错误及解决方案

**1. CUDA内存不足**
```
Error: CUDA out of memory
解决方案:
- 降低batch_size
- 使用CPU模式
- 关闭其他GPU应用
```

**2. 模型加载失败**
```
Error: Failed to load model checkpoint
解决方案:
- 检查模型文件完整性
- 验证配置文件格式
- 确认路径正确性
```

**3. 音频格式不支持**
```
Error: Unsupported audio format
解决方案:
- 使用FFmpeg转换格式
- 检查文件是否损坏
- 更新音频处理库
```

#### 自动恢复机制
```python
def safe_process_audio(input_file, max_retries=3):
    for attempt in range(max_retries):
        try:
            return process_audio(input_file)
        except Exception as e:
            if attempt == max_retries - 1:
                raise e
            print(f"Attempt {attempt + 1} failed, retrying...")
            time.sleep(2 ** attempt)  # 指数退避
```

## 5. 备份与恢复

### 5.1 数据备份

#### 重要文件备份
```bash
# 创建备份脚本 backup.bat
@echo off
set BACKUP_DIR=backup_%date:~0,4%%date:~5,2%%date:~8,2%
mkdir %BACKUP_DIR%

# 备份用户模型
xcopy models %BACKUP_DIR%\models /E /I

# 备份配置文件
xcopy msst\data %BACKUP_DIR%\msst_data /E /I

# 备份用户数据
xcopy outputs %BACKUP_DIR%\outputs /E /I
xcopy results %BACKUP_DIR%\results /E /I

echo Backup completed: %BACKUP_DIR%
```

#### 自动备份策略
```python
import schedule
import shutil
from datetime import datetime

def backup_user_data():
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"backup_{timestamp}"
    
    # 备份关键目录
    shutil.copytree("models", f"{backup_dir}/models")
    shutil.copytree("outputs", f"{backup_dir}/outputs")
    
    print(f"Backup created: {backup_dir}")

# 每天凌晨2点自动备份
schedule.every().day.at("02:00").do(backup_user_data)
```

### 5.2 系统恢复

#### 快速恢复步骤
```bash
# 1. 停止应用
taskkill /f /im "木偶AI翻唱.exe"

# 2. 恢复配置文件
xcopy backup_20240101\msst_data msst\data /E /Y

# 3. 恢复用户模型
xcopy backup_20240101\models models /E /Y

# 4. 清理缓存
rmdir /s /q cache
rmdir /s /q temp

# 5. 重启应用
start "木偶AI翻唱.exe"
```

## 6. 性能优化

### 6.1 GPU优化

#### CUDA内存管理
```python
import torch

# 清理GPU缓存
torch.cuda.empty_cache()

# 设置内存分配策略
torch.cuda.set_per_process_memory_fraction(0.8)

# 启用混合精度训练
from torch.cuda.amp import autocast
with autocast():
    output = model(input_tensor)
```

### 6.2 CPU优化

#### 多线程处理
```python
import concurrent.futures
import multiprocessing

# 设置线程数
num_workers = min(4, multiprocessing.cpu_count())

with concurrent.futures.ThreadPoolExecutor(max_workers=num_workers) as executor:
    futures = [executor.submit(process_segment, segment) for segment in segments]
    results = [future.result() for future in futures]
```

### 6.3 存储优化

#### 缓存管理
```python
import os
import time

def cleanup_cache(cache_dir="cache", max_age_days=7):
    """清理过期缓存文件"""
    current_time = time.time()
    max_age_seconds = max_age_days * 24 * 3600
    
    for filename in os.listdir(cache_dir):
        filepath = os.path.join(cache_dir, filename)
        if os.path.isfile(filepath):
            file_age = current_time - os.path.getmtime(filepath)
            if file_age > max_age_seconds:
                os.remove(filepath)
                print(f"Removed expired cache: {filename}")
```

## 7. 安全考虑

### 7.1 文件安全
- 限制上传文件大小和格式
- 验证文件完整性
- 定期清理临时文件

### 7.2 系统安全
- 运行在受限用户权限下
- 禁用不必要的网络连接
- 定期更新依赖库

这个部署与运维指南为项目的生产环境部署和日常维护提供了全面的操作指导。
