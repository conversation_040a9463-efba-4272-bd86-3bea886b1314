"""
歌曲管理手风琴
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QLabel, QListWidget, QListWidgetItem,
    QHBoxLayout, QPushButton, QFrame
)
from PySide6.QtCore import Qt

from .base_accordion import BaseAccordion
from ...utils.constants import Colors, Styles


class SongManagerAccordion(BaseAccordion):
    """歌曲管理手风琴"""
    
    def __init__(self):
        super().__init__("歌曲管理", "🎵")
        self.setup_content()
        
    def setup_content(self):
        """设置内容"""
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(Styles.MARGIN_NORMAL)
        
        # 歌曲列表
        self.song_list = QListWidget()
        self.song_list.setMinimumHeight(200)
        self.song_list.setStyleSheet(f"""
        QListWidget {{
            background-color: {Colors.INPUT_BG};
            border: 1px solid {Colors.BORDER};
            border-radius: {Styles.INPUT_RADIUS};
            padding: {Styles.PADDING_SMALL}px;
            color: {Colors.FOREGROUND};
        }}
        QListWidget::item {{
            padding: {Styles.PADDING_SMALL}px;
            border-bottom: 1px solid {Colors.BORDER};
        }}
        QListWidget::item:selected {{
            background-color: {Colors.ACCENT};
            color: white;
        }}
        QListWidget::item:hover {{
            background-color: {Colors.SECONDARY_BG};
        }}
        """)
        
        # 添加示例歌曲
        self.add_sample_songs()
        
        layout.addWidget(self.song_list)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        button_layout.setSpacing(Styles.MARGIN_SMALL)
        
        self.play_button = QPushButton("▶ 播放")
        self.play_button.setStyleSheet(f"""
        QPushButton {{
            background-color: {Colors.ACCENT};
            border: none;
            border-radius: {Styles.BUTTON_RADIUS};
            padding: {Styles.PADDING_SMALL}px {Styles.PADDING_NORMAL}px;
            color: white;
            font-size: {Styles.FONT_SIZE_SMALL}px;
        }}
        QPushButton:hover {{
            background-color: {Colors.ACCENT_HOVER};
        }}
        QPushButton:pressed {{
            background-color: {Colors.ACCENT_PRESSED};
        }}
        """)
        
        self.delete_button = QPushButton("🗑 删除")
        self.delete_button.setStyleSheet(f"""
        QPushButton {{
            background-color: {Colors.ERROR};
            border: none;
            border-radius: {Styles.BUTTON_RADIUS};
            padding: {Styles.PADDING_SMALL}px {Styles.PADDING_NORMAL}px;
            color: white;
            font-size: {Styles.FONT_SIZE_SMALL}px;
        }}
        QPushButton:hover {{
            background-color: #DC2626;
        }}
        QPushButton:pressed {{
            background-color: #B91C1C;
        }}
        """)
        
        self.export_button = QPushButton("📤 导出")
        self.export_button.setStyleSheet(f"""
        QPushButton {{
            background-color: {Colors.SUCCESS};
            border: none;
            border-radius: {Styles.BUTTON_RADIUS};
            padding: {Styles.PADDING_SMALL}px {Styles.PADDING_NORMAL}px;
            color: white;
            font-size: {Styles.FONT_SIZE_SMALL}px;
        }}
        QPushButton:hover {{
            background-color: #059669;
        }}
        QPushButton:pressed {{
            background-color: #047857;
        }}
        """)
        
        button_layout.addWidget(self.play_button)
        button_layout.addWidget(self.delete_button)
        button_layout.addWidget(self.export_button)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
        # 空状态提示
        self.empty_state = self.create_empty_state()
        layout.addWidget(self.empty_state)
        
        # 连接信号
        self.play_button.clicked.connect(self.play_selected_song)
        self.delete_button.clicked.connect(self.delete_selected_song)
        self.export_button.clicked.connect(self.export_selected_song)
        self.song_list.itemSelectionChanged.connect(self.update_button_states)
        
        # 初始状态
        self.update_button_states()
        
        self.set_content_widget(content_widget)
        
    def create_empty_state(self):
        """创建空状态显示"""
        empty_frame = QFrame()
        empty_frame.setStyleSheet(f"""
        QFrame {{
            background-color: {Colors.SECONDARY_BG};
            border: 2px dashed {Colors.BORDER};
            border-radius: {Styles.CARD_RADIUS};
            padding: {Styles.PADDING_LARGE}px;
        }}
        """)
        
        empty_layout = QVBoxLayout(empty_frame)
        empty_layout.setAlignment(Qt.AlignCenter)
        
        icon_label = QLabel("🎵")
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet(f"""
        QLabel {{
            font-size: 32px;
            color: {Colors.SECONDARY_TEXT};
            margin-bottom: {Styles.MARGIN_SMALL}px;
        }}
        """)
        
        text_label = QLabel("暂无成品歌曲")
        text_label.setAlignment(Qt.AlignCenter)
        text_label.setStyleSheet(f"""
        QLabel {{
            color: {Colors.SECONDARY_TEXT};
            font-size: {Styles.FONT_SIZE_NORMAL}px;
        }}
        """)
        
        empty_layout.addWidget(icon_label)
        empty_layout.addWidget(text_label)
        
        empty_frame.hide()  # 初始隐藏，有歌曲时显示
        return empty_frame
        
    def add_sample_songs(self):
        """添加示例歌曲"""
        sample_songs = [
            "示例歌曲1 - 翻唱版.wav",
            "示例歌曲2 - AI版本.wav",
            "测试音频 - 处理完成.wav"
        ]
        
        for song in sample_songs:
            item = QListWidgetItem(song)
            item.setData(Qt.UserRole, {"path": f"outputs/{song}", "duration": "3:45"})
            self.song_list.addItem(item)
            
    def update_button_states(self):
        """更新按钮状态"""
        has_selection = bool(self.song_list.currentItem())
        self.play_button.setEnabled(has_selection)
        self.delete_button.setEnabled(has_selection)
        self.export_button.setEnabled(has_selection)
        
        # 更新空状态显示
        if self.song_list.count() == 0:
            self.song_list.hide()
            self.empty_state.show()
        else:
            self.song_list.show()
            self.empty_state.hide()
            
    def play_selected_song(self):
        """播放选中的歌曲"""
        current_item = self.song_list.currentItem()
        if current_item:
            song_name = current_item.text()
            print(f"播放歌曲: {song_name}")
            # TODO: 实现实际的播放功能
            
    def delete_selected_song(self):
        """删除选中的歌曲"""
        current_row = self.song_list.currentRow()
        if current_row >= 0:
            item = self.song_list.takeItem(current_row)
            print(f"删除歌曲: {item.text()}")
            self.update_button_states()
            
    def export_selected_song(self):
        """导出选中的歌曲"""
        current_item = self.song_list.currentItem()
        if current_item:
            song_name = current_item.text()
            print(f"导出歌曲: {song_name}")
            # TODO: 实现实际的导出功能
