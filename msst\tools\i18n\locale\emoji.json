{"仅供个人娱乐和非商业用途, 禁止用于血腥/暴力/性相关/政治相关内容。[点击前往教程文档](https://r1kc63iz15l.feishu.cn/wiki/JSp3wk7zuinvIXkIqSUcCXY1nKc)<br>本整合包完全免费, 严禁以任何形式倒卖, 如果你从任何地方**付费**购买了本整合包, 请**立即退款**。<br> 整合包作者: [bilibili@阿狸不吃隼舞](https://space.bilibili.com/403335715) [Github@KitsuneX07](https://github.com/KitsuneX07) | [Bilibili@Sucial](https://space.bilibili.com/445022409) [Github@SUC-DriverOld](https://github.com/SUC-DriverOld) | Gradio主题: [Gradio Theme](https://huggingface.co/spaces/NoCrypt/miku)": "For personal entertainment and non-commercial use only<br>Package authors: [Github@KitsuneX07](https://github.com/KitsuneX07) | [Github@SUC-DriverOld](https://github.com/SUC-DriverOld) | Gradio theme: [Gradio Theme](https://huggingface.co/spaces/NoCrypt/miku)", "MSST分离": "📂🔄MSST", "UVR分离": "📂🔄UVR", "预设流程": "🔢📜🎵🔄", "合奏模式": "🎶🎛️", "小工具": "🛠️🔧", "安装模型": "🔧📦", "MSST训练": "🏋️‍♂️📚MSST", "设置": "⚙️🔧", "输出音轨": "🔊🎶", "请至少添加2个模型到合奏流程": "⚠️➕2️⃣🔧🎛️", "合奏流程已保存": "💾🎶✅", "请上传至少一个音频文件!": "🎵📂🚀🔄📤🎶⚠️", "请先创建合奏流程": "⚠️🎛️🛠️", "模型": "📦", "不存在": "🚫❌!", "用户强制终止": "🚫🙅", "处理失败: ": "❌⚠️", "处理完成, 成功: ": "✅🔄🎉: ", "个文件, 失败: ": "📄❌", "个文件": "📄", ", 结果已保存至: ": "📂💾📍: ", ", 耗时: ": "⏲️", "请上传至少2个文件": "📤📄🔍👆2️⃣!", "上传的文件数目与权重数目不匹配": "📤📄🔢🚫⚖️❌!", "处理完成, 文件已保存为: ": "⚙️📄✅, 📂💾📥:", "处理失败!": "⚙️❌!", "input文件夹内文件列表:\\n": "📂input🗂️📜\\n", "input文件夹为空\\n": "📂input❌\\n", "results文件夹内文件列表:\\n": "📂results🗂️📜\\n", "results文件夹为空\\n": "📂results❌\\n", "已删除input文件夹内所有文件": "🗑️📂input❌", "已删除results文件夹内所有文件": "🗑️📂results❌", "请先选择模型": "🗂️📦🔍", "仅输出主音轨": "🎵🔊🔝", "仅输出次音轨": "🎵🔊🔽", "已打开下载管理器": "🔓⬇️🗂️", "选择模型": "📦🔍👆!", "模型名字": "📦📛", "模型类型": "📦⚙️", "主要提取音轨": "1🔊📝", "次要提取音轨": "2🔊📝", "下载链接": "⬇️🔗", "模型类别": "📦🔍", "可提取音轨": "🔊📝", "模型大小": "📦📏", "模型已安装": "📦✔️", "无法校验sha256": "❌sha256🔐🧩", "sha256校验失败": "sha256❌", "模型sha256校验失败, 请重新下载": "📦sha256❌🔁", "sha256校验成功": "sha256✔️", "模型未安装": "📦❌", "请选择模型类型": "📦🔍⚙️👆!", "已安装": "📦✅", "请手动删除后重新下载": "❌🗑️🔄📥", "下载成功": "📥✅", "下载失败": "📥❌", "已安装。请勿重复安装。": "✅⚙️🚫🔄", "已打开": "🚪✅", "的下载链接": "🔗📥", "上传参数文件": "⬆️📄🗂️!", "上传参数": "⬆️🔢!", "请上传'.yaml'格式的配置文件": "📤📄'.yaml'!", "请上传'ckpt', 'chpt', 'th'格式的模型文件": "📤🗂️'ckpt' 'chpt' 'th'!", "请输入正确的模型类别和模型类型": "📝🔍📦📛🔢", "安装成功。重启WebUI以刷新模型列表": "✅📦🔄🌐📜!", "安装失败": "❌📦!", "请输入选择模型参数": "📝🔍📦🔢", "请上传'.json'格式的参数文件": "📤📄'.json'!", "请上传'.pth'格式的模型文件": "📤📄'.pth'!", "请输入正确的音轨名称": "🔡🎵✏️👆!", "配置保存成功!": "⚙️💾✅!", "非官方模型不支持重置配置!": "🚫🔧⚙️❌!", "配置重置成功!": "⚙️🔄✅!", "备份文件不存在!": "❌📁🚫", "选择输出音轨": "🎶➡️📂", "请选择模型": "📦🔍👆!", "请选择输入目录": "📂🔍📥!", "请选择输出目录": "📂📤🔍!", "请选择GPU": "🖥️🔍🎮", "处理完成, 结果已保存至: ": "✅📁💾➡️", "暂无备份文件": "📦⚙️🚫❌!", "作为下一模型输入(或结果输出)的音轨": "🔜🎶🔄", "直接保存至输出目录的音轨(可多选)": "📂🎶➡️💾", "不输出": "🚫📤", "请填写预设名称": "🔍📝⚙️!", "预设": "⚙️", "保存成功": "💾✅!", "请选择预设": "⚙️🔍👆!", "不支持的预设版本: ": "⚠️🚫🛠️📦: ", ", 请重新制作预设。": "🔄🛠️📜", "预设版本不支持": "🚫📦🛠️", "预设不存在": "⚙️🚫❌!", "删除成功": "🗑️✅!", "预设已删除": "⚙️🗑️✅!", "选择需要恢复的预设流程备份": "📦⚙️🔍👆🔄", "已成功恢复备份": "📦⚙️🔄✅", "设置重置成功, 请重启WebUI刷新! ": "⚙️🔄✅, 🔄🌐🚀!", "记录重置成功, 请重启WebUI刷新! ": "📑🔄✅, 🔄🌐🚀!", "请选择正确的模型目录": "📂📦🔍👆!", "设置保存成功! 请重启WebUI以应用。": "⚙️💾✅! 🔄🌐🚀!", "当前版本: ": "📅📦: ", ", 发现新版本: ": ", 🔍🆕📦: ", ", 已是最新版本": ", 🚀🆕📦✅", "检查更新失败": "🔍🆕🚫❌", "语言已更改, 重启WebUI生效": "🌐🔄✅, 🔄🌐🚀", "成功将端口设置为": "🌐⚙️✅", ", 重启WebUI生效": ", 🔄🌐🚀", "huggingface.co (需要魔法)": "🌐🔗huggingface.co", "hf-mirror.com (镜像站可直连)": "🌐🔗hf-mirror.com", "下载链接已更改": "🔗📥🔄✅", "公共链接已开启, 重启WebUI生效": "🌐🔗🔓, 🔄🌐🚀", "公共链接已关闭, 重启WebUI生效": "🌐🔗🔒, 🔄🌐🚀", "已开启局域网分享, 重启WebUI生效": "💻🔗🔓, 🔄🌐🚀", "已关闭局域网分享, 重启WebUI生效": "💻🔗🔒, 🔄🌐🚀", "已开启自动清理缓存": "🔄🧹🗑️💾✔️🆙", "已关闭自动清理缓存": "🔄🛑🧹🗑️❌", "已开启调试模式": "🐞🔧✔️", "已关闭调试模式": "🐞🔧❌", "主题已更改, 重启WebUI生效": "🎨🔄🌐WebUI✔️", "音频设置已保存": "🎵⚙️💾", "已重命名为": "🔄📛➡️", "选择模型类型": "📊🔍📦🤖", "请先选择模型类型": "🙏🔍📦🤖", "新模型名称后缀错误!": "📦📛❌!", "模型名字已存在! 请重新命名!": "📛📦🔁⚠️!", "重命名失败!": "🔄📛❌!", "检测到": "🔍✔️", "旧版配置, 正在更新至最新版": "📜➡️🆕", "成功清理Gradio缓存": "✅🧹Gradio🗂️", "请上传至少一个文件": "📤📄🔍👆!", "单声道": "🔊🎚️1️⃣", "处理完成, 成功转换: ": "✅📄➡️", "请先下载SOME预处理模型并放置在tools/SOME_weights文件夹下! ": "📦🔄📥SOME, 🔄⚙️📂tools/SOME_weights!", "请先选择模型保存路径! ": "📦💾🔍👆!", "初始模型": "🚀📦", "模型类型错误, 请重新选择": "📦⚙️🚫❌, 🔄🔍⚙️!", "配置文件不存在, 请重新选择": "⚙️📄🚫❌, 🔄🔍⚙️!", "数据集路径不存在, 请重新选择": "📂🔍🚫❌, 🔄🔍⚙️!", "验证集路径不存在, 请重新选择": "📂🔍🚫❌, 🔄🔍⚙️!", "数据集类型错误, 请重新选择": "📦⚙️🚫❌, 🔄🔍⚙️!", "训练启动成功! 请前往控制台查看训练信息! ": "📈🚀✅! 🔍🖥️👀!", "模型不存在, 请重新选择": "📦🚫❌, 🔄🔍📦!", "验证完成! 请打开输出文件夹查看详细结果": "📂📤🎉✅! 🔍📂👀!", "错误: 无法找到增强配置文件模板, 请检查文件configs/augmentations_template.yaml是否存在。": "🚫🔍📂❌, 📂📄🔍configs/augmentations_template.yaml👀!", "已开启调试日志": "📜🔧✔️", "已关闭调试日志": "📜🔧❌", "模型不存在!": "🚫📦❌!", "输入音频分离": "🎧🎵🎶➗🔄", "输入文件夹分离": "📂📁➗🔄", "请先选择文件夹!": "📂🔍👆!", "显存不足, 请尝试减小batchsize值和chunksize值后重试。": "⚠️🧠📉batchsize, chunksize📊", "内存不足，请尝试增大虚拟内存后重试。若分离时出现此报错，也可尝试将推理音频裁切短一些，分段分离。": "⚠️💾🔄🎶✂️", "FFmpeg未找到，请检查FFmpeg是否正确安装。若使用的是整合包，请重新安装。": "❌⚙️🔍FFmpeg", "模型损坏，请重新下载并安装模型后重试。": "❌📦🔧🔄", "文件或路径不存在，请根据错误指示检查是否存在该文件。": "❌📂🚫🔍", "合奏模式可用于集成不同算法的结果, 具体的文档位于/docs/ensemble.md。目前主要有以下两种合奏方式:<br>1. 从原始音频合奏: 直接上传一个或多个音频文件, 然后选择多个模型进行处理, 将这些处理结果根据选择的合奏模式进行合奏<br>2. 从分离结果合奏: 上传多个已经分离完成的结果音频, 然后选择合奏模式进行合奏": "🎛️💻🔄🎶📜/docs/ensemble.md<br>1️⃣🎶📂🔄: 📤🎶📂🔛🧩🔧2️⃣🎶📂💨🔄🎧🎛️", "从原始音频合奏": "🎶📂🔄🎛️", "从原始音频合奏需要上传至少一个音频文件, 然后选择多个模型先进行分离处理, 然后将这些处理结果根据选择的合奏模式进行合奏。<br>注意, 请确保你的磁盘空间充足, 合奏过程会产生的临时文件仅会在处理结束后删除。": "⚠️🎶📂🛠️🔄🎛️📤🎶💾⚠️💻💾🔄🗑️", "制作合奏流程": "🛠️🎛️🎶", "权重": "⚖️", "添加到合奏流程": "➕🎛️🎶", "撤销上一步": "↩️🔙", "全部清空": "🧹🚮", "合奏流程": "🎛️🎶", "保存此合奏流程": "💾🎛️🎶", "集成模式": "🤝🔄", "输出格式": "📤📦🗂️", "使用CPU (注意: 使用CPU会导致速度非常慢) ": "🖥️⚙️🐢⚠️", "使用TTA (测试时增强), 可能会提高质量, 但时间x3": "TTA🚀⚖️🕑⬇️⬆️!", "输出次级音轨 (例如: 合奏人声时, 同时输出伴奏)": "🔊🎶➕🎤🎵", "输入音频": "🎧🎵🎶📤", "上传一个或多个音频文件": "⬆️🎵📁", "输入文件夹": "📂📁📤🔄", "输入目录": "📁📥", "选择文件夹": "✅📂", "打开文件夹": "🔓📂", "输出目录": "📁🔄📤", "从分离结果合奏": "🎶💨🎛️", "从分离结果合奏需要上传至少两个音频文件, 这些音频文件是使用不同的模型分离同一段音频的结果。因此, 上传的所有音频长度应该相同。": "⚠️2️⃣🎶🔄🧩📤", "上传多个音频文件": "⬆️🎵📁", "权重(以空格分隔, 数量要与上传的音频一致)": "⚖️🔢🔗🆚", "运行": "✅🔄", "强制停止": "⏹️🛑", "### 集成模式": "🤝🔄", "1. `avg_wave`: 在1D变体上进行集成, 独立地找到波形的每个样本的平均值<br>2. `median_wave`: 在1D变体上进行集成, 独立地找到波形的每个样本的中位数<br>3. `min_wave`: 在1D变体上进行集成, 独立地找到波形的每个样本的最小绝对值<br>4. `max_wave`: 在1D变体上进行集成, 独立地找到波形的每个样本的最大绝对值<br>5. `avg_fft`: 在频谱图 (短时傅里叶变换 (STFT) 2D变体) 上进行集成, 独立地找到频谱图的每个像素的平均值。平均后使用逆STFT得到原始的1D波形<br>6. `median_fft`: 与avg_fft相同, 但使用中位数代替平均值 (仅在集成3个或更多来源时有用) <br>7. `min_fft`: 与avg_fft相同, 但使用最小函数代替平均值 (减少激进程度) <br>8. `max_fft`: 与avg_fft相同, 但使用最大函数代替平均值 (增加激进程度) ": "1️⃣📈📉🔢📉🔄<br>2️⃣📈📉🔢📈🔄<br>3️⃣📈📉🔢📉🔢🔽<br>4️⃣📈📉🔢📈🔝<br>5️⃣📊📈🔄📉🔄📉🔄📉<br>6️⃣📊📈🔄📈🔄🔢🔍📏<br>7️⃣📊📈🔄🔢🔽<br>8️⃣📊📈🔄🔢🔝", "### 注意事项": "⚠️📋", "1. min_fft可用于进行更保守的合成, 它将减少更激进模型的影响。<br>2. 最好合成等质量的模型。在这种情况下, 它将带来增益。如果其中一个模型质量不好, 它将降低整体质量。<br>3. 在原仓库作者的实验中, 与其他方法相比, avg_wave在SDR分数上总是更好或相等。<br>4. 最终会在输出目录下生成一个`ensemble_<集成模式>.wav`。": "1️⃣🔍⚖️🔄📈🔽<br>2️⃣⚖️🔄📈⬆️<br>3️⃣🔬📊🔍📈🆚<br>4️⃣📂📝", "下载官方模型": "⬇️📦🌐", "点击打开下载管理器": "🖱️🔓⬇️🗂️", "模型信息": "📦📝", "打开模型目录": "📂🔍", "自动下载": "🔄⬇️", "手动下载": "🖱️⬇️", "1. MSST模型默认下载在pretrain/<模型类型>文件夹下。UVR模型默认下载在设置中的UVR模型目录中。<br>2. 下加载进度可以打开终端查看。如果一直卡着不动或者速度很慢, 在确信网络正常的情况下请尝试重启WebUI。<br>3. 若下载失败, 会在模型目录**留下一个损坏的模型**, 请**务必**打开模型目录手动删除! <br>4. 点击“重启WebUI”按钮后, 会短暂性的失去连接, 随后会自动开启一个新网页。": "1️⃣📂⬇️MSST📦pretrain/<📂>UVR📦⚙️UVR📂<br>2️⃣⏳🖥️🕵️⚡🐢🔄<br>3️⃣⚠️⬇️❌📂🗑️!<br>4️⃣🔄🌐👆🔗", "### 模型下载链接": "🔗⬇️📦", "1. 自动从Github, Huggingface或镜像站下载模型。<br>2. 你也可以在此整合包下载链接中的All_Models文件夹中找到所有可用的模型并下载。": "1️⃣🤖⬇️GitHub, Huggingface🪞📦", "若自动下载出现报错或下载过慢, 请点击手动下载, 跳转至下载链接。手动下载完成后, 请根据你选择的模型类型放置到对应文件夹内。": "⚠️⬇️🐢💥🖱️➡️🔗⏬📂🏷️!", "### 当前UVR模型目录: ": "🔍UVR📂🗂️", ", 如需更改, 请前往设置页面。": "✏️⚙️📝", "### 模型安装完成后, 需重启WebUI刷新模型列表": "📦✅🔄🌐📜!", "重启WebUI": "🔄🌐🚀", "安装非官方MSST模型": "⬆️📦⚙️MSST", "你可以从其他途径获取非官方MSST模型, 在此页面完成配置文件设置后, 即可正常使用。<br>注意: 仅支持'.ckpt', '.th', '.chpt'格式的模型。模型显示名字为模型文件名。<br>选择模型类型: 共有三个可选项。依次代表人声相关模型, 多音轨分离模型, 单音轨分离模型。仅用于区分模型大致类型, 可任意选择。<br>选择模型类别: 此选项关系到模型是否能正常推理使用, 必须准确选择!": "⬇️🔍📦⚙️MSST⬆️⚙️! ⚠️🔗 '.ckpt', '.th', '.chpt'! 📜.", "上传非官方MSST模型": "⬆️📦⚙️MSST", "上传非官方MSST模型配置文件": "⬆️📦🗂️⚙️MSST", "选择模型类别": "📦📊👆", "模型下载链接 (非必须，若无，可跳过)": "🔗⬇️📦❔❌➡️", "安装非官方VR模型": "⬆️📦⚙️VR", "你可以从其他途径获取非官方UVR模型, 在此页面完成配置文件设置后, 即可正常使用。<br>注意: 仅支持'.pth'格式的模型。模型显示名字为模型文件名。": "⬇️🔍📦⚙️UVR⬆️⚙️! ⚠️🔗 '.pth'! 📜.", "上传非官方VR模型": "⬆️📦⚙️VR", "主要音轨名称": "🎵🔝📛", "次要音轨名称": "🎵🔽📛", "选择模型参数": "📦⚙️👆", "是否为Karaoke模型": "🎤📦❔", "是否为BV模型": "📦BV❔", "是否为VR 5.1模型": "📦VR 5.1❔", "MSST音频分离原项目地址: [https://github.com/ZFTurbo/Music-Source-Separation-Training](https://github.com/ZFTurbo/Music-Source-Separation-Training)": "🔗🔍🎵🔊🛠️[https://github.com/ZFTurbo/Music-Source-Separation-Training](https://github.com/ZFTurbo/Music-Source-Separation-Training)", "选择使用的GPU": "🖥️📊🎮", "强制使用CPU推理, 注意: 使用CPU推理速度非常慢!": "🔋➡️CPU🚀, ⚠️🕒!", "使用CPU": "🖥️⚙️CPU", "[点击展开] 推理参数设置, 不同模型之间参数相互独立": "[🔽] 🧩📊🎛️🛠️💡", "只有在点击保存后才会生效。参数直接写入配置文件, 无法撤销。假如不知道如何设置, 请保持默认值。<br>请牢记自己修改前的参数数值, 防止出现问题以后无法恢复。请确保输入正确的参数, 否则可能会导致模型无法正常运行。<br>假如修改后无法恢复, 请点击``重置``按钮, 这会使得配置文件恢复到默认值。": "✍️💻➡️💸, 🔒❌️📝🤔❗️, 💡📊😬👀💻, 🔙️↩️🔄️", "批次大小, 减小此值可以降低显存占用, 此参数对推理效果影响不大": "📉🎛️🧠💾", "重叠数, 增大此值可以提高分离效果, 但会增加处理时间, 建议设置成4": "🔄📊⏱️📈4️⃣", "分块大小, 增大此值可以提高分离效果, 但会增加处理时间和显存占用": "🔲📊📈⏱️🧠", "音频归一化, 对音频进行归一化输入和输出, 部分模型没有此功能": "🎵🔄⚖️📤🔊", "启用TTA, 能小幅提高分离质量, 若使用, 推理时间x3": "🔛TTA📈🕒x3", "保存配置": "💾✅", "重置配置": "🔄⚙️🔧", "预设流程允许按照预设的顺序运行多个模型。每一个模型的输出将作为下一个模型的输入。": "🔢📜🎵🔄, 🛠️🔄🔢🔧🎛️🔄", "使用预设": "🔢📜", "该模式下的UVR推理参数将直接沿用UVR分离页面的推理参数, 如需修改请前往UVR分离页面。<br>修改完成后, 还需要任意处理一首歌才能保存参数! ": "UVR🎵🔄🔛📈, 🔧🔄UVR🎵🔄📝", "将次级输出保存至输出目录的单独文件夹内": "📂📁📄", "制作预设": "🔧🛠️🔢📜", "预设名称": "🔢📜📝", "请输入预设名称": "💬✏️🔢📜📝", "添加至流程": "➕🔢📜", "保存上述预设流程": "💾🔢📜", "管理预设": "⚙️🔢📜", "此页面提供查看预设, 删除预设, 备份预设, 恢复预设等功能<br>`model_type`: 模型类型；`model_name`: 模型名称；`input_to_next`: 作为下一模型输入的音轨；`output_to_storage`: 直接保存至输出目录下的direct_output文件夹内的音轨, **不会经过后续流程处理**<br>每次点击删除预设按钮时, 将自动备份预设以免误操作。": "📄🔍🗑️💾🔄⚙️<br>`📦`: 📦🗂️；`🆔`: 📛；`🔜`: 🎶🔄；`💾`: 📂➡️💾📁🎶🔄❌<br>每次点击🗑️按钮时, 将自动备份📄以免❌操作。", "删除所选预设": "🗑️🔢📜", "请先选择预设": "🔍⚙️👆!", "恢复所选预设": "🔄✔️📄", "打开备份文件夹": "📂🔓", "WebUI设置": "WebUI⚙️", "GPU信息": "🖥️🧠", "系统信息": "💻📝", "设置WebUI端口, 0为自动": "⚙️🌐🔗, 🔢0️⃣🆙", "选择语言": "🌐🔤", "选择MSST模型下载链接": "🔍🔗MSST📥🔄", "选择WebUI主题": "🎨🌐✔️", "对本地局域网开放WebUI: 开启后, 同一局域网内的设备可通过'本机IP:端口'的方式访问WebUI。": "🔓💻🔗, 💻👉🔗'💻IP:port'", "开启公共链接: 开启后, 他人可通过公共链接访问WebUI。链接有效时长为72小时。": "🔓🌐🔗, ⏳72🕒", "自动清理缓存: 开启后, 每次启动WebUI时会自动清理缓存。": "🔄🧹🗑️💾🌐🖥️🆙📅✔️", "全局调试模式: 向开发者反馈问题时请开启。(该选项支持热切换)": "🌍🐞✔️🔄", "选择UVR模型目录": "🔍UVR📂🔧", "检查更新": "🔍🔄🆙", ", 请点击检查更新按钮": ", 🔍🔄🆙", "前往Github瞅一眼": "🔗👀📦", "重置WebUI路径记录": "🔄🔙🗂️", "重置WebUI设置": "🔄⚙️🔧", "### 选择UVR模型目录": "🔍UVR📂🔧", "如果你的电脑中有安装UVR5, 你不必重新下载一遍UVR5模型, 只需在下方“选择UVR模型目录”中选择你的UVR5模型目录, 定位到models/VR_Models文件夹。<br>例如: E:/Program Files/Ultimate Vocal Remover/models/VR_Models 点击保存设置或重置设置后, 需要重启WebUI以更新。": "💻✅UVR, 🔧📂🗂️models/VR_Models, 🔄💾🆙🛠️, 🔄🌐🚀", "### 检查更新": "🔍📅🔄🆙", "从Github检查更新, 需要一定的网络要求。点击检查更新按钮后, 会自动检查是否有最新版本。你可以前往此整合包的下载链接或访问Github仓库下载最新版本。": "🔍💻🌐🔗🆙📥", "### 重置WebUI路径记录": "🔄🔙🗂️", "将所有输入输出目录重置为默认路径, 预设/模型/配置文件以及上面的设置等**不会重置**, 无需担心。重置WebUI设置后, 需要重启WebUI。": "🔄🔙📂🗂️⚙️🔧💾🆙, 🔄🌐🚀", "### 重置WebUI设置": "🔄⚙️🔧", "仅重置WebUI设置, 例如UVR模型路径, WebUI端口等。重置WebUI设置后, 需要重启WebUI。": "🔄⚙️UVR, 🌐🔧🆙, 🔄🌐🚀", "### 重启WebUI": "🔄🌐🚀", "点击 “重启WebUI” 按钮后, 会短暂性的失去连接, 随后会自动开启一个新网页。": "🖱️🔄🌐🚀, ➡️❓️💸😊🔄📱🔓", "音频输出设置": "🎧⚙️📤", "此页面支持用户自定义修改MSST/VR推理后输出音频的质量。输出音频的**采样率, 声道数与模型支持的参数有关, 无法更改**。<br>修改完成后点击保存设置即可生效。": "🔧🖥️🎶🎛️💡🖱️💾⚙️", "输出wav位深度": "🎶wav📂🎚️📏", "输出flac位深度": "🎶flac📂🎚️📏", "输出mp3比特率(bps)": "🎶mp3📂🎚️bps", "保存设置": "💾⚙️", "模型改名": "📦📛🔄", "此页面支持用户自定义修改模型名字, 以便记忆和使用。修改完成后, 需要重启WebUI以刷新模型列表。<br>【注意】此操作不可逆 (无法恢复至默认命名), 请谨慎命名。输入新模型名字时, 需保留后缀!": "👤⚙️📛🔄📦📝🔄🌐📜❗️🚫🔙🔍❗️✍️📛📦⚠️!", "新模型名": "🆕📛📦", "请输入新模型名字, 需保留后缀!": "✍️🆕📛📦⚠️!", "确认修改": "✅🔄", "立体声": "🔊🎚️🎶", "音频格式转换": "🔄🎵📝", "上传一个或多个音频文件并将其转换为指定格式。<br>支持的格式包括 .mp3, .flac, .wav, .ogg, .m4a, .wma, .aac...等等。<br>**不支持**网易云音乐/QQ音乐等加密格式, 如.ncm, .qmc等。": "⬆️🎵📁, 🔄📝📀🎶.mp3, .flac, .wav, .ogg, .m4a, .wma, .aac..., 🚫🔒.ncm, .qmc...", "选择或输入音频输出格式": "🔍✏️🎵📂", "选择音频输出目录": "🔍📂🎵", "输出音频采样率(Hz)": "🎶➡️📊Hz", "输出音频声道数": "🎶➡️🔢🔊", "输出ogg比特率(bps)": "🎶ogg📂🎚️bps", "转换音频": "🔄🎵", "合并音频": "🔗🎵🔄", "点击合并音频按钮后, 将自动把输入文件夹中的所有音频文件合并为一整个音频文件<br>合并后的音频会保存至输出目录中, 文件名为merged_audio_<文件夹名字>.wav": "🔘🎶📂➡️🎧📁💾merged_audio_<name>.wav", "计算SDR": "📏🔍🎵SDR", "上传两个**wav音频文件**并计算它们的[SDR](https://www.aicrowd.com/challenges/music-demixing-challenge-ismir-2021#evaluation-metric)。<br>SDR是一个用于评估模型质量的数值。数值越大, 模型算法结果越好。": "⬆️🎵🎵, 📏🔍🎵[SDR](https://www.aicrowd.com/challenges/music-demixing-challenge-ismir-2021#evaluation-metric)", "参考音频": "🎶📏", "待估音频": "🎶🔍", "歌声转MIDI": "🎤➡️🎵📝MIDI", "歌声转MIDI功能使用开源项目[SOME](https://github.com/openvpi/SOME/), 可以将分离得到的**干净的歌声**转换成.mid文件。<br>【必须】若想要使用此功能, 请先下载权重文件[model_steps_64000_simplified.ckpt](https://hf-mirror.com/Sucial/MSST-WebUI/resolve/main/SOME_weights/model_steps_64000_simplified.ckpt)并将其放置在程序目录下的`tools/SOME_weights`文件夹内。文件命名不可随意更改!": "🎤🎶➡️🎹📁[SOME](https://github.com/openvpi/SOME/)，⚖️[model_steps_64000_simplified.ckpt](https://huggingface.co/Sucial/MSST-WebUI/resolve/main/SOME_weights/model_steps_64000_simplified.ckpt)⚙️📂🔄", "如果不知道如何测量歌曲BPM, 可以尝试这两个在线测量工具: [bpmdetector](https://bpmdetector.kniffen.dev/) | [key-bpm-finder](https://vocalremover.org/zh/key-bpm-finder), 测量时建议上传原曲或伴奏, 若干声可能导致测量结果不准确。": "🎵❓BPM🔍⬆️🌐🛠️📊 [bpmdetector](https://bpmdetector.kniffen.dev/) | [key-bpm-finder](https://vocalremover.org/zh/key-bpm-finder) 🔗🎶", "上传音频": "📤🎶", "输入音频BPM": "🔢🎵⏱️BPM", "开始转换": "✅🔄", "1. 音频BPM (每分钟节拍数) 可以通过MixMeister BPM Analyzer等软件测量获取。<br>2. 为保证MIDI提取质量, 音频文件请采用干净清晰无混响底噪人声。<br>3. 输出MIDI不带歌词信息, 需要用户自行添加歌词。<br>4. 实际使用体验中部分音符会出现断开的现象, 需自行修正。SOME的模型主要面向DiffSinger唱法模型自动标注, 比正常用户在创作中需要的MIDI更加精细, 因而可能导致模型倾向于对音符进行切分。<br>5. 提取的MIDI没有量化/没有对齐节拍/不适配BPM, 需自行到各编辑器中手动调整。": "1️⃣BPM🎵⏱️🔍📈MixMeister BPM Analyzer<br>2️⃣🎵🔍📂📉🎤✨🔇<br>3️⃣MIDI🗂️🎵🚫📝<br>4️⃣MIDI🎵🛠️✏️🔍<br>MIDI5️⃣🎵🔢✏️🔧", "此页面提供数据集制作教程, 训练参数选择, 以及一键训练。有关配置文件的修改和数据集文件夹的详细说明请参考MSST原项目: [https://github.com/ZFTurbo/Music-Source-Separation-Training](https://github.com/ZFTurbo/Music-Source-Separation-Training)<br>在开始下方的模型训练之前, 请先进行训练数据的制作。<br>说明: 数据集类型即训练集制作Step 1中你选择的类型, 1: Type1; 2: Type2; 3: Type3; 4: Type4, 必须与你的数据集类型相匹配。": "📜📦📚⚙️🛠️📂🔗[https://github.com/ZFTurbo/Music-Source-Separation-Training](https://github.com/ZFTurbo/Music-Source-Separation-Training)📦🛠️🏋️", "训练": "🏋️🔧", "选择训练模型类型": "🔍🏋️📂", "配置文件路径": "📂📍", "请输入配置文件路径或选择配置文件": "✍️📂📍🖱️", "选择配置文件": "🖱️📂", "数据集类型": "📊🔢", "数据集路径": "📂📍", "请输入或选择数据集文件夹": "✍️📂📍🖱️", "选择数据集文件夹": "🖱️📂", "验证集路径": "🗂️📁📍", "请输入或选择验证集文件夹": "🖊️📂🔍", "选择验证集文件夹": "🗂️📁📍", "模型保存路径": "🗂️💾", "请输入或选择模型保存文件夹": "🖊️📂🔍💾", "选择初始模型, 若无初始模型, 留空或选择None即可": "🎛️⚙️❌", "刷新初始模型列表": "🔄📦📜", "训练参数设置": "⚙️🎓🔢", "num_workers: 数据集读取线程数, 0为自动": "num_workers: 👥🔢📊🔄📉🆗", "随机数种子, 0为随机": "🎲🔢🔄", "是否将加载的数据放置在固定内存中, 默认为否": "💾📥🔒❓🚫", "是否使用加速训练, 对于多显卡用户会加快训练": "🚀🏋️💨❔", "是否在训练前验证模型, 默认为否": "🔄📦✔️❓🔄🚫", "是否使用MultiSTFT Loss, 默认为否": "MultiSTFT Loss: 📉🔍🔢🆗🚫", "是否使用MSE loss, 默认为否": "MSE loss: 📉🔍🔢🆗🚫", "是否使用L1 loss, 默认为否": "L1 loss: 📉🔍🔢🆗🚫", "选择输出的评估指标": "✔️🎯📊", "选择调度器使用的评估指标": "🗂️📊✔️", "保存上述训练配置": "💾📝📊", "开始训练": "✅🏋️‍♂️", "点击开始训练后, 请到终端查看训练进度或报错, 下方不会输出报错信息, 想要停止训练可以直接关闭终端。在训练过程中, 你也可以关闭网页, 仅**保留终端**。": "🖱️✅🏋️‍♂️， 📈🖥️🚫🛑， 🔍💻", "验证": "🔍🔬", "此页面用于手动验证模型效果, 测试验证集, 输出SDR测试信息。输出的信息会存放在输出文件夹的results.txt中。<br>下方参数将自动加载训练页面的参数, 在训练页面点击保存训练参数后, 重启WebUI即可自动加载。当然你也可以手动输入参数。<br>": "🔍📝📊💾🗂️results.txt📄, 🔄🌐🚀🔄🆗🖊️", "模型路径": "🗂️📍", "请输入或选择模型文件": "🖊️📂🔍", "选择模型文件": "🗂️📂📍", "验证参数设置": "⚙️🔍🔢", "选择验证集音频格式": "🎵🔊📜", "验证集读取线程数, 0为自动": "👥🔢📊🔄📉🆗", "开始验证": "🔍✅", "训练集制作指南": "📘🛠️📊", "Step 1: 数据集制作": "1️⃣: 📂📊🛠️", "请**任选下面四种类型之一**制作数据集文件夹, 并按照给出的目录层级放置你的训练数据。完成后, 记录你的数据集**文件夹路径**以及你选择的**数据集类型**, 以便后续使用。": "🗂️🔢📊📑🔍📜📝", "不同的文件夹。每个文件夹包含所需的所有stems, 格式为stem_name.wav。与MUSDBHQ18数据集相同。在最新的代码版本中, 可以使用flac替代wav。<br>例如: ": "🗂️🎵📂stem_name.wav📝, 🎼flac🔄wav📜", "每个文件夹是stem_name。文件夹中包含仅由所需stem组成的wav文件。<br>例如: ": "stem_name🗂️🎵stem📂🎼wav📝", "可以提供以下结构的CSV文件 (或CSV文件列表) <br>例如: ": "CSV📄📋🔢🔍", "与类型1相同, 但在训练过程中所有乐器都将来自歌曲的相同位置。<br>例如: ": "🔍✅1️⃣, 🎼🔄📍🔄", "Step 2: 验证集制作": "2️⃣: 📍📊🛠️", "验证集制作。验证数据集**必须**与上面数据集制作的Type 1(MUSDB)数据集**结构相同** (**无论你使用哪种类型的数据集进行训练**) , 此外每个文件夹还必须包含每首歌的mixture.wav, mixture.wav是所有stem的总和<br>例如: ": "🔍✅1️⃣📊, 🗂️mixture.wav📜🎵🔄, mixture.wav📦stem", "Step 3: 选择并修改修改配置文件": "3️⃣: 📑✏️🔄", "请先明确你想要训练的模型类型, 然后选择对应的配置文件进行修改。<br>目前有以下几种模型类型: ": "📝🎯📊🔍:", "<br>确定好模型类型后, 你可以前往整合包根目录中的configs_backup文件夹下找到对应的配置文件模板。复制一份模板, 然后根据你的需求进行修改。修改完成后记下你的配置文件路径, 以便后续使用。<br>特别说明: config_musdb18_xxx.yaml是针对MUSDB18数据集的配置文件。<br>": "🔍📂🗂️configs_backup📑, 📝📊", "打开配置文件模板文件夹": "🔓📂📁", "你可以使用下表根据你的GPU选择用于训练的BS_Roformer模型的batch_size参数。表中提供的批量大小值适用于单个GPU。如果你有多个GPU, 则需要将该值乘以GPU的数量。": "📊BS_Roformer🖥️🎯batch_size🔢🆗", "Step 4: 数据增强": "4️⃣: 🛠️📈📊💡", "数据增强可以动态更改stem, 通过从旧样本创建新样本来增加数据集的大小。现在, 数据增强的控制在配置文件中进行。下面是一个包含所有可用数据增强的完整配置示例。你可以将其复制到你的配置文件中以使用数据增强。<br>注意:<br>1. 要完全禁用所有数据增强, 可以从配置文件中删除augmentations部分或将enable设置为false。<br>2. 如果要禁用某些数据增强, 只需将其设置为0。<br>3. all部分中的数据增强应用于所有stem。<br>4. vocals/bass等部分中的数据增强仅应用于相应的stem。你可以为training.instruments中给出的所有stem创建这样的部分。": "🔍📝🔄📈, 🛠️🔧⚙️📂, 📄🗂️❌0️⃣, 🔢📑🎵🎸", "说明: 本整合包仅融合了UVR的VR Architecture模型, MDX23C和HtDemucs类模型可以直接使用前面的MSST音频分离。<br>UVR分离使用项目: [https://github.com/nomadkaraoke/python-audio-separator](https://github.com/nomadkaraoke/python-audio-separator) 并进行了优化。": "VR Architecture📦MDX23C, HtDemucs🤖🎵UVR🔗[https://github.com/nomadkaraoke/python-audio-separator](https://github.com/nomadkaraoke/python-audio-separator)📜!", "Window Size: 窗口大小, 用于平衡速度和质量, 默认为512": "🪟🔲⚖️🚀🎶512", "Aggression: 主干提取强度, 范围-100-100, 人声请选5": "Aggression: ⚙️📈🔍🔢-100️⃣100️⃣🗣️5️⃣", "[点击展开] 以下是一些高级设置, 一般保持默认即可": "[🔽] 🛠️🔧🛠️🔧", "批次大小, 减小此值可以降低显存占用": "📉🎛️🧠💾", "后处理特征阈值, 取值为0.1-0.3, 默认0.2": "🧠🔧0.1-0.3⚖️0.2", "次级输出使用频谱而非波形进行反转, 可能会提高质量, 但速度稍慢": "🔊📊🎶📈🕒", "启用“测试时增强”, 可能会提高质量, 但速度稍慢": "🔛🔬📈🕒", "将输出音频缺失的频率范围镜像输出, 作用不大": "🔄📉🎶⚠️", "识别人声输出中残留的人工痕迹, 可改善某些歌曲的分离效果": "🔍👤🧩🎶📈", "正在启动WebUI, 请稍等...": "⏳🌐🔄WebUI...", "若启动失败, 请尝试以管理员身份运行此程序": "❌🚀👤🔧", "WebUI运行过程中请勿关闭此窗口!": "⛔🌐🚪", "检测到CUDA, 设备信息: ": "🔍CUDA✔️💻💨", "使用MPS": "⚙️🚀MPS", "检测到MPS, 使用MPS": "🔍✔️🔋MPS", "无可用的加速设备, 使用CPU": "⚠️🚫🔋➡️CPU", "\\033[33m未检测到可用的加速设备, 使用CPU\\033[0m": "⚠️🚫🔋➡️CPU", "\\033[33m如果你使用的是NVIDIA显卡, 请更新显卡驱动至最新版后重试\\033[0m": "⚠️💻💡🔄NVIDIA", "模型下载失败, 请重试!": "📦⬇️❌, 🔄🔁!", "作者: [Github@KitsuneX07](https://github.com/KitsuneX07) | [Github@SUC-DriverOld](https://github.com/SUC-DriverOld), [点击前往教程文档](https://r1kc63iz15l.feishu.cn/wiki/JSp3wk7zuinvIXkIqSUcCXY1nKc)": "Authors: <AUTHORS>", "**请将需要处理的音频放置到input文件夹内, 处理完成后的音频将会保存到results文件夹内! 云端输入输出目录不可更改!**": "**Please place the audio to be processed in the input folder, and the processed audio will be saved in the results folder! The cloud input and output directories cannot be changed!**", "文件管理": "📁⚙️", "文件管理页面是云端WebUI特有的页面, 用于上传, 下载, 删除文件。<br>1. 上传文件: 将文件上传到input文件夹内。可以勾选是否自动解压zip文件<br>2. 下载文件: 以zip格式打包results文件夹内的文件, 输出至WebUI以供下载。注意: 打包不会删除results文件夹, 若打包后不再需要分离结果, 请点击按钮手动删除。<br>3. 删除文件: 删除input和results文件夹内的文件。": "📂⚙️🌐📤📥🗑️", "删除input文件夹内所有文件": "🗑️📂input❌", "删除results文件夹内所有文件": "🗑️📂results❌", "刷新input和results文件列表": "🔄📂input&results🗂️", "打包results文件夹内所有文件": "📦results📂🗂️", "上传一个或多个文件至input文件夹": "📤📂input📂", "自动解压zip文件(仅支持zip, 压缩包内文件名若含有非ASCII字符, 解压后文件名可能为乱码)": "🔧📂📤zip📦💾❌ASCII📃", "上传文件": "📤📂", "input和results文件列表": "📂input🗂️📜📂results🗂️📜", "请先点击刷新按钮": "⚠️🔄🔘", "下载results文件夹内所有文件": "📥results📂🗂️", "Window Size: 窗口大小, 用于平衡速度和质量": "Window Size: 🔲📏⚖️🔄🚀🎛️", "初始模型: 继续训练或微调模型训练时, 请选择初始模型, 否则将从头开始训练! ": "🔄🗂️🎯💪🆗⏳", "点击合并音频按钮后, 将自动把输入文件夹中的所有音频文件合并为一整个音频文件<br>目前支持的格式包括 .mp3, .flac, .wav, .ogg, m4a 这五种<br>合并后的音频会保存至输出目录中, 文件名为merged_audio_<文件夹名字>.wav": "👆🎶➕.mp3, .flac, .wav, .ogg, m4a➡️📁🎶🔄💾📂📄"}