#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
轻量级单文件exe打包脚本
只打包PyQt5 GUI框架，所有重型依赖通过workenv环境提供
目标：生成100MB左右的单文件exe，而不是800MB的臃肿文件
"""

import os
import sys
import shutil
import subprocess



def main():
    print("=== 木偶AI翻唱 轻量级单文件exe打包脚本 ===\n")

    # 检查主脚本
    if not os.path.exists("app-单文件终版.py"):
        print("错误: app-单文件终版.py 不存在!")
        return False

    # 清理旧的构建文件
    print("清理构建目录...")
    for dir_name in ["build", "dist", "__pycache__"]:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"已删除: {dir_name}")

    # 直接使用命令行参数，不使用spec文件
    cmd = [
        "pyinstaller",
        "--onefile",  # 单文件模式
        "--windowed",  # 无控制台
        "--clean",
        "--noconfirm",
        "--strip",  # 去除调试信息
        "--optimize", "2",  # 最高级别优化
        f"--name=木偶AI翻唱",
        # 只包含最基本的GUI依赖
        "--hidden-import=PyQt5.QtCore",
        "--hidden-import=PyQt5.QtGui",
        "--hidden-import=PyQt5.QtWidgets",
        "--hidden-import=PyQt5.QtMultimedia",
        # 排除所有重型依赖（这些通过workenv提供）
        # "--exclude-module=numpy",
        # "--exclude-module=pyqtgraph",
        # "--exclude-module=pydub",
        # "--exclude-module=PIL",
        # "--exclude-module=Pillow",
        # "--exclude-module=requests",
        "--exclude-module=torch",
        "--exclude-module=torchvision",
        "--exclude-module=torchaudio",
        "--exclude-module=tensorflow",
        "--exclude-module=sklearn",
        "--exclude-module=scipy",
        "--exclude-module=pandas",
        "--exclude-module=matplotlib",
        "--exclude-module=librosa",
        "--exclude-module=soundfile",
        "--exclude-module=gradio",
        "--exclude-module=fastapi",
        "--exclude-module=uvicorn",
        "--exclude-module=fairseq",
        "--exclude-module=transformers",
        "--exclude-module=huggingface-hub",
        # 排除其他Qt绑定（确保只使用PyQt5）
        "--exclude-module=PyQt6",
        "--exclude-module=PyQt6.QtCore",
        "--exclude-module=PyQt6.QtGui",
        "--exclude-module=PyQt6.QtWidgets",
        "--exclude-module=PySide2",
        "--exclude-module=PySide6",
        "--exclude-module=PySide6.QtCore",
        "--exclude-module=PySide6.QtGui",
        "--exclude-module=PySide6.QtWidgets",
        "--exclude-module=shiboken2",
        "--exclude-module=shiboken6",
        "--exclude-module=QtPy",
        # 排除其他不需要的模块（保留必要的标准库）
        "--exclude-module=tkinter",
        "--exclude-module=test",
        "--exclude-module=unittest",
        "--exclude-module=setuptools",
        "--exclude-module=pkg_resources",
        "--exclude-module=wheel",
        "--exclude-module=pip",
        "--exclude-module=distutils",
        "--exclude-module=sqlite3",
        "--exclude-module=curses",
        "--exclude-module=turtle",
        "--exclude-module=pdb",
        "--exclude-module=doctest",
        "app-单文件终版.py"
    ]

    # 添加图标
    if os.path.exists("assets/images/title.ico"):
        cmd.insert(-1, "--icon=assets/images/title.ico")
    
    print(f"执行命令: {' '.join(cmd)}")
    print("开始打包单文件exe...")
    print("注意: 单文件打包可能需要较长时间，请耐心等待...")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("打包成功!")
        if result.stdout:
            print("输出信息:")
            print(result.stdout[-1000:])  # 显示最后1000个字符
    except subprocess.CalledProcessError as e:
        print(f"打包失败: {e}")
        if e.stderr:
            print("错误信息:")
            print(e.stderr[-1000:])  # 显示最后1000个字符的错误信息
        return False
    
    # 检查生成的exe文件
    exe_path = os.path.join("dist", "木偶AI翻唱.exe")
    if os.path.exists(exe_path):
        file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
        print(f"\n✓ 轻量级单文件exe生成成功!")
        print(f"文件路径: {exe_path}")
        print(f"文件大小: {file_size:.1f} MB")
        print(f"\n分发时请将以下文件一起打包:")
        print("- 木偶AI翻唱.exe")
        print("- workenv/ (完整目录)")
        print("- msst/ (完整目录)")
        print("- main_reflow.py")
        print("- ffmpeg/ (完整目录)")
        print("- models/ (模型文件目录)")
    else:
        print("❌ 未找到生成的exe文件!")
        return False

    return True

if __name__ == "__main__":
    if not main():
        sys.exit(1)
