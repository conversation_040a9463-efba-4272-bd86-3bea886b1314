audio:
  chunk_size: 352800
  dim_f: 1024
  dim_t: 801
  hop_length: 441
  min_mean_abs: 0.0
  n_fft: 2048
  num_channels: 2
  sample_rate: 44100
augmentations:
  all:
    channel_shuffle: 0.5
    mp3_compression: 0.05
    mp3_compression_backend: lameenc
    mp3_compression_max_bitrate: 320
    mp3_compression_min_bitrate: 32
    pedalboard_mp3_compressor: 0.005
    pedalboard_mp3_compressor_pedalboard_mp3_compressor_max: 9.999
    pedalboard_mp3_compressor_pedalboard_mp3_compressor_min: 0
    pedalboard_resample: 0.001
    pedalboard_resample_target_sample_rate_max: 44100
    pedalboard_resample_target_sample_rate_min: 4000
    random_inverse: 0.1
    random_polarity: 0.5
    vocals:
      pedalboard_reverb: 0.01
      pedalboard_reverb_damping_max: 0.9
      pedalboard_reverb_damping_min: 0.1
      pedalboard_reverb_dry_level_max: 0.9
      pedalboard_reverb_dry_level_min: 0.1
      pedalboard_reverb_room_size_max: 0.9
      pedalboard_reverb_room_size_min: 0.1
      pedalboard_reverb_wet_level_max: 0.9
      pedalboard_reverb_wet_level_min: 0.1
      pedalboard_reverb_width_max: 1.0
      pedalboard_reverb_width_min: 0.9
      pitch_shift: 0.25
      pitch_shift_max_semitones: 7
      pitch_shift_min_semitones: -7
      seven_band_parametric_eq: 0.25
      seven_band_parametric_eq_max_gain_db: 9
      seven_band_parametric_eq_min_gain_db: -9
      tanh_distortion: 0.01
      tanh_distortion_max: 0.7
      tanh_distortion_min: 0.1
  enable: true
  loudness: true
  loudness_max: 1.5
  loudness_min: 0.5
  mixup: false
  mixup_loudness_max: 1.75
  mixup_loudness_min: 0.25
  mixup_probs: !!python/tuple
  - 0.2
  - 0.02
inference:
  batch_size: 2
  dim_t: 1101
  num_overlap: 2
model:
  attn_dropout: 0.1
  depth: 16
  dim: 512
  dim_freqs_in: 1025
  dim_head: 64
  ff_dropout: 0.1
  flash_attn: true
  freq_transformer_depth: 1
  freqs_per_bands: !!python/tuple
  - 2
  - 2
  - 2
  - 2
  - 2
  - 2
  - 2
  - 2
  - 2
  - 2
  - 2
  - 2
  - 2
  - 2
  - 2
  - 2
  - 2
  - 2
  - 2
  - 2
  - 2
  - 2
  - 2
  - 2
  - 4
  - 4
  - 4
  - 4
  - 4
  - 4
  - 4
  - 4
  - 4
  - 4
  - 4
  - 4
  - 12
  - 12
  - 12
  - 12
  - 12
  - 12
  - 12
  - 12
  - 24
  - 24
  - 24
  - 24
  - 24
  - 24
  - 24
  - 24
  - 48
  - 48
  - 48
  - 48
  - 48
  - 48
  - 48
  - 48
  - 128
  - 129
  heads: 8
  linear_transformer_depth: 0
  mask_estimator_depth: 2
  multi_stft_hop_size: 147
  multi_stft_normalized: false
  multi_stft_resolution_loss_weight: 1.0
  multi_stft_resolutions_window_sizes: !!python/tuple
  - 4096
  - 2048
  - 1024
  - 512
  - 256
  num_stems: 1
  stereo: true
  stft_hop_length: 441
  stft_n_fft: 2048
  stft_normalized: false
  stft_win_length: 2048
  time_transformer_depth: 1
training:
  batch_size: 1
  coarse_loss_clip: true
  ema_momentum: 0.999
  grad_clip: 0
  gradient_accumulation_steps: 1
  instruments:
  - vocals
  - other
  lr: 2.0e-07
  num_epochs: 1000
  num_steps: 5000
  optimizer: adamw8bit
  other_fix: true
  patience: 2
  q: 0.95
  reduce_factor: 0.95
  target_instrument: vocals
  use_amp: true
