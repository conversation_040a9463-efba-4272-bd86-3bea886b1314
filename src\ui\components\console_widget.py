"""
控制台输出组件
"""

import datetime
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTextEdit, QPushButton, 
    QLabel, QFrame, QComboBox, QCheckBox
)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QFont, QTextCursor, QColor

from ...utils.constants import Colors, Styles


class ConsoleWidget(QWidget):
    """控制台输出组件"""
    
    # 信号
    command_executed = Signal(str)  # 命令执行信号
    
    def __init__(self):
        super().__init__()
        self.max_lines = 1000  # 最大行数限制
        self.auto_scroll = True
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(Styles.MARGIN_SMALL)
        
        # 控制栏
        self.create_control_bar(layout)
        
        # 控制台输出区域
        self.create_console_area(layout)
        
        # 命令输入区域（可选）
        self.create_command_area(layout)
        
    def create_control_bar(self, layout):
        """创建控制栏"""
        control_frame = QFrame()
        control_layout = QHBoxLayout(control_frame)
        control_layout.setContentsMargins(0, 0, 0, 0)
        control_layout.setSpacing(Styles.MARGIN_SMALL)
        
        # 标题
        title_label = QLabel("控制台输出")
        title_label.setStyleSheet(f"""
        QLabel {{
            color: {Colors.FOREGROUND};
            font-weight: bold;
            font-size: {Styles.FONT_SIZE_NORMAL}px;
        }}
        """)
        
        # 日志级别过滤
        level_label = QLabel("级别:")
        level_label.setStyleSheet(f"color: {Colors.SECONDARY_TEXT}; font-size: {Styles.FONT_SIZE_SMALL}px;")
        
        self.level_combo = QComboBox()
        self.level_combo.addItems(["全部", "INFO", "WARNING", "ERROR", "DEBUG"])
        self.level_combo.setCurrentText("全部")
        self.level_combo.setStyleSheet(f"""
        QComboBox {{
            background-color: {Colors.INPUT_BG};
            border: 1px solid {Colors.BORDER};
            border-radius: {Styles.INPUT_RADIUS};
            padding: 2px 8px;
            color: {Colors.FOREGROUND};
            font-size: {Styles.FONT_SIZE_SMALL}px;
            min-width: 60px;
        }}
        """)
        self.level_combo.currentTextChanged.connect(self.filter_by_level)
        
        # 自动滚动开关
        self.auto_scroll_checkbox = QCheckBox("自动滚动")
        self.auto_scroll_checkbox.setChecked(True)
        self.auto_scroll_checkbox.setStyleSheet(f"""
        QCheckBox {{
            color: {Colors.FOREGROUND};
            font-size: {Styles.FONT_SIZE_SMALL}px;
        }}
        QCheckBox::indicator {{
            width: 12px;
            height: 12px;
        }}
        QCheckBox::indicator:unchecked {{
            background-color: {Colors.INPUT_BG};
            border: 1px solid {Colors.BORDER};
            border-radius: 2px;
        }}
        QCheckBox::indicator:checked {{
            background-color: {Colors.ACCENT};
            border: 1px solid {Colors.ACCENT};
            border-radius: 2px;
        }}
        """)
        self.auto_scroll_checkbox.toggled.connect(self.set_auto_scroll)
        
        # 清空按钮
        clear_button = QPushButton("清空")
        clear_button.setStyleSheet(f"""
        QPushButton {{
            background-color: {Colors.WARNING};
            border: none;
            border-radius: {Styles.BUTTON_RADIUS};
            padding: 4px 12px;
            color: white;
            font-size: {Styles.FONT_SIZE_SMALL}px;
        }}
        QPushButton:hover {{
            background-color: #D97706;
        }}
        """)
        clear_button.clicked.connect(self.clear_console)
        
        # 导出按钮
        export_button = QPushButton("导出")
        export_button.setStyleSheet(f"""
        QPushButton {{
            background-color: {Colors.SUCCESS};
            border: none;
            border-radius: {Styles.BUTTON_RADIUS};
            padding: 4px 12px;
            color: white;
            font-size: {Styles.FONT_SIZE_SMALL}px;
        }}
        QPushButton:hover {{
            background-color: #059669;
        }}
        """)
        export_button.clicked.connect(self.export_logs)
        
        # 布局控制栏
        control_layout.addWidget(title_label)
        control_layout.addStretch()
        control_layout.addWidget(level_label)
        control_layout.addWidget(self.level_combo)
        control_layout.addWidget(self.auto_scroll_checkbox)
        control_layout.addWidget(clear_button)
        control_layout.addWidget(export_button)
        
        layout.addWidget(control_frame)
        
    def create_console_area(self, layout):
        """创建控制台输出区域"""
        self.console_text = QTextEdit()
        self.console_text.setReadOnly(True)
        self.console_text.setMinimumHeight(200)
        
        # 设置等宽字体
        font = QFont("Consolas", Styles.FONT_SIZE_SMALL)
        if not font.exactMatch():
            font = QFont("Monaco", Styles.FONT_SIZE_SMALL)
        if not font.exactMatch():
            font = QFont("Courier New", Styles.FONT_SIZE_SMALL)
        self.console_text.setFont(font)
        
        self.console_text.setStyleSheet(f"""
        QTextEdit {{
            background-color: {Colors.INPUT_BG};
            border: 1px solid {Colors.BORDER};
            border-radius: {Styles.INPUT_RADIUS};
            padding: {Styles.PADDING_SMALL}px;
            color: {Colors.FOREGROUND};
            selection-background-color: {Colors.ACCENT};
        }}
        """)
        
        # 添加初始消息
        self.add_message("INFO", "控制台已初始化")
        
        layout.addWidget(self.console_text)
        
    def create_command_area(self, layout):
        """创建命令输入区域（可选功能）"""
        # 这个功能可以用于调试或高级用户
        # 暂时隐藏，可以根据需要启用
        pass
        
    def add_message(self, level: str, message: str, timestamp: bool = True):
        """添加消息到控制台"""
        # 生成时间戳
        if timestamp:
            now = datetime.datetime.now()
            time_str = now.strftime("%H:%M:%S")
        else:
            time_str = ""
            
        # 格式化消息
        if timestamp:
            formatted_message = f"[{time_str}] [{level}] {message}"
        else:
            formatted_message = f"[{level}] {message}"
            
        # 设置颜色
        color = self.get_level_color(level)
        
        # 添加到控制台
        cursor = self.console_text.textCursor()
        cursor.movePosition(QTextCursor.End)
        
        # 设置文本颜色
        format = cursor.charFormat()
        format.setForeground(QColor(color))
        cursor.setCharFormat(format)
        
        # 插入文本
        cursor.insertText(formatted_message + "\n")
        
        # 限制行数
        self.limit_lines()
        
        # 自动滚动
        if self.auto_scroll:
            self.scroll_to_bottom()
            
    def get_level_color(self, level: str) -> str:
        """获取日志级别对应的颜色"""
        colors = {
            "INFO": Colors.FOREGROUND,
            "WARNING": Colors.WARNING,
            "ERROR": Colors.ERROR,
            "SUCCESS": Colors.SUCCESS,
            "DEBUG": Colors.SECONDARY_TEXT,
        }
        return colors.get(level, Colors.FOREGROUND)
        
    def add_info(self, message: str):
        """添加信息消息"""
        self.add_message("INFO", message)
        
    def add_warning(self, message: str):
        """添加警告消息"""
        self.add_message("WARNING", message)
        
    def add_error(self, message: str):
        """添加错误消息"""
        self.add_message("ERROR", message)
        
    def add_success(self, message: str):
        """添加成功消息"""
        self.add_message("SUCCESS", message)
        
    def add_debug(self, message: str):
        """添加调试消息"""
        self.add_message("DEBUG", message)
        
    def filter_by_level(self, level: str):
        """按级别过滤日志"""
        # TODO: 实现日志过滤功能
        # 这需要存储所有消息并根据级别重新显示
        pass
        
    def set_auto_scroll(self, enabled: bool):
        """设置自动滚动"""
        self.auto_scroll = enabled
        
    def scroll_to_bottom(self):
        """滚动到底部"""
        scrollbar = self.console_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
        
    def limit_lines(self):
        """限制行数"""
        document = self.console_text.document()
        if document.blockCount() > self.max_lines:
            # 删除最早的行
            cursor = QTextCursor(document)
            cursor.movePosition(QTextCursor.Start)
            cursor.movePosition(QTextCursor.Down, QTextCursor.KeepAnchor, 
                              document.blockCount() - self.max_lines)
            cursor.removeSelectedText()
            
    def clear_console(self):
        """清空控制台"""
        self.console_text.clear()
        self.add_message("INFO", "控制台已清空", timestamp=True)
        
    def export_logs(self):
        """导出日志"""
        # TODO: 实现日志导出功能
        self.add_info("日志导出功能待实现")
        
    def get_text(self) -> str:
        """获取控制台文本"""
        return self.console_text.toPlainText()
        
    def set_max_lines(self, max_lines: int):
        """设置最大行数"""
        self.max_lines = max_lines
