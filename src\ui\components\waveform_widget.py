"""
波形显示组件
"""

import numpy as np
from pathlib import Path
from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel, QFrame
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QPainter, QPen, QColor, QFont
import pyqtgraph as pg

from ...utils.constants import Colors, Styles


class WaveformWidget(QWidget):
    """波形显示组件"""
    
    # 信号
    position_clicked = Signal(float)  # 点击位置信号 (0.0-1.0)
    
    def __init__(self):
        super().__init__()
        self.audio_data = None
        self.sample_rate = 44100
        self.duration = 0
        self.current_position = 0
        self.max_points = 2000  # 最大显示点数，用于性能优化
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(Styles.MARGIN_SMALL)
        
        # 标题
        title_label = QLabel("波形显示")
        title_label.setStyleSheet(f"""
        QLabel {{
            color: {Colors.FOREGROUND};
            font-weight: bold;
            font-size: {Styles.FONT_SIZE_NORMAL}px;
            padding: {Styles.PADDING_SMALL}px;
        }}
        """)
        layout.addWidget(title_label)
        
        # 波形图容器
        self.waveform_frame = QFrame()
        self.waveform_frame.setMinimumHeight(150)
        self.waveform_frame.setStyleSheet(f"""
        QFrame {{
            background-color: {Colors.WAVEFORM_BG};
            border: 1px solid {Colors.BORDER};
            border-radius: {Styles.INPUT_RADIUS};
        }}
        """)
        
        # 创建pyqtgraph绘图组件
        self.setup_plot_widget()
        
        waveform_layout = QVBoxLayout(self.waveform_frame)
        waveform_layout.setContentsMargins(1, 1, 1, 1)
        waveform_layout.addWidget(self.plot_widget)
        
        layout.addWidget(self.waveform_frame)
        
        # 信息显示
        self.info_label = QLabel("未加载音频文件")
        self.info_label.setStyleSheet(f"""
        QLabel {{
            color: {Colors.SECONDARY_TEXT};
            font-size: {Styles.FONT_SIZE_SMALL}px;
            padding: {Styles.PADDING_SMALL}px;
        }}
        """)
        layout.addWidget(self.info_label)
        
    def setup_plot_widget(self):
        """设置绘图组件"""
        # 创建pyqtgraph绘图组件
        self.plot_widget = pg.PlotWidget()
        self.plot_widget.setBackground(Colors.WAVEFORM_BG)
        
        # 设置坐标轴
        self.plot_widget.setLabel('left', '振幅')
        self.plot_widget.setLabel('bottom', '时间 (秒)')
        self.plot_widget.showGrid(x=True, y=True, alpha=0.3)
        
        # 设置样式
        self.plot_widget.getAxis('left').setPen(Colors.SECONDARY_TEXT)
        self.plot_widget.getAxis('bottom').setPen(Colors.SECONDARY_TEXT)
        self.plot_widget.getAxis('left').setTextPen(Colors.SECONDARY_TEXT)
        self.plot_widget.getAxis('bottom').setTextPen(Colors.SECONDARY_TEXT)
        
        # 创建波形曲线
        self.waveform_curve = self.plot_widget.plot(
            pen=pg.mkPen(color=Colors.ACCENT, width=1)
        )
        
        # 创建播放位置指示线
        self.position_line = pg.InfiniteLine(
            pos=0, 
            angle=90, 
            pen=pg.mkPen(color=Colors.ERROR, width=2),
            movable=False
        )
        self.plot_widget.addItem(self.position_line)
        
        # 连接点击事件
        self.plot_widget.scene().sigMouseClicked.connect(self.on_plot_clicked)
        
    def load_audio_file(self, file_path: str):
        """加载音频文件"""
        try:
            # 这里应该使用音频处理库加载文件
            # 为了演示，我们生成一些示例数据
            self.generate_sample_waveform(file_path)
            
            # 更新信息显示
            file_name = Path(file_path).name
            duration_str = f"{self.duration:.2f}秒"
            sample_rate_str = f"{self.sample_rate}Hz"
            self.info_label.setText(f"{file_name} | {duration_str} | {sample_rate_str}")
            
            return True
            
        except Exception as e:
            self.info_label.setText(f"加载失败: {str(e)}")
            return False
            
    def generate_sample_waveform(self, file_path: str):
        """生成示例波形数据（实际应用中应该从音频文件读取）"""
        # 生成5秒的示例音频数据
        self.duration = 5.0
        self.sample_rate = 44100
        
        # 生成示例波形：正弦波 + 噪声
        t = np.linspace(0, self.duration, int(self.sample_rate * self.duration))
        frequency = 440  # A4音符
        waveform = np.sin(2 * np.pi * frequency * t) * 0.5
        waveform += np.random.normal(0, 0.1, len(waveform))  # 添加噪声
        
        # 应用包络（模拟音频衰减）
        envelope = np.exp(-t * 0.5)
        waveform *= envelope
        
        self.audio_data = waveform
        self.update_waveform_display()
        
    def update_waveform_display(self):
        """更新波形显示"""
        if self.audio_data is None:
            return
            
        # 降采样以提高性能
        data_length = len(self.audio_data)
        if data_length > self.max_points:
            # 使用峰值提取降采样
            downsample_ratio = data_length // self.max_points
            downsampled_data = self.peak_extract_downsample(self.audio_data, downsample_ratio)
        else:
            downsampled_data = self.audio_data
            
        # 创建时间轴
        time_axis = np.linspace(0, self.duration, len(downsampled_data))
        
        # 更新波形曲线
        self.waveform_curve.setData(time_axis, downsampled_data)
        
        # 设置坐标轴范围
        self.plot_widget.setXRange(0, self.duration)
        self.plot_widget.setYRange(-1.1, 1.1)
        
    def peak_extract_downsample(self, data, ratio):
        """峰值提取降采样算法"""
        if ratio <= 1:
            return data
            
        # 将数据分组
        groups = data.reshape(-1, ratio)
        
        # 提取每组的最大绝对值
        peaks = np.max(np.abs(groups), axis=1)
        
        # 保持原始符号
        signs = np.sign(groups[np.arange(len(groups)), np.argmax(np.abs(groups), axis=1)])
        
        return peaks * signs
        
    def set_position(self, position_ms):
        """设置播放位置（毫秒）"""
        if self.duration > 0:
            position_seconds = position_ms / 1000.0
            self.current_position = min(position_seconds, self.duration)
            self.position_line.setPos(self.current_position)
            
    def on_plot_clicked(self, event):
        """处理波形图点击事件"""
        if self.duration > 0:
            # 获取点击位置
            pos = event.scenePos()
            if self.plot_widget.sceneBoundingRect().contains(pos):
                # 转换为绘图坐标
                mouse_point = self.plot_widget.getViewBox().mapSceneToView(pos)
                clicked_time = mouse_point.x()
                
                # 限制在有效范围内
                if 0 <= clicked_time <= self.duration:
                    # 计算相对位置 (0.0-1.0)
                    relative_position = clicked_time / self.duration
                    self.position_clicked.emit(relative_position)
                    
    def clear_waveform(self):
        """清空波形显示"""
        self.audio_data = None
        self.duration = 0
        self.current_position = 0
        self.waveform_curve.setData([], [])
        self.position_line.setPos(0)
        self.info_label.setText("未加载音频文件")
        
    def get_duration(self):
        """获取音频时长（秒）"""
        return self.duration
        
    def get_sample_rate(self):
        """获取采样率"""
        return self.sample_rate
