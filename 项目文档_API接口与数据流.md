# 木偶AI翻唱项目 - API接口与数据流文档

## 1. 数据流程概览

### 1.1 完整处理流程

```mermaid
graph TD
    A[音频文件上传] --> B[格式标准化]
    B --> C[音频分离MSST]
    C --> D[人声提取]
    C --> E[伴奏提取]
    D --> F[F0基频提取]
    D --> G[音量包络提取]
    F --> H[音色转换Reflow]
    G --> H
    H --> I[声码器合成]
    I --> J[混响处理]
    E --> K[伴奏处理]
    J --> L[最终混音]
    K --> L
    L --> M[输出结果]
```

### 1.2 数据格式转换链

```
原始音频 → 标准化(44100Hz/WAV) → 分离处理 → 特征提取 → AI推理 → 音频合成 → 后处理 → 最终输出
```

## 2. 内部API接口设计

### 2.1 音频处理核心接口

#### 音频标准化接口
```python
def standardize_audio(input_file: str) -> str:
    """
    将任何格式的音频文件转换为标准的44100Hz采样率WAV格式
    
    Args:
        input_file: 输入音频文件路径
    
    Returns:
        标准化后的临时WAV文件路径
    
    Raises:
        Exception: 音频转换失败时抛出异常
    """
```

#### 音频分离接口
```python
def separate_audio(input_file: str, output_dir: str, mode: str = "complete") -> Dict[str, str]:
    """
    使用MSST进行音频分离
    
    Args:
        input_file: 输入音频文件路径
        output_dir: 输出目录
        mode: 处理模式 ("complete" | "fast")
    
    Returns:
        {
            "vocal": "人声文件路径",
            "instrumental": "伴奏文件路径",
            "status": "success" | "error",
            "message": "处理信息"
        }
    """
```

#### 音色转换接口
```python
def voice_conversion(
    input_file: str,
    model_path: str,
    config_path: str = None,
    key_shift: int = 0,
    formant_shift: int = 0,
    **kwargs
) -> Dict[str, Any]:
    """
    使用Reflow模型进行音色转换
    
    Args:
        input_file: 输入音频文件路径
        model_path: 模型文件路径
        config_path: 配置文件路径
        key_shift: 音高调节（半音数）
        formant_shift: 共振峰偏移
    
    Returns:
        {
            "output_file": "输出文件路径",
            "processing_time": "处理时间(秒)",
            "status": "success" | "error",
            "message": "处理信息"
        }
    """
```

### 2.2 模型管理接口

#### 模型扫描接口
```python
def scan_models() -> Dict[str, List[Dict]]:
    """
    扫描models目录，获取可用模型和配置文件
    
    Returns:
        {
            "models": [
                {
                    "name": "模型名称",
                    "path": "模型路径",
                    "size": "文件大小",
                    "modified": "修改时间"
                }
            ],
            "configs": [
                {
                    "name": "配置名称",
                    "path": "配置路径",
                    "compatible_models": ["兼容的模型列表"]
                }
            ]
        }
    """
```

#### 模型配置解析接口
```python
def parse_model_config(config_path: str) -> Dict[str, Any]:
    """
    解析模型配置文件
    
    Args:
        config_path: 配置文件路径
    
    Returns:
        {
            "data": {
                "sampling_rate": 44100,
                "block_size": 512,
                "encoder": "contentvec768l12tta2x",
                "f0_extractor": "rmvpe"
            },
            "model": {
                "type": "RectifiedFlow",
                "n_spk": 1,
                "use_pitch_aug": true
            },
            "vocoder": {
                "type": "nsf-hifigan",
                "ckpt": "pretrain/Vocoder/..."
            }
        }
    """
```

### 2.3 进度追踪接口

#### 任务状态管理
```python
class ProcessingTask:
    def __init__(self, task_id: str, task_type: str):
        self.task_id = task_id
        self.task_type = task_type  # "separation" | "conversion" | "mixing"
        self.status = "pending"     # "pending" | "running" | "completed" | "error"
        self.progress = 0.0         # 0.0 - 1.0
        self.message = ""
        self.start_time = None
        self.end_time = None
        self.result = None

def get_task_status(task_id: str) -> Dict[str, Any]:
    """获取任务状态"""
    
def update_task_progress(task_id: str, progress: float, message: str = ""):
    """更新任务进度"""
```

## 3. 外部API设计（重构后的FastAPI服务）

### 3.1 RESTful API端点

#### 健康检查
```http
GET /health
Response: {
    "status": "healthy",
    "version": "1.0.0",
    "gpu_available": true,
    "models_loaded": 3
}
```

#### 模型管理
```http
GET /models/list
Response: {
    "models": [...],
    "configs": [...]
}

GET /models/scan
Response: {
    "message": "Models rescanned successfully",
    "count": 5
}
```

#### 音频分离
```http
POST /separate
Content-Type: multipart/form-data

Request:
- file: 音频文件
- mode: "complete" | "fast"
- output_format: "wav" | "mp3"

Response: {
    "task_id": "uuid",
    "status": "accepted",
    "estimated_time": 120
}
```

#### 音色转换
```http
POST /convert
Content-Type: multipart/form-data

Request:
- file: 音频文件
- model: 模型名称
- config: 配置名称（可选）
- key_shift: 音高调节
- formant_shift: 共振峰偏移

Response: {
    "task_id": "uuid",
    "status": "accepted",
    "estimated_time": 180
}
```

#### 进度查询
```http
GET /progress/{task_id}
Response: {
    "task_id": "uuid",
    "status": "running",
    "progress": 0.65,
    "message": "Processing audio segment 13/20",
    "estimated_remaining": 45
}
```

#### 结果下载
```http
GET /download/{task_id}
Response: 音频文件流
```

### 3.2 WebSocket实时通信

#### 连接建立
```javascript
const ws = new WebSocket('ws://localhost:8000/ws/{task_id}');

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    // 处理进度更新
    updateProgress(data.progress, data.message);
};
```

#### 消息格式
```json
{
    "type": "progress",
    "task_id": "uuid",
    "progress": 0.75,
    "message": "Applying reverb effects...",
    "timestamp": "2024-01-01T12:00:00Z"
}
```

## 4. 数据模型定义

### 4.1 音频文件模型
```python
class AudioFile:
    def __init__(self):
        self.filepath: str = ""
        self.filename: str = ""
        self.format: str = ""
        self.duration: float = 0.0
        self.sample_rate: int = 0
        self.channels: int = 0
        self.file_size: int = 0
        self.md5_hash: str = ""
        self.created_at: datetime = None
```

### 4.2 处理任务模型
```python
class ProcessingTask:
    def __init__(self):
        self.task_id: str = ""
        self.task_type: str = ""  # "separation" | "conversion" | "mixing"
        self.status: str = "pending"
        self.progress: float = 0.0
        self.message: str = ""
        self.input_file: AudioFile = None
        self.output_files: List[AudioFile] = []
        self.parameters: Dict[str, Any] = {}
        self.start_time: datetime = None
        self.end_time: datetime = None
        self.processing_time: float = 0.0
        self.error_message: str = ""
```

### 4.3 模型信息模型
```python
class ModelInfo:
    def __init__(self):
        self.name: str = ""
        self.path: str = ""
        self.config_path: str = ""
        self.model_type: str = ""  # "RectifiedFlow" | "DDSP" | etc.
        self.vocoder_type: str = ""
        self.sample_rate: int = 44100
        self.n_speakers: int = 1
        self.supports_pitch_aug: bool = False
        self.file_size: int = 0
        self.created_at: datetime = None
        self.compatible_configs: List[str] = []
```

### 4.4 用户设置模型
```python
class UserSettings:
    def __init__(self):
        self.default_model: str = ""
        self.default_config: str = ""
        self.output_format: str = "wav"
        self.output_quality: str = "high"
        self.enable_reverb: bool = True
        self.reverb_settings: Dict[str, float] = {
            "room_size": 0.3,
            "damping": 0.1,
            "wet_level": 0.2,
            "dry_level": 0.9
        }
        self.gpu_enabled: bool = True
        self.cache_enabled: bool = True
        self.auto_cleanup: bool = True
```

## 5. 错误处理与异常管理

### 5.1 异常类型定义
```python
class AudioProcessingError(Exception):
    """音频处理相关异常"""
    pass

class ModelLoadError(Exception):
    """模型加载异常"""
    pass

class ConfigurationError(Exception):
    """配置文件异常"""
    pass

class InsufficientResourceError(Exception):
    """资源不足异常"""
    pass
```

### 5.2 错误响应格式
```json
{
    "error": {
        "code": "AUDIO_PROCESSING_ERROR",
        "message": "Failed to process audio file",
        "details": "Unsupported audio format: .xyz",
        "timestamp": "2024-01-01T12:00:00Z",
        "task_id": "uuid"
    }
}
```

## 6. 性能监控与日志

### 6.1 性能指标
- 处理时间统计
- 内存使用监控
- GPU利用率追踪
- 并发任务数量
- 错误率统计

### 6.2 日志格式
```
2024-01-01 12:00:00 [INFO] Task uuid started: voice_conversion
2024-01-01 12:00:05 [DEBUG] F0 extraction completed: 512 frames
2024-01-01 12:00:15 [INFO] Model inference completed: 10.2s
2024-01-01 12:00:20 [INFO] Task uuid completed successfully: 20.5s total
```

这个API接口与数据流文档为项目的重构和API服务开发提供了详细的技术规范。
