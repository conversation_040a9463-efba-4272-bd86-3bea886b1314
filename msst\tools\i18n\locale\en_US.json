{"仅供个人娱乐和非商业用途, 禁止用于血腥/暴力/性相关/政治相关内容。[点击前往教程文档](https://r1kc63iz15l.feishu.cn/wiki/JSp3wk7zuinvIXkIqSUcCXY1nKc)<br>本整合包完全免费, 严禁以任何形式倒卖, 如果你从任何地方**付费**购买了本整合包, 请**立即退款**。<br> 整合包作者: [bilibili@阿狸不吃隼舞](https://space.bilibili.com/403335715) [Github@KitsuneX07](https://github.com/KitsuneX07) | [Bilibili@Sucial](https://space.bilibili.com/445022409) [Github@SUC-DriverOld](https://github.com/SUC-DriverOld) | Gradio主题: [Gradio Theme](https://huggingface.co/spaces/NoCrypt/miku)": "For personal entertainment and non-commercial use only<br>Package authors: [Github@KitsuneX07](https://github.com/KitsuneX07) | [Github@SUC-DriverOld](https://github.com/SUC-DriverOld) | Gradio theme: [Gradio Theme](https://huggingface.co/spaces/NoCrypt/miku)", "MSST分离": "MSST Separation", "UVR分离": "UVR Separation", "预设流程": "Preset flow", "合奏模式": "Ensemble mode", "小工具": "Tools", "安装模型": "Install Model", "MSST训练": "MSST Training", "设置": "Settings", "输出音轨": "Output track", "请至少添加2个模型到合奏流程": "Please add at least 2 models to the ensemble process.", "合奏流程已保存": "Ensemble process saved.", "请上传至少一个音频文件!": "Please upload at least one audio file!", "请先创建合奏流程": "Please create an ensemble process first.", "模型": "Model ", "不存在": "does not exist", "用户强制终止": "User forced termination", "处理失败: ": "Processing failed: ", "处理完成, 成功: ": "Processing completed, success: ", "个文件, 失败: ": " files, failed: ", "个文件": " files", ", 结果已保存至: ": ", results saved to: ", ", 耗时: ": ", Time taken: ", "请上传至少2个文件": "Please upload at least 2 files", "上传的文件数目与权重数目不匹配": "Uploaded file number does not match weight number", "处理完成, 文件已保存为: ": "Processing complete, file saved as: ", "处理失败!": "Processing failed!", "input文件夹内文件列表:\\n": "File List in Input Folder:\\n", "input文件夹为空\\n": "Input Folder is Empty\\n", "results文件夹内文件列表:\\n": "File List in Results Folder:\\n", "results文件夹为空\\n": "Results Folder is Empty\\n", "已删除input文件夹内所有文件": "All Files in Input Folder Have Been Deleted", "已删除results文件夹内所有文件": "All Files in Results Folder Have Been Deleted", "请先选择模型": "Please select a model first", "仅输出主音轨": "Output main track only", "仅输出次音轨": "Output secondary track only", "已打开下载管理器": "Download manager opened", "选择模型": "Select model", "模型名字": "Model Name", "模型类型": "Model category", "主要提取音轨": "Primary Stem", "次要提取音轨": "Secondary Stem", "下载链接": "Download Link", "模型类别": "Model type", "可提取音轨": "Extractable Tracks", "模型大小": "Model Size", "模型已安装": "Model installed", "无法校验sha256": "Unable to verify sha256.", "sha256校验失败": "sha256 verification failed", "模型sha256校验失败, 请重新下载": "Model sha256 verification failed, please download again", "sha256校验成功": "sha256 verification successful", "模型未安装": "Model not installed", "请选择模型类型": "Please select model category", "已安装": "Installed", "请手动删除后重新下载": "Please delete manually and re-download.", "下载成功": "Download successful", "下载失败": "Download failed", "已安装。请勿重复安装。": "Already installed. Please do not reinstall.", "已打开": "Opened", "的下载链接": "Download link", "上传参数文件": "Upload parameter file", "上传参数": "Upload parameters", "请上传'.yaml'格式的配置文件": "Please upload the configuration file in '.yaml' format", "请上传'ckpt', 'chpt', 'th'格式的模型文件": "Please upload model files in 'ckpt', 'chpt', 'th' format", "请输入正确的模型类别和模型类型": "Please enter the correct model category and model type", "安装成功。重启WebUI以刷新模型列表": "Installation was successful. Restart WebUI to refresh the model list", "安装失败": "Installation failure", "请输入选择模型参数": "Please enter the selected model parameters", "请上传'.json'格式的参数文件": "Please upload the parameter file in '.json' format", "请上传'.pth'格式的模型文件": "Please upload the model file in '.pth' format", "请输入正确的音轨名称": "Please enter the correct stem name", "配置保存成功!": "Configuration saved successfully!", "非官方模型不支持重置配置!": "Unofficial models do not support resetting configurations!", "配置重置成功!": "Configuration reset successfully!", "备份文件不存在!": "Backup file does not exist!", "选择输出音轨": "Select output stems", "请选择模型": "Please select a model", "请选择输入目录": "Please select input directory", "请选择输出目录": "Please select output directory", "请选择GPU": "Please select a GPU", "处理完成, 结果已保存至: ": "Processing complete, result saved to: ", "暂无备份文件": "No backup file available", "作为下一模型输入(或结果输出)的音轨": "Track as input for the next model (or output result)", "直接保存至输出目录的音轨(可多选)": "Tracks directly saved to output directory (multiple selections allowed)", "不输出": "No output", "请填写预设名称": "Please fill in preset name", "预设": "Preset", "保存成功": "Saved successfully", "请选择预设": "Please select a preset", "不支持的预设版本: ": "Unsupported preset version: ", ", 请重新制作预设。": ", please recreate the preset.", "预设版本不支持": "Preset version not supported.", "预设不存在": "Preset does not exist", "删除成功": "Deleted successfully", "预设已删除": "Preset deleted", "选择需要恢复的预设流程备份": "Select the preset process backup to restore", "已成功恢复备份": "Backup successfully restored", "设置重置成功, 请重启WebUI刷新! ": "Settings reset successfully, please restart WebUI to refresh!", "记录重置成功, 请重启WebUI刷新! ": "Records reset successfully, please restart WebUI to refresh!", "请选择正确的模型目录": "Please select the correct model directory!", "设置保存成功! 请重启WebUI以应用。": "Settings saved successfully! Please restart WebUI to apply.", "当前版本: ": "Current version: ", ", 发现新版本: ": ", new version found: ", ", 已是最新版本": ", already the latest version", "检查更新失败": "Check for updates failed", "语言已更改, 重启WebUI生效": "The language has been changed, restarting the WebUI will take effect.", "成功将端口设置为": "Port successfully set to", ", 重启WebUI生效": ", restarting the WebUI will take effect.", "huggingface.co (需要魔法)": "huggingface.co", "hf-mirror.com (镜像站可直连)": "hf-mirror.com (for users in China)", "下载链接已更改": "Download changed successfully.", "公共链接已开启, 重启WebUI生效": "The public link has been activated, restarting the WebUI will take effect", "公共链接已关闭, 重启WebUI生效": "The public link has been closed, restarting the WebUI will take effect", "已开启局域网分享, 重启WebUI生效": "LAN sharing has been enabled, restarting thr WebUI will take effect", "已关闭局域网分享, 重启WebUI生效": "LAN sharing has been disabled, restarting thr WebUI will take effect", "已开启自动清理缓存": "Automatic cache cleaning has been enabled", "已关闭自动清理缓存": "Automatic cache cleaning has been disabled", "已开启调试模式": "Debug mode enabled", "已关闭调试模式": "Debug mode disabled", "主题已更改, 重启WebUI生效": "Theme changed, restart WebUI to take effect", "音频设置已保存": "Audio settings saved.", "已重命名为": "is renamed as ", "选择模型类型": "Select model category", "请先选择模型类型": "Please select model category first", "新模型名称后缀错误!": "The suffix of the new model name is incorrect!", "模型名字已存在! 请重新命名!": "The model name already exists! Please rename!", "重命名失败!": "<PERSON><PERSON> failed!", "检测到": "Detected", "旧版配置, 正在更新至最新版": "old configuration, updating to the latest version", "成功清理Gradio缓存": "Successfully cleared Gradio cache", "请上传至少一个文件": "Please upload at least one file", "单声道": "Mono", "处理完成, 成功转换: ": "Processing complete, successfully converted: ", "请先下载SOME预处理模型并放置在tools/SOME_weights文件夹下! ": "Please download SOME preprocessing model and place it in the tools/SOME_weights folder first!", "请先选择模型保存路径! ": "Please select the model save path first!", "初始模型": "Initial model", "模型类型错误, 请重新选择": "Model type error, please reselect", "配置文件不存在, 请重新选择": "Configuration file does not exist, please reselect", "数据集路径不存在, 请重新选择": "Dataset path does not exist, please reselect", "验证集路径不存在, 请重新选择": "Validation set path does not exist, please reselect", "数据集类型错误, 请重新选择": "Dataset type error, please reselect", "训练启动成功! 请前往控制台查看训练信息! ": "Training started successfully! Please go to the console to view the training information!", "模型不存在, 请重新选择": "Model does not exist, please reselect", "验证完成! 请打开输出文件夹查看详细结果": "Validation complete! Please open the output folder to view detailed results", "错误: 无法找到增强配置文件模板, 请检查文件configs/augmentations_template.yaml是否存在。": "Error: Unable to find augmentation configuration file template, please check if configs/augmentations_template.yaml exists.", "已开启调试日志": "Debug logging enabled", "已关闭调试日志": "Debug logging disabled", "模型不存在!": "Model does not exist!", "输入音频分离": "Input audio separation", "输入文件夹分离": "Input folder separation", "请先选择文件夹!": "Please select a folder first!", "显存不足, 请尝试减小batchsize值和chunksize值后重试。": "Insufficient VRAM, please try reducing the batch size and chunk size values, then retry.", "内存不足，请尝试增大虚拟内存后重试。若分离时出现此报错，也可尝试将推理音频裁切短一些，分段分离。": "Insufficient memory, please try increasing virtual memory and retry. If this error occurs during separation, you can also try trimming the inference audio and separating it in segments.", "FFmpeg未找到，请检查FFmpeg是否正确安装。若使用的是整合包，请重新安装。": "FFmpeg not found, please check if FFmpeg is installed correctly. If using an integrated package, please reinstall.", "模型损坏，请重新下载并安装模型后重试。": "Model corrupted, please re-download and install the model before retrying.", "文件或路径不存在，请根据错误指示检查是否存在该文件。": "File or path does not exist, please check according to the error message.", "合奏模式可用于集成不同算法的结果, 具体的文档位于/docs/ensemble.md。目前主要有以下两种合奏方式:<br>1. 从原始音频合奏: 直接上传一个或多个音频文件, 然后选择多个模型进行处理, 将这些处理结果根据选择的合奏模式进行合奏<br>2. 从分离结果合奏: 上传多个已经分离完成的结果音频, 然后选择合奏模式进行合奏": "Ensemble mode can be used to integrate the results of different algorithms. Specific documentation is available at /docs/ensemble.md. Currently, there are two main ensemble methods:<br>1. Ensemble from original audio: Upload one or more audio files, then select multiple models for processing, and ensemble these results based on the selected ensemble mode.<br>2. Ensemble from separation results: Upload multiple already-separated result audios, then choose an ensemble mode for processing.", "从原始音频合奏": "Ensemble from original audio", "从原始音频合奏需要上传至少一个音频文件, 然后选择多个模型先进行分离处理, 然后将这些处理结果根据选择的合奏模式进行合奏。<br>注意, 请确保你的磁盘空间充足, 合奏过程会产生的临时文件仅会在处理结束后删除。": "To ensemble from original audio, upload at least one audio file, select multiple models for separation processing, and then ensemble these results based on the chosen mode.<br>Note: Ensure sufficient disk space, as temporary files generated during the process will only be deleted after completion.", "制作合奏流程": "Create ensemble process", "权重": "Weight", "添加到合奏流程": "Add to ensemble process", "撤销上一步": "Undo last step", "全部清空": "Clear all", "合奏流程": "Ensemble process", "保存此合奏流程": "Save this ensemble process", "集成模式": "Ensemble mode", "输出格式": "Output format", "使用CPU (注意: 使用CPU会导致速度非常慢) ": "Use CPU (Note: Using CPU will result in very slow performance)", "使用TTA (测试时增强), 可能会提高质量, 但时间x3": "Using TTA (enhanced during testing) may improve quality, but the speed is slightly slower", "输出次级音轨 (例如: 合奏人声时, 同时输出伴奏)": "Output secondary tracks (e.g., output accompaniment when ensembling vocals)", "输入音频": "Input audio", "上传一个或多个音频文件": "Upload one or more audio files", "输入文件夹": "Input folder", "输入目录": "Input directory", "选择文件夹": "Select folder", "打开文件夹": "Open folder", "输出目录": "Output directory", "从分离结果合奏": "Ensemble from separation results", "从分离结果合奏需要上传至少两个音频文件, 这些音频文件是使用不同的模型分离同一段音频的结果。因此, 上传的所有音频长度应该相同。": "To ensemble from separation results, upload at least two audio files that are results of separating the same audio using different models. Therefore, all uploaded audio files should have the same length.", "上传多个音频文件": "Upload multiple audio files", "权重(以空格分隔, 数量要与上传的音频一致)": "Weights (separated by spaces, the number must match the uploaded audio)", "运行": "Run", "强制停止": "Forced Stop", "### 集成模式": "### Ensemble Mode", "1. `avg_wave`: 在1D变体上进行集成, 独立地找到波形的每个样本的平均值<br>2. `median_wave`: 在1D变体上进行集成, 独立地找到波形的每个样本的中位数<br>3. `min_wave`: 在1D变体上进行集成, 独立地找到波形的每个样本的最小绝对值<br>4. `max_wave`: 在1D变体上进行集成, 独立地找到波形的每个样本的最大绝对值<br>5. `avg_fft`: 在频谱图 (短时傅里叶变换 (STFT) 2D变体) 上进行集成, 独立地找到频谱图的每个像素的平均值。平均后使用逆STFT得到原始的1D波形<br>6. `median_fft`: 与avg_fft相同, 但使用中位数代替平均值 (仅在集成3个或更多来源时有用) <br>7. `min_fft`: 与avg_fft相同, 但使用最小函数代替平均值 (减少激进程度) <br>8. `max_fft`: 与avg_fft相同, 但使用最大函数代替平均值 (增加激进程度) ": "1. `avg_wave`: Ensemble on the 1D variant, independently finding the average of each sample of the waveform. <br>2. `median_wave`: Ensemble on the 1D variant, independently finding the median of each sample of the waveform. <br>3. `min_wave`: Ensemble on the 1D variant, independently finding the smallest absolute value of each sample of the waveform. <br>4. `max_wave`: Ensemble on the 1D variant, independently finding the largest absolute value of each sample of the waveform. <br>5. `avg_fft`: Ensemble on the spectrogram (Short-Time Fourier Transform (STFT) 2D variant), independently finding the average of each pixel of the spectrogram. The average is then used to obtain the original 1D waveform using inverse STFT. <br>6. `median_fft`: Same as avg_fft, but using the median instead of the average (useful only when ensembling 3 or more sources). <br>7. `min_fft`: Same as avg_fft, but using the minimum function instead of the average (reduces aggressiveness). <br>8. `max_fft`: Same as avg_fft, but using the maximum function instead of the average (increases aggressiveness).", "### 注意事项": "### Notes", "1. min_fft可用于进行更保守的合成, 它将减少更激进模型的影响。<br>2. 最好合成等质量的模型。在这种情况下, 它将带来增益。如果其中一个模型质量不好, 它将降低整体质量。<br>3. 在原仓库作者的实验中, 与其他方法相比, avg_wave在SDR分数上总是更好或相等。<br>4. 最终会在输出目录下生成一个`ensemble_<集成模式>.wav`。": "1. min_fft can be used for more conservative synthesis, reducing the impact of more aggressive models. <br>2. It is best to ensemble models of equal quality. In this case, it will provide gains. If one of the models is of poor quality, it will reduce the overall quality. <br>3. In the original author's experiments, avg_wave was always better or equal in SDR score compared to other methods. <br>4. A `ensemble_<ensemble mode>.wav` will be generated in the output directory.", "下载官方模型": "Download official models", "点击打开下载管理器": "Click to open download manager", "模型信息": "Model Information", "打开模型目录": "Open model directory", "自动下载": "Automatic Download", "手动下载": "Manual Download", "1. MSST模型默认下载在pretrain/<模型类型>文件夹下。UVR模型默认下载在设置中的UVR模型目录中。<br>2. 下加载进度可以打开终端查看。如果一直卡着不动或者速度很慢, 在确信网络正常的情况下请尝试重启WebUI。<br>3. 若下载失败, 会在模型目录**留下一个损坏的模型**, 请**务必**打开模型目录手动删除! <br>4. 点击“重启WebUI”按钮后, 会短暂性的失去连接, 随后会自动开启一个新网页。": "1. The MSST model is downloaded by default in the 'pretrain/<model type>' folder. The UVR model is downloaded by default from the UVR model directory in the settings.<br>2. The loading progress can be viewed by opening the terminal. If it remains stuck or slow, please try restarting WebUI while ensuring that the network is functioning properly.<br>3. If the download fails, a damaged model will be left in the model directory. Please **make sure** to open the model directory and manually delete it.<br>4. After clicking the 'Restart WebUI' button, the connection will be temporarily lost, and then a new webpage will automatically open.", "### 模型下载链接": "### Model download link", "1. 自动从Github, Huggingface或镜像站下载模型。<br>2. 你也可以在此整合包下载链接中的All_Models文件夹中找到所有可用的模型并下载。": "Automatically download models from Github, Huggingface, or mirror sites.", "若自动下载出现报错或下载过慢, 请点击手动下载, 跳转至下载链接。手动下载完成后, 请根据你选择的模型类型放置到对应文件夹内。": "If the automatic download reports an error or the download is too slow, please click the manual download to jump to the download link. After the manual download is completed, please place the models in the corresponding folder according to the type of model you selected.", "### 当前UVR模型目录: ": "### Current UVR Model Directory: ", ", 如需更改, 请前往设置页面。": ", if you need to change it, please go to the settings page.", "### 模型安装完成后, 需重启WebUI刷新模型列表": "### After the model installation is completed, it is necessary to restart the WebUI to refresh the model list.", "重启WebUI": "Restart WebUI", "安装非官方MSST模型": "Install unofficial MSST models", "你可以从其他途径获取非官方MSST模型, 在此页面完成配置文件设置后, 即可正常使用。<br>注意: 仅支持'.ckpt', '.th', '.chpt'格式的模型。模型显示名字为模型文件名。<br>选择模型类型: 共有三个可选项。依次代表人声相关模型, 多音轨分离模型, 单音轨分离模型。仅用于区分模型大致类型, 可任意选择。<br>选择模型类别: 此选项关系到模型是否能正常推理使用, 必须准确选择!": "You can obtain unofficial MSST models from other sources. After completing the configuration file settings on this page, you can use them normally.<br>Note: Only models in formats of '.ckpt', '.th', and '.chpt' are supported. The model display name is the model file name.<br>Select model category: There are three options available. Representing voice related models, multi track separation models, and single track separation models in sequence. Only used to distinguish the approximate types of models, can be freely selected.<br>Select model type: This option is related to whether the model can be used for normal inference and must be selected accurately!", "上传非官方MSST模型": "Upload unofficial MSST models", "上传非官方MSST模型配置文件": "Upload unofficial MSST model configuration file", "选择模型类别": "Select model type", "模型下载链接 (非必须，若无，可跳过)": "Model download link (not required, if not available, can be skipped)", "安装非官方VR模型": "Install unofficial VR models", "你可以从其他途径获取非官方UVR模型, 在此页面完成配置文件设置后, 即可正常使用。<br>注意: 仅支持'.pth'格式的模型。模型显示名字为模型文件名。": "You can obtain unofficial UVR models from other sources. After completing the configuration file settings on this page, you can use them normally.<br>Note: Only models in '.pth' format are supported. The model display name is the model file name.", "上传非官方VR模型": "Upload unofficial VR models", "主要音轨名称": "Primary stem name", "次要音轨名称": "Secondary stem name", "选择模型参数": "Select model parameters", "是否为Karaoke模型": "Is Karaoke model", "是否为BV模型": "Is BV model", "是否为VR 5.1模型": "Is VR 5.1 model", "MSST音频分离原项目地址: [https://github.com/ZFTurbo/Music-Source-Separation-Training](https://github.com/ZFTurbo/Music-Source-Separation-Training)": "MSST Audio Separation original project address: [https://github.com/ZFTurbo/Music-Source-Separation-Training](https://github.com/ZFTurbo/Music-Source-Separation-Training)", "选择使用的GPU": "Select the GPU to use", "强制使用CPU推理, 注意: 使用CPU推理速度非常慢!": "Force CPU inference, note: CPU inference is very slow!", "使用CPU": "Use CPU", "[点击展开] 推理参数设置, 不同模型之间参数相互独立": "[Click to expand] Inference parameter settings, parameters are independent between different models.", "只有在点击保存后才会生效。参数直接写入配置文件, 无法撤销。假如不知道如何设置, 请保持默认值。<br>请牢记自己修改前的参数数值, 防止出现问题以后无法恢复。请确保输入正确的参数, 否则可能会导致模型无法正常运行。<br>假如修改后无法恢复, 请点击``重置``按钮, 这会使得配置文件恢复到默认值。": "Changes will only take effect after clicking save. Parameters are written directly into the configuration file and cannot be undone. If you are unsure of the settings, please keep the default values.<br>Remember the parameter values before modifying them to prevent irreversible issues. Ensure you input correct parameters, or it may cause the model to malfunction.<br>If modifications cannot be undone, click the ``Reset`` button, which will restore the configuration file to its default values.", "批次大小, 减小此值可以降低显存占用, 此参数对推理效果影响不大": "Batch size, reducing this value can decrease VRAM usage, and it has little impact on inference quality.", "重叠数, 增大此值可以提高分离效果, 但会增加处理时间, 建议设置成4": "Overlap, increasing this value can improve separation quality but will increase processing time. A value of 4 is recommended.", "分块大小, 增大此值可以提高分离效果, 但会增加处理时间和显存占用": "Chunk size, increasing this value can improve separation quality but will increase processing time and VRAM usage.", "音频归一化, 对音频进行归一化输入和输出, 部分模型没有此功能": "Audio normalization, normalizes audio input and output. Some models do not have this function.", "启用TTA, 能小幅提高分离质量, 若使用, 推理时间x3": "Enable TTA, can slightly improve separation quality. If used, inference time will be tripled.", "保存配置": "Save configuration", "重置配置": "Reset configuration", "预设流程允许按照预设的顺序运行多个模型。每一个模型的输出将作为下一个模型的输入。": "Preset flows allow multiple models to run in a predetermined order. The output of each model will be used as the input for the next model.", "使用预设": "Use preset", "该模式下的UVR推理参数将直接沿用UVR分离页面的推理参数, 如需修改请前往UVR分离页面。<br>修改完成后, 还需要任意处理一首歌才能保存参数! ": "The UVR inference parameters in this mode will directly use the inference parameters from the UVR separation page. If you need to modify them, please go to the UVR separation page.<br>After the modification is completed, you still need to process any song to save the parameters!", "将次级输出保存至输出目录的单独文件夹内": "Save secondary output to extra_output in the output directory", "制作预设": "Create preset", "预设名称": "Preset name", "请输入预设名称": "Please enter preset name", "添加至流程": "Add to flow", "保存上述预设流程": "Save the above preset flow", "管理预设": "Manage presets", "此页面提供查看预设, 删除预设, 备份预设, 恢复预设等功能<br>`model_type`: 模型类型；`model_name`: 模型名称；`input_to_next`: 作为下一模型输入的音轨；`output_to_storage`: 直接保存至输出目录下的direct_output文件夹内的音轨, **不会经过后续流程处理**<br>每次点击删除预设按钮时, 将自动备份预设以免误操作。": "This page allows viewing presets, deleting presets, backing up presets, restoring presets, and more.<br>`model_type`: Model type; `model_name`: Model name; `input_to_next`: Track used as input for the next model; `output_to_storage`: Track saved directly to the `direct_output` folder in the output directory, **will not be processed in subsequent steps**.<br>Every time you click the delete preset button, presets will be automatically backed up to prevent accidental operations.", "删除所选预设": "Delete selected preset", "请先选择预设": "Please select a preset first", "恢复所选预设": "<PERSON><PERSON> selected preset", "打开备份文件夹": "Open Backup Folder", "WebUI设置": "WebUI Settings", "GPU信息": "GPU Information", "系统信息": "System Information", "设置WebUI端口, 0为自动": "Set WebUI Port, 0 is automatic", "选择语言": "Select language", "选择MSST模型下载链接": "Select MSST model download link", "选择WebUI主题": "Select WebUI theme", "对本地局域网开放WebUI: 开启后, 同一局域网内的设备可通过'本机IP:端口'的方式访问WebUI。": "Open WebUI to local LAN: After enabling, devices within the same LAN can access WebUI through the 'local IP: port'.", "开启公共链接: 开启后, 他人可通过公共链接访问WebUI。链接有效时长为72小时。": "Open public link: Others can access WebUI through the link. Link expires in 72 hours.", "自动清理缓存: 开启后, 每次启动WebUI时会自动清理缓存。": "Automatically clear cache: After enabling, the cache will be automatically cleared every time the WebUI is launched.", "全局调试模式: 向开发者反馈问题时请开启。(该选项支持热切换)": "Global debug mode: Enable when reporting issues to the developer. (This option supports hot switching)", "选择UVR模型目录": "Select UVR Model Directory", "检查更新": "Check for Updates", ", 请点击检查更新按钮": ", please click the Check for Updates button", "前往Github瞅一眼": "Go to Github Releases", "重置WebUI路径记录": "Reset WebUI Path Records", "重置WebUI设置": "Reset WebUI settings", "### 选择UVR模型目录": "### Select UVR Model Directory", "如果你的电脑中有安装UVR5, 你不必重新下载一遍UVR5模型, 只需在下方“选择UVR模型目录”中选择你的UVR5模型目录, 定位到models/VR_Models文件夹。<br>例如: E:/Program Files/Ultimate Vocal Remover/models/VR_Models 点击保存设置或重置设置后, 需要重启WebUI以更新。": "If you have UVR5 installed on your computer, you do not need to re-download the UVR5 model. Just select your UVR5 model directory in the “Select UVR Model Directory” section below and locate it to the models/VR_Models folder.<br>For example: E:/Program Files/Ultimate Vocal Remover/models/VR_Models. After clicking Save Settings or Reset Settings, you need to restart WebUI to update.", "### 检查更新": "### Check for Updates", "从Github检查更新, 需要一定的网络要求。点击检查更新按钮后, 会自动检查是否有最新版本。你可以前往此整合包的下载链接或访问Github仓库下载最新版本。": "Check for updates from Github, which requires certain network conditions. After clicking the Check for Updates button, it will automatically check for the latest version. You can go to the download link of this integration package or visit the Github repository to download the latest version.", "### 重置WebUI路径记录": "### Reset WebUI Path Records", "将所有输入输出目录重置为默认路径, 预设/模型/配置文件以及上面的设置等**不会重置**, 无需担心。重置WebUI设置后, 需要重启WebUI。": "Reset all input and output directories to default paths. Presets/models/config files and the settings above **will not be reset**, so no need to worry. After resetting WebUI settings, you need to restart WebUI.", "### 重置WebUI设置": "### Reset WebUI settings", "仅重置WebUI设置, 例如UVR模型路径, WebUI端口等。重置WebUI设置后, 需要重启WebUI。": "Only reset WebUI settings, such as UVR model path, WebUI port, etc. After resetting the WebUI settings, it is necessary to restart the WebUI.", "### 重启WebUI": "### Restart WebUI", "点击 “重启WebUI” 按钮后, 会短暂性的失去连接, 随后会自动开启一个新网页。": "After clicking the “Restart WebUI” button, you will temporarily lose connection, and a new page will automatically open.", "音频输出设置": "Audio output settings", "此页面支持用户自定义修改MSST/VR推理后输出音频的质量。输出音频的**采样率, 声道数与模型支持的参数有关, 无法更改**。<br>修改完成后点击保存设置即可生效。": "This page allows users to customize the quality of audio output after MSST/VR inference. The output audio's **sample rate and channel count depend on the parameters supported by the model and cannot be changed**.<br>Click 'Save Settings' after modification to take effect.", "输出wav位深度": "Output WAV bit depth", "输出flac位深度": "Output FLAC bit depth", "输出mp3比特率(bps)": "Output MP3 bitrate (bps)", "保存设置": "Save settings", "模型改名": "Model renaming", "此页面支持用户自定义修改模型名字, 以便记忆和使用。修改完成后, 需要重启WebUI以刷新模型列表。<br>【注意】此操作不可逆 (无法恢复至默认命名), 请谨慎命名。输入新模型名字时, 需保留后缀!": "This page supports users to customize and modify model names for easy memory and use. After the modification is completed, it is necessary to restart the WebUI to refresh the model list.<br>[Note] This operation is irreversible (cannot be restored to the default name), please use caution when naming. When entering a new model name, the suffix must be retained!", "新模型名": "New model name", "请输入新模型名字, 需保留后缀!": "Please enter the name of the new model, keeping the suffix!", "确认修改": "Confirm modification", "立体声": "Stereo", "音频格式转换": "Audio Format Conversion", "上传一个或多个音频文件并将其转换为指定格式。<br>支持的格式包括 .mp3, .flac, .wav, .ogg, .m4a, .wma, .aac...等等。<br>**不支持**网易云音乐/QQ音乐等加密格式, 如.ncm, .qmc等。": "Upload one or more audio files and convert them to the specified format. <br>Supported formats include .mp3, .flac, .wav, .ogg, .m4a, .wma, .aac...etc. <br>**Not supported** encrypted formats.", "选择或输入音频输出格式": "Select or input audio output format", "选择音频输出目录": "Select audio output directory", "输出音频采样率(Hz)": "Output audio sample rate (Hz)", "输出音频声道数": "Number of output audio channels", "输出ogg比特率(bps)": "Output OGG bitrate (bps)", "转换音频": "Convert audio", "合并音频": "Merge audio", "点击合并音频按钮后, 将自动把输入文件夹中的所有音频文件合并为一整个音频文件<br>合并后的音频会保存至输出目录中, 文件名为merged_audio_<文件夹名字>.wav": "After clicking the 'Merge Audio' button, all audio files in the input folder will be automatically merged into a single audio file.<br>The merged audio will be saved in the output directory with the filename merged_audio_<folder_name>.wav.", "计算SDR": "Calculate SDR", "上传两个**wav音频文件**并计算它们的[SDR](https://www.aicrowd.com/challenges/music-demixing-challenge-ismir-2021#evaluation-metric)。<br>SDR是一个用于评估模型质量的数值。数值越大, 模型算法结果越好。": "Upload two **wav audio files** and calculate their [SDR](https://www.aicrowd.com/challenges/music-demixing-challenge-ismir-2021#evaluation-metric). <br>SDR is a metric used to evaluate model quality. The larger the value, the better the model algorithm's result.", "参考音频": "Reference audio", "待估音频": "Audio to be evaluated", "歌声转MIDI": "Vocal to MIDI", "歌声转MIDI功能使用开源项目[SOME](https://github.com/openvpi/SOME/), 可以将分离得到的**干净的歌声**转换成.mid文件。<br>【必须】若想要使用此功能, 请先下载权重文件[model_steps_64000_simplified.ckpt](https://hf-mirror.com/Sucial/MSST-WebUI/resolve/main/SOME_weights/model_steps_64000_simplified.ckpt)并将其放置在程序目录下的`tools/SOME_weights`文件夹内。文件命名不可随意更改!": "The vocal-to-MIDI function uses the open-source project [SOME](https://github.com/openvpi/SOME/), allowing the conversion of **clean vocals** into .mid files.<br>**[Required]** To use this function, please download the weight file [model_steps_64000_simplified.ckpt](https://huggingface.co/Sucial/MSST-WebUI/resolve/main/SOME_weights/model_steps_64000_simplified.ckpt) and place it in the `tools/SOME_weights` folder of the program directory. The filename must not be changed arbitrarily!", "如果不知道如何测量歌曲BPM, 可以尝试这两个在线测量工具: [bpmdetector](https://bpmdetector.kniffen.dev/) | [key-bpm-finder](https://vocalremover.org/zh/key-bpm-finder), 测量时建议上传原曲或伴奏, 若干声可能导致测量结果不准确。": "If you don't know how to measure song BPM, you can try these two online measurement tools: [bpmdetector](https://bpmdetector.kniffen.dev/) | [key-bpm-finder](https://vocalremover.org/zh/key-bpm-finder). When measuring, it is recommended to upload the original song or accompaniment, as several sounds may cause inaccurate measurement results.", "上传音频": "Upload audio", "输入音频BPM": "Input audio BPM", "开始转换": "Start conversion", "1. 音频BPM (每分钟节拍数) 可以通过MixMeister BPM Analyzer等软件测量获取。<br>2. 为保证MIDI提取质量, 音频文件请采用干净清晰无混响底噪人声。<br>3. 输出MIDI不带歌词信息, 需要用户自行添加歌词。<br>4. 实际使用体验中部分音符会出现断开的现象, 需自行修正。SOME的模型主要面向DiffSinger唱法模型自动标注, 比正常用户在创作中需要的MIDI更加精细, 因而可能导致模型倾向于对音符进行切分。<br>5. 提取的MIDI没有量化/没有对齐节拍/不适配BPM, 需自行到各编辑器中手动调整。": "1. Audio BPM (beats per minute) can be measured using software like MixMeister BPM Analyzer. <br>2. To ensure MIDI extraction quality, please use clean, clear, and noise-free vocal audio without reverb. <br>3. The output MIDI does not contain lyrics information; you need to add the lyrics manually. <br>4. In actual use, some notes may appear disconnected, requiring manual correction. The SOME model is mainly designed for auto-labeling with DiffSinger vocal models, which may lead to finer segmentation of notes than typically needed in user creations. <br>5. The extracted MIDI is not quantized/aligned with the beat/does not match the BPM, requiring manual adjustment in editors.", "此页面提供数据集制作教程, 训练参数选择, 以及一键训练。有关配置文件的修改和数据集文件夹的详细说明请参考MSST原项目: [https://github.com/ZFTurbo/Music-Source-Separation-Training](https://github.com/ZFTurbo/Music-Source-Separation-Training)<br>在开始下方的模型训练之前, 请先进行训练数据的制作。<br>说明: 数据集类型即训练集制作Step 1中你选择的类型, 1: Type1; 2: Type2; 3: Type3; 4: Type4, 必须与你的数据集类型相匹配。": "This page provides tutorials on dataset creation, training parameter selection, and one click training. For detailed instructions on modifying configuration files and dataset folders, please refer to the MSST original project: [https://github.com/ZFTurbo/Music-Source-Separation-Training](https://github.com/ZFTurbo/Music-Source-Separation-Training)<br>Before starting the model training below, please create the training data first.<br>Explanation: The dataset type refers to the type you selected in Step 1 of the datasets production, 1: Type1; 2: Type2; 3: Type3; 4: Type 4, which must match your dataset type.", "训练": "Training", "选择训练模型类型": "Select Training Model Type", "配置文件路径": "Configuration File Path", "请输入配置文件路径或选择配置文件": "Please enter or select the configuration file path", "选择配置文件": "Select Configuration File", "数据集类型": "Dataset Type", "数据集路径": "Dataset Path", "请输入或选择数据集文件夹": "Please enter or select the dataset folder", "选择数据集文件夹": "Select Dataset Folder", "验证集路径": "Validation Set Path", "请输入或选择验证集文件夹": "Please enter or select the validation set folder", "选择验证集文件夹": "Select Validation Set Folder", "模型保存路径": "Model Save Path", "请输入或选择模型保存文件夹": "Please enter or select the model save folder", "选择初始模型, 若无初始模型, 留空或选择None即可": "Select an initial model. If no initial model is available, leave blank or choose 'None'.", "刷新初始模型列表": "Refresh Initial Model List", "训练参数设置": "Training parameter settings", "num_workers: 数据集读取线程数, 0为自动": "num_workers: Number of dataset reading threads, 0 is automatic", "随机数种子, 0为随机": "Random seed, 0 is random", "是否将加载的数据放置在固定内存中, 默认为否": "Place loaded data in pinned memory, default is no", "是否使用加速训练, 对于多显卡用户会加快训练": "Use accelerated training, which will speed up training for multi-gpu users", "是否在训练前验证模型, 默认为否": "Validate the model before training, default is no", "是否使用MultiSTFT Loss, 默认为否": "Use MultiSTFT Loss, default is no", "是否使用MSE loss, 默认为否": "Use MSE loss, default is no", "是否使用L1 loss, 默认为否": "Use L1 loss, default is no", "选择输出的评估指标": "Select evaluation metrics for output", "选择调度器使用的评估指标": "Select evaluation metrics used by the scheduler", "保存上述训练配置": "Save the above training configuration", "开始训练": "Start Training", "点击开始训练后, 请到终端查看训练进度或报错, 下方不会输出报错信息, 想要停止训练可以直接关闭终端。在训练过程中, 你也可以关闭网页, 仅**保留终端**。": "After clicking start training, please check the terminal for training progress or errors, errors will not be output below, you can stop training by closing the terminal directly. During training, you can also close the webpage and **keep the terminal** only.", "验证": "Validation", "此页面用于手动验证模型效果, 测试验证集, 输出SDR测试信息。输出的信息会存放在输出文件夹的results.txt中。<br>下方参数将自动加载训练页面的参数, 在训练页面点击保存训练参数后, 重启WebUI即可自动加载。当然你也可以手动输入参数。<br>": "This page is for manually validating the model performance, testing the validation set, and outputting SDR test information. The output information will be stored in the results.txt file in the output folder.<br>The parameters below will be automatically loaded from the training page, after saving the training parameters on the training page, restart WebUI to automatically load them. Of course, you can also enter the parameters manually.<br>", "模型路径": "Model Path", "请输入或选择模型文件": "Please enter or select the model file", "选择模型文件": "Select Model File", "验证参数设置": "Validation parameter settings", "选择验证集音频格式": "Select Validation Set Audio Format", "验证集读取线程数, 0为自动": "Validation set reading thread count, 0 is automatic", "开始验证": "Start Validation", "训练集制作指南": "Datasets Creation Guide", "Step 1: 数据集制作": "Step 1: Dataset Creation", "请**任选下面四种类型之一**制作数据集文件夹, 并按照给出的目录层级放置你的训练数据。完成后, 记录你的数据集**文件夹路径**以及你选择的**数据集类型**, 以便后续使用。": "Please **choose one of the following four types** to create the dataset folder and place your training data according to the given directory structure. After completing this, record your dataset **folder path** and the **dataset type** you selected for future use.", "不同的文件夹。每个文件夹包含所需的所有stems, 格式为stem_name.wav。与MUSDBHQ18数据集相同。在最新的代码版本中, 可以使用flac替代wav。<br>例如: ": "Different folders. Each folder contains all the required stems in the format stem_name.wav. Same as the MUSDBHQ18 dataset. In the latest code version, flac can be used instead of wav.<br>For example: ", "每个文件夹是stem_name。文件夹中包含仅由所需stem组成的wav文件。<br>例如: ": "Each folder is stem_name. The folder contains wav files composed only of the required stem.<br>For example: ", "可以提供以下结构的CSV文件 (或CSV文件列表) <br>例如: ": "A CSV file (or a list of CSV files) with the following structure can be provided.<br>For example: ", "与类型1相同, 但在训练过程中所有乐器都将来自歌曲的相同位置。<br>例如: ": "Same as Type 1, but all instruments during training will come from the same location in the song.<br>For example: ", "Step 2: 验证集制作": "Step 2: Validation Set Creation", "验证集制作。验证数据集**必须**与上面数据集制作的Type 1(MUSDB)数据集**结构相同** (**无论你使用哪种类型的数据集进行训练**) , 此外每个文件夹还必须包含每首歌的mixture.wav, mixture.wav是所有stem的总和<br>例如: ": "Validation set creation. The validation dataset **must** have the **same structure** as the Type 1 (MUSDB) dataset created above (**regardless of the type of dataset used for training**). Additionally, each folder must also contain a mixture.wav for each song, which is the sum of all stems.<br>For example: ", "Step 3: 选择并修改修改配置文件": "Step 3: Select and Modify Configuration File", "请先明确你想要训练的模型类型, 然后选择对应的配置文件进行修改。<br>目前有以下几种模型类型: ": "First, determine the type of model you want to train, then select the corresponding configuration file to modify.<br>Currently, there are the following model types: ", "<br>确定好模型类型后, 你可以前往整合包根目录中的configs_backup文件夹下找到对应的配置文件模板。复制一份模板, 然后根据你的需求进行修改。修改完成后记下你的配置文件路径, 以便后续使用。<br>特别说明: config_musdb18_xxx.yaml是针对MUSDB18数据集的配置文件。<br>": "<br>After determining the model type, you can find the corresponding configuration file template in the configs_backup folder in the root directory of the integration package. Copy a template, then modify it according to your needs. After modifying, record your configuration file path for future use.<br>Note: config_musdb18_xxx.yaml is the configuration file for the MUSDB18 dataset.<br>", "打开配置文件模板文件夹": "Open Configuration File Templates Folder", "你可以使用下表根据你的GPU选择用于训练的BS_Roformer模型的batch_size参数。表中提供的批量大小值适用于单个GPU。如果你有多个GPU, 则需要将该值乘以GPU的数量。": "You can use the table below to select the batch_size parameter for the BS_Roformer model used for training based on your GPU. The batch size values provided in the table are for a single GPU. If you have multiple GPUs, you need to multiply this value by the number of GPUs.", "Step 4: 数据增强": "Step 4: Data Augmentation", "数据增强可以动态更改stem, 通过从旧样本创建新样本来增加数据集的大小。现在, 数据增强的控制在配置文件中进行。下面是一个包含所有可用数据增强的完整配置示例。你可以将其复制到你的配置文件中以使用数据增强。<br>注意:<br>1. 要完全禁用所有数据增强, 可以从配置文件中删除augmentations部分或将enable设置为false。<br>2. 如果要禁用某些数据增强, 只需将其设置为0。<br>3. all部分中的数据增强应用于所有stem。<br>4. vocals/bass等部分中的数据增强仅应用于相应的stem。你可以为training.instruments中给出的所有stem创建这样的部分。": "Data augmentation can dynamically change stems, increasing the size of the dataset by creating new samples from old ones. Currently, data augmentation control is done in the configuration file. Below is a complete configuration example with all available data augmentations. You can copy it into your configuration file to use data augmentation.<br>Note:<br>1. To completely disable all data augmentations, you can either remove the augmentations section from the configuration file or set enable to false.<br>2. To disable certain data augmentations, simply set them to 0.<br>3. Augmentations in the all section apply to all stems.<br>4. Augmentations in sections like vocals/bass apply only to the corresponding stems. You can create such sections for all stems given in training.instruments.", "说明: 本整合包仅融合了UVR的VR Architecture模型, MDX23C和HtDemucs类模型可以直接使用前面的MSST音频分离。<br>UVR分离使用项目: [https://github.com/nomadkaraoke/python-audio-separator](https://github.com/nomadkaraoke/python-audio-separator) 并进行了优化。": "Notes: This integration package only integrates the VR Architecture model of UVR. MDX23C and HtDemucs class models can directly use the MSST separation mentioned earlier.<br>UVR separation usage project: [https://github.com/nomadkaraoke/python-audio-separator](https://github.com/nomadkaraoke/python-audio-separator), and optimized it.", "Window Size: 窗口大小, 用于平衡速度和质量, 默认为512": "Window Size: Used to balance speed and quality, default is 512.", "Aggression: 主干提取强度, 范围-100-100, 人声请选5": "Aggression: Stem extraction intensity, range -100-100, choose 5 for vocals", "[点击展开] 以下是一些高级设置, 一般保持默认即可": "[Click to expand] Below are some advanced settings, generally keep the defaults.", "批次大小, 减小此值可以降低显存占用": "Batch size, reducing this value can decrease VRAM usage.", "后处理特征阈值, 取值为0.1-0.3, 默认0.2": "Post-processing feature threshold, range is 0.1-0.3, default is 0.2.", "次级输出使用频谱而非波形进行反转, 可能会提高质量, 但速度稍慢": "Use spectrum instead of waveform for inversion in secondary output, may improve quality but slower.", "启用“测试时增强”, 可能会提高质量, 但速度稍慢": "Enable 'Test-Time Augmentation', may improve quality but slower.", "将输出音频缺失的频率范围镜像输出, 作用不大": "Mirror output of missing frequency range in audio, minimal effect.", "识别人声输出中残留的人工痕迹, 可改善某些歌曲的分离效果": "Detect residual artifacts in vocal output, may improve separation for some songs.", "正在启动WebUI, 请稍等...": "Starting WebUI, please wait...", "若启动失败, 请尝试以管理员身份运行此程序": "If startup fails, try running this program as administrator", "WebUI运行过程中请勿关闭此窗口!": "Do not close this window while WebUI is running!", "检测到CUDA, 设备信息: ": "CUDA detected, device info: ", "使用MPS": "Using MPS", "检测到MPS, 使用MPS": "MPS detected, using MPS", "无可用的加速设备, 使用CPU": "No available accelerator, using CPU", "\\033[33m未检测到可用的加速设备, 使用CPU\\033[0m": "\\033[33mNo available accelerator detected, using CPU\\033[0m", "\\033[33m如果你使用的是NVIDIA显卡, 请更新显卡驱动至最新版后重试\\033[0m": "\\033[33mIf you are using an NVIDIA GPU, please update your GPU driver to the latest version and try again\\033[0m", "模型下载失败, 请重试!": "Model download failed, please try again!", "作者: [Github@KitsuneX07](https://github.com/KitsuneX07) | [Github@SUC-DriverOld](https://github.com/SUC-DriverOld), [点击前往教程文档](https://r1kc63iz15l.feishu.cn/wiki/JSp3wk7zuinvIXkIqSUcCXY1nKc)": "Authors: <AUTHORS>", "**请将需要处理的音频放置到input文件夹内, 处理完成后的音频将会保存到results文件夹内! 云端输入输出目录不可更改!**": "**Please place the audio to be processed in the input folder, and the processed audio will be saved in the results folder! The cloud input and output directories cannot be changed!**", "文件管理": "File Management", "文件管理页面是云端WebUI特有的页面, 用于上传, 下载, 删除文件。<br>1. 上传文件: 将文件上传到input文件夹内。可以勾选是否自动解压zip文件<br>2. 下载文件: 以zip格式打包results文件夹内的文件, 输出至WebUI以供下载。注意: 打包不会删除results文件夹, 若打包后不再需要分离结果, 请点击按钮手动删除。<br>3. 删除文件: 删除input和results文件夹内的文件。": "The File Management page is a cloud-based WebUI-specific page used for uploading, downloading, and deleting files.<br>1. Upload Files: Upload files to the input folder. You can choose whether to automatically unzip zip files.<br>2. Download Files: Package files in the results folder into a zip file and output it to the WebUI for download. Note: Packaging will not delete the results folder. If the separated results are no longer needed after packaging, please manually delete them by clicking the button.<br>3. Delete Files: Delete files in both the input and results folders.", "删除input文件夹内所有文件": "Delete All Files in the Input Folder", "删除results文件夹内所有文件": "Delete All Files in the Results Folder", "刷新input和results文件列表": "Refresh the File Lists in Input and Results Folders", "打包results文件夹内所有文件": "Package All Files in the Results Folder", "上传一个或多个文件至input文件夹": "Upload One or More Files to the Input Folder", "自动解压zip文件(仅支持zip, 压缩包内文件名若含有非ASCII字符, 解压后文件名可能为乱码)": "Automatically Unzip Zip Files (Only Supports Zip; If the Zip contains non-ASCII characters in filenames, the extracted filenames may be corrupted)", "上传文件": "Upload Files", "input和results文件列表": "File Lists in Input and Results Folders", "请先点击刷新按钮": "Please Click the Refresh <PERSON><PERSON> First", "下载results文件夹内所有文件": "Download All Files in the Results Folder", "Window Size: 窗口大小, 用于平衡速度和质量": "Window Size: For balancing speed and quality", "初始模型: 继续训练或微调模型训练时, 请选择初始模型, 否则将从头开始训练! ": "Initial Model: When continuing or fine-tuning the model training, please select the initial model, otherwise it will start training from scratch!", "点击合并音频按钮后, 将自动把输入文件夹中的所有音频文件合并为一整个音频文件<br>目前支持的格式包括 .mp3, .flac, .wav, .ogg, m4a 这五种<br>合并后的音频会保存至输出目录中, 文件名为merged_audio_<文件夹名字>.wav": "After clicking the merge audio button, all audio files in the input folder will automatically be merged into a single audio file.<br>Currently supported formats include .mp3, .flac, .wav, .ogg, and .m4a.<br>The merged audio will be saved in the output directory with the filename merged_audio_<folder_name>.wav"}