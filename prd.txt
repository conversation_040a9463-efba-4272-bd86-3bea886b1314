<context>
# Overview  
木偶AI翻唱应用重构项目 - 将现有的PyQt5单文件GUI应用程序重构为模块化的PySide6应用，并将资源密集型的音频处理功能分离为独立的FastAPI服务。该项目旨在解决当前应用在性能、可维护性和硬件兼容性方面的问题，同时为未来集成到更大的PySide6项目中做准备。

当前应用主要功能包括：音频文件上传与管理、音频分离（人声/伴奏）、AI音色转换、混响效果处理、波形显示与播放、成品歌曲管理等。核心痛点是msst音频分离和main_reflow音色转换极度消耗GPU资源，需要CUDA加速，但很多用户缺乏英伟达显卡支持。

# Core Features  
## 1. 模块化GUI界面 (PySide6)
- **功能**: 将单文件GUI拆分为模块化的PySide6应用
- **重要性**: 提高代码可维护性，便于未来集成到其他PySide6项目
- **实现**: 采用MVC架构，分离界面、业务逻辑和数据层

## 2. 左右分栏+手风琴式界面设计
- **左侧栏设计**:
  - **Logo区域**: 木偶AI Logo居中显示在左侧栏顶部
  - **歌曲管理手风琴**: 可折叠区域，包含成品歌曲的预览、分轨播放、管理和组织功能
  - **API管理手风琴**: 可折叠区域，包含本地/云端API服务配置、控制台输出、启动/停止控制
  - **上传处理区域**: 音频文件拖拽上传、处理模式选择、一键翻唱按钮
- **右侧栏设计**:
  - **模型与音高配置**: 音色模型选择、配置文件选择、人声/伴奏音高调节
  - **混响与和声配置**: 混响开关、和声设置、房间大小等参数调节
  - **高级参数配置**: 声码器、F0提取器、共振峰偏移等专业参数设置

## 3. 云端/本地混合处理架构
- **功能**: 将CUDA密集型处理（msst分离、main_reflow转换）独立为FastAPI服务
- **重要性**: 解决硬件兼容性问题，支持云端处理
- **实现**:
  - 本地API：用户可选择随GUI启动本地API服务
  - 云端API：支持配置远程API端点
  - 自动回退：本地失败时自动切换到云端
  - 模型同步：API服务扫描models目录，向客户端提供可用模型和配置文件列表

## 4. 优化的音频组件
- **波形显示**: 性能优化的波形渲染，支持大文件快速加载
- **播放器组件**: 集成成熟的Python音频播放解决方案
- **混响效果**: 集成Spotify开源Python库，提升音效质量

## 5. 实时进度追踪
- **功能**: 显示音频分离和音色转换的实时进度
- **重要性**: 改善用户体验，避免长时间等待的焦虑
- **实现**: 通过WebSocket或HTTP轮询获取API处理进度

# User Experience  
## 用户画像
- **主要用户**: AI翻唱爱好者、音乐制作人、内容创作者
- **技术水平**: 中等，熟悉基本的音频处理概念
- **硬件环境**: 混合（部分有CUDA显卡，部分仅有CPU）

## 关键用户流程
1. **音频上传流程**: 在左侧上传区域拖拽文件 → 自动格式转换 → 波形预览 → 选择处理模式和输出格式
2. **参数配置流程**: 在右侧面板选择音色模型和配置文件 → 调节人声/伴奏音高滑块 → 配置混响参数 → 设置高级参数
3. **处理执行流程**: 点击"一键翻唱"按钮 → 实时进度显示 → 处理完成通知 → 结果预览
4. **API管理流程**: 展开API管理手风琴 → 配置API URL和Python环境 → 查看控制台输出 → 启动/停止API服务
5. **歌曲管理流程**: 展开歌曲管理手风琴 → 浏览成品歌曲列表 → 预览和播放 → 管理和导出

## UI/UX考虑
- **深色主题设计**: 采用深色配色方案（背景色#111827，卡片色#1F2937），提供舒适的视觉体验
- **响应式布局**: 左右分栏设计，大屏幕时为3:2比例，小屏幕时自动堆叠为单列布局
- **组件自适应**: 所有组件都需要跟随窗口大小自适应调整大小，以适应不同的屏幕分辨率及用户手动调整窗口大小
- **手风琴交互**: 左侧功能区域采用可折叠的手风琴设计，点击展开时填充整个左侧区域
- **蓝色强调色**: 使用#3B82F6作为主要强调色，应用于按钮、滑块、边框等交互元素
- **Material Icons**: 集成Google Material Icons图标库，提供一致的视觉语言
- **进度反馈**: 所有长时间操作都有明确的进度指示和控制台输出显示
- **错误处理**: 友好的错误提示和恢复建议
- **快捷操作**: 常用功能的键盘快捷键支持
</context>
<PRD>
# Technical Architecture  
## 系统组件架构
### 1. GUI客户端 (PySide6)
```
src/
├── main.py                 # 应用入口
├── ui/                     # 界面模块
│   ├── main_window.py     # 主窗口（左右分栏布局）
│   ├── panels/            # 面板组件
│   │   ├── left_panel.py  # 左侧面板（Logo+手风琴区域+上传区域）
│   │   └── right_panel.py # 右侧面板（参数配置区域）
│   ├── accordions/        # 手风琴组件
│   │   ├── song_manager_accordion.py  # 歌曲管理手风琴
│   │   └── api_manager_accordion.py   # API管理手风琴
│   └── components/        # 可复用组件
│       ├── waveform_widget.py
│       ├── audio_player.py
│       ├── progress_widget.py
│       ├── slider_widget.py      # 自定义滑块组件
│       ├── upload_widget.py      # 拖拽上传组件
│       └── console_widget.py     # 控制台输出组件
├── core/                  # 核心业务逻辑
│   ├── audio_manager.py   # 音频文件管理
│   ├── api_client.py      # API通信客户端
│   └── config_manager.py  # 配置管理
└── utils/                 # 工具函数
    ├── audio_utils.py
    └── file_utils.py
```

### 2. FastAPI处理服务
```
api_server.py              # 单文件API服务
├── 音频分离端点 (/separate)
├── 音色转换端点 (/convert)
├── 模型管理端点 (/models)
│   ├── GET /models/list   # 获取可用模型列表
│   ├── GET /models/configs # 获取配置文件列表
│   └── GET /models/scan   # 重新扫描models目录
├── 进度查询端点 (/progress/{task_id})
├── 健康检查端点 (/health)
└── 文件上传/下载端点
```

### 3. 数据模型
- **AudioFile**: 音频文件元数据模型
- **ProcessingTask**: 处理任务状态模型
- **APIConfig**: API配置模型
- **UserSettings**: 用户设置模型
- **ModelInfo**: 模型文件信息模型（包含模型路径、配置文件、兼容性等）
- **FinishedSong**: 成品歌曲模型（包含分轨文件路径、元数据等）

## APIs和集成
### 内部API设计
- **RESTful API**: 标准HTTP接口，支持JSON数据交换
- **异步处理**: 长时间任务采用异步模式，返回任务ID
- **进度追踪**: WebSocket连接实时推送处理进度
- **文件传输**: 支持大文件分块上传/下载
- **模型管理**: 自动扫描models目录，提供模型和配置文件的动态列表
- **配置同步**: 确保客户端和API服务端的模型配置保持一致

### 外部依赖
- **PySide6**: GUI框架
- **FastAPI**: API服务框架
- **librosa**: 音频处理库
- **torch**: 深度学习框架
- **ffmpeg**: 音频格式转换

## 基础设施要求
### 本地环境
- **Python 3.8+**: 基础运行环境
- **可选CUDA**: GPU加速支持
- **FFmpeg**: 音频处理依赖

### 云端环境  
- **CUDA支持**: 英伟达GPU环境
- **负载均衡**: 支持多实例部署

# Development Roadmap  
## Phase 1: 基础架构搭建 (MVP)
### 1.1 项目结构初始化
- 创建src目录结构
- 设置PySide6开发环境
- 配置项目依赖管理

### 1.2 核心GUI框架
- 实现主窗口基础框架（左右分栏布局）
- 创建左侧面板结构（Logo+手风琴+上传区域）
- 创建右侧面板结构（参数配置区域）
- 实现手风琴折叠/展开交互逻辑
- 实现基础的配置管理系统

### 1.3 音频管理模块
- 拖拽上传组件（蓝色虚线边框，上传图标，提示文字）
- 处理模式选择下拉框（完整模式/快速模式）
- 输出格式选择下拉框（WAV/MP3）
- 一键翻唱按钮（蓝色主按钮，带火箭图标）
- 处理状态显示（空闲/处理中等状态）
- 基础的文件格式转换
- 简单的音频信息显示

### 1.4 FastAPI服务基础
- 创建单文件API服务
- 实现基础的音频分离接口
- 添加健康检查和错误处理
- 实现models目录扫描功能，提供模型和配置文件列表API

## Phase 2: 核心功能实现
### 2.1 音频处理集成
- 集成msst音频分离功能
- 集成main_reflow音色转换
- 实现处理任务队列管理

### 2.2 进度追踪系统
- WebSocket进度推送
- GUI进度显示组件
- 任务状态管理

### 2.3 API管理手风琴界面
- 本地/云端API配置（API URL、Python环境路径）
- 随软件启动API和云端API的复选框选项
- 控制台输出显示区域（200px高度，等宽字体）
- 启动/停止API按钮（网格布局）
- 连接测试功能
- 自动回退机制
- 模型同步状态显示
- 远程模型列表获取和本地对比

## Phase 3: 界面优化与增强
### 3.1 音频组件升级
- 优化波形显示性能
- 集成成熟播放器组件
- 改进混响效果处理

### 3.2 用户体验优化
- 深色主题界面完善（卡片阴影、边框圆角、颜色过渡动画）
- 响应式布局优化（大屏幕3:2分栏，小屏幕单列堆叠）
- 手风琴交互动画优化（展开/收起过渡效果）
- 滑块组件美化（自定义滑块样式、实时数值显示）
- 快捷键支持
- 错误处理改进

### 3.3 歌曲管理手风琴功能完善
- 成品歌曲的预览和播放功能（在手风琴展开区域内）
- 分轨音频的独立播放控制（人声、伴奏、混响等）
- 歌曲元数据管理和标签系统
- 成品歌曲的导出和分享功能
- 播放列表和收藏功能
- 空状态显示（虚线边框、音频文件图标、"暂无成品歌曲"提示）

## Phase 4: 性能优化与扩展
### 4.1 性能优化
- 内存使用优化
- 多线程处理改进
- 缓存机制实现

### 4.2 功能扩展
- 批量处理支持
- 预设配置管理

# Logical Dependency Chain
## 开发顺序依赖
1. **基础架构** → **核心功能** → **界面优化** → **性能扩展**
2. **项目结构** → **GUI框架** → **音频管理** → **API服务**
3. **FastAPI基础** → **音频处理集成** → **进度追踪** → **API管理界面**

## 关键里程碑
1. **可运行的GUI框架**: 用户可以看到基础的左右分栏界面和手风琴结构
2. **音频上传预览**: 用户可以通过拖拽上传并预览音频文件
3. **手风琴交互**: 用户可以点击展开/收起歌曲管理和API管理区域
4. **模型管理功能**: API服务可以扫描并提供模型列表，客户端可以在右侧面板选择模型
5. **参数调节界面**: 用户可以通过滑块和下拉框调节各种音频处理参数
6. **基础处理功能**: 用户可以进行简单的音频处理
7. **成品歌曲管理**: 用户可以在歌曲管理手风琴中预览和播放已完成的翻唱作品
8. **完整处理流程**: 用户可以完成完整的翻唱制作流程

## 原子化功能模块
- 每个手风琴面板作为独立模块开发
- 左右面板可独立开发和测试
- 音频处理功能可独立测试
- API服务可独立部署和调试
- 配置管理系统支持热重载
- 滑块、上传等UI组件可复用

# Risks and Mitigations  
## 技术挑战
### 1. PySide6迁移复杂性
- **风险**: PyQt5到PySide6的API差异可能导致功能缺失
- **缓解**: 创建详细的迁移对照表，逐步迁移并测试

### 2. 音频处理性能
- **风险**: 大文件处理可能导致界面卡顿
- **缓解**: 采用多线程处理，实现异步音频操作

### 3. API服务稳定性
- **风险**: 网络连接问题可能影响云端处理
- **缓解**: 实现重试机制和本地回退方案

## MVP范围界定
### 核心MVP功能
- 基础的PySide6界面框架（包含左右分栏和手风琴结构）
- 深色主题UI实现（背景色、卡片样式、按钮样式）
- 音频文件拖拽上传和预览功能
- 手风琴展开/收起交互功能
- 滑块参数调节组件
- 本地API服务基础功能（包含模型扫描）
- 简单的音频分离处理
- 基础的成品歌曲预览功能（空状态显示）

### 非MVP功能
- 高级混响效果和音效处理
- 批量处理功能
- 复杂的预设管理
- 插件系统
- 高级的分轨播放控制
- 云端模型自动下载功能

## 资源约束
### 开发资源
- **时间约束**: 优先实现核心功能，界面美化放在后期
- **技术债务**: 现有代码重构需要谨慎处理，避免功能回退

### 硬件兼容性
- **CUDA依赖**: 确保CPU模式的备选方案
- **内存使用**: 大音频文件的内存管理优化

# Appendix  
## 技术规范
### UI设计规范
- **配色方案**:
  - 主背景色: #111827 (深灰蓝)
  - 卡片背景色: #1F2937 (中灰蓝)
  - 输入框背景色: #374151 (浅灰蓝)
  - 边框色: #4B5563 (边框灰)
  - 主文本色: #E5E7EB (浅灰白)
  - 次要文本色: #9CA3AF (中灰)
  - 强调色: #3B82F6 (蓝色)
  - 强调色悬停: #2563EB (深蓝)
- **组件规范**:
  - 卡片圆角: 12px
  - 输入框圆角: 8px
  - 按钮圆角: 8px
  - 滑块高度: 6px，滑块按钮: 16px圆形
  - 控制台高度: 200px，等宽字体
  - 手风琴过渡动画: 0.3s ease-out
- **响应式断点**:
  - 大屏幕(lg): 左侧1列，右侧2列
  - 小屏幕: 单列堆叠布局

### 代码规范
- Python PEP 8编码标准
- 类型注解要求
- 文档字符串规范

### 测试策略
- 单元测试覆盖核心业务逻辑
- 集成测试验证API通信
- UI测试确保界面功能正常

### 部署要求
- 支持Windows 10+环境

## 研究发现
### 现有代码分析
- 当前应用约4000行代码，功能相对完整
- 主要性能瓶颈在音频处理和波形渲染
- 用户界面布局合理，但缺乏模块化设计

### 技术选型依据
- **PySide6**: 官方支持，长期维护，适合商业项目
- **FastAPI**: 现代Python Web框架，支持异步处理
- **模块化架构**: 便于维护和扩展，支持团队协作开发
</PRD>
