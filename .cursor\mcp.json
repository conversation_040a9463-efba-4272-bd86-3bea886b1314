{"mcpServers": {"task-master-ai": {"command": "npx", "args": ["-y", "--package=task-master-ai", "task-master-ai"], "env": {"ANTHROPIC_API_KEY": "ANTHROPIC_API_KEY_HERE", "PERPLEXITY_API_KEY": "PERPLEXITY_API_KEY_HERE", "OPENAI_API_KEY": "OPENAI_API_KEY_HERE", "GOOGLE_API_KEY": "AIzaSyAHhuC8DfPbjWLPxVANzd_WTY0NKJb6TGk", "XAI_API_KEY": "XAI_API_KEY_HERE", "OPENROUTER_API_KEY": "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", "MISTRAL_API_KEY": "MISTRAL_API_KEY_HERE", "AZURE_OPENAI_API_KEY": "AZURE_OPENAI_API_KEY_HERE", "OLLAMA_API_KEY": "OLLAMA_API_KEY_HERE"}}}}