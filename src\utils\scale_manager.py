"""
缩放管理器 - 处理界面自适应缩放
"""

from PySide6.QtCore import QObject, Signal
from PySide6.QtWidgets import QApplication
from .constants import WINDOW_WIDTH, WINDOW_HEIGHT


class ScaleManager(QObject):
    """缩放管理器"""
    
    # 缩放因子改变信号
    scale_changed = Signal(float)
    
    def __init__(self):
        super().__init__()
        self._base_width = WINDOW_WIDTH
        self._base_height = WINDOW_HEIGHT
        self._current_scale = 1.0
        
    def calculate_scale_factor(self, current_width: int, current_height: int) -> float:
        """计算缩放因子"""
        # 基于宽度和高度的缩放因子
        width_scale = current_width / self._base_width
        height_scale = current_height / self._base_height
        
        # 使用较小的缩放因子以确保内容不会超出边界
        scale_factor = min(width_scale, height_scale)
        
        # 限制缩放范围
        scale_factor = max(0.5, min(scale_factor, 2.0))
        
        return scale_factor
    
    def update_scale(self, current_width: int, current_height: int):
        """更新缩放因子"""
        new_scale = self.calculate_scale_factor(current_width, current_height)
        
        if abs(new_scale - self._current_scale) > 0.01:  # 避免频繁更新
            self._current_scale = new_scale
            self.scale_changed.emit(new_scale)
    
    def get_current_scale(self) -> float:
        """获取当前缩放因子"""
        return self._current_scale
    
    def scale_value(self, value: int) -> int:
        """缩放数值"""
        return int(value * self._current_scale)
    
    def scale_font_size(self, base_size: int) -> int:
        """缩放字体大小"""
        scaled_size = int(base_size * self._current_scale)
        return max(8, scaled_size)  # 最小字体大小为8
    
    def get_scaled_stylesheet(self, base_stylesheet: str) -> str:
        """获取缩放后的样式表"""
        # 这里可以实现更复杂的样式表缩放逻辑
        # 目前返回原样式表，具体缩放在组件中处理
        return base_stylesheet


# 全局缩放管理器实例
scale_manager = ScaleManager()
