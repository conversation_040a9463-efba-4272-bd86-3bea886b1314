"""
自定义滑块组件
"""

from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QSlider
from PySide6.QtCore import Qt, Signal

from ...utils.constants import Colors, Styles


class SliderWidget(QWidget):
    """自定义滑块组件"""
    
    # 信号
    value_changed = Signal(int)
    
    def __init__(self, label: str, min_value: int, max_value: int, 
                 default_value: int, unit: str = ""):
        super().__init__()
        self.label_text = label
        self.min_value = min_value
        self.max_value = max_value
        self.unit = unit
        self.init_ui()
        self.slider.setValue(default_value)
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(Styles.MARGIN_SMALL)
        
        # 标签和数值显示
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(0, 0, 0, 0)
        
        self.label = QLabel(self.label_text)
        self.label.setStyleSheet(f"""
        QLabel {{
            color: {Colors.FOREGROUND};
            font-size: {Styles.FONT_SIZE_NORMAL}px;
            font-weight: bold;
        }}
        """)
        
        self.value_label = QLabel("0")
        self.value_label.setAlignment(Qt.AlignRight)
        self.value_label.setStyleSheet(f"""
        QLabel {{
            color: {Colors.ACCENT};
            font-size: {Styles.FONT_SIZE_NORMAL}px;
            font-weight: bold;
            min-width: 60px;
        }}
        """)
        
        header_layout.addWidget(self.label)
        header_layout.addStretch()
        header_layout.addWidget(self.value_label)
        
        layout.addLayout(header_layout)
        
        # 滑块
        self.slider = QSlider(Qt.Horizontal)
        self.slider.setMinimum(self.min_value)
        self.slider.setMaximum(self.max_value)
        self.slider.setStyleSheet(f"""
        QSlider::groove:horizontal {{
            background-color: {Colors.SECONDARY_BG};
            height: 6px;
            border-radius: 3px;
            border: 1px solid {Colors.BORDER};
        }}
        
        QSlider::handle:horizontal {{
            background-color: {Colors.ACCENT};
            border: 2px solid {Colors.ACCENT};
            width: 18px;
            height: 18px;
            border-radius: 9px;
            margin: -6px 0;
        }}
        
        QSlider::handle:horizontal:hover {{
            background-color: {Colors.ACCENT_HOVER};
            border-color: {Colors.ACCENT_HOVER};
        }}
        
        QSlider::handle:horizontal:pressed {{
            background-color: {Colors.ACCENT_PRESSED};
            border-color: {Colors.ACCENT_PRESSED};
        }}
        
        QSlider::sub-page:horizontal {{
            background-color: {Colors.ACCENT};
            border-radius: 3px;
        }}
        
        QSlider::add-page:horizontal {{
            background-color: {Colors.SECONDARY_BG};
            border-radius: 3px;
        }}
        """)
        
        # 连接信号
        self.slider.valueChanged.connect(self.on_value_changed)
        
        layout.addWidget(self.slider)
        
        # 范围标签
        range_layout = QHBoxLayout()
        range_layout.setContentsMargins(0, 0, 0, 0)
        
        min_label = QLabel(f"{self.min_value}{self.unit}")
        min_label.setStyleSheet(f"""
        QLabel {{
            color: {Colors.SECONDARY_TEXT};
            font-size: {Styles.FONT_SIZE_SMALL}px;
        }}
        """)
        
        max_label = QLabel(f"{self.max_value}{self.unit}")
        max_label.setAlignment(Qt.AlignRight)
        max_label.setStyleSheet(f"""
        QLabel {{
            color: {Colors.SECONDARY_TEXT};
            font-size: {Styles.FONT_SIZE_SMALL}px;
        }}
        """)
        
        range_layout.addWidget(min_label)
        range_layout.addStretch()
        range_layout.addWidget(max_label)
        
        layout.addLayout(range_layout)
        
    def on_value_changed(self, value):
        """值改变事件"""
        # 更新显示
        display_text = f"{value}{self.unit}"
        self.value_label.setText(display_text)
        
        # 发送信号
        self.value_changed.emit(value)
        
    def get_value(self):
        """获取当前值"""
        return self.slider.value()
        
    def set_value(self, value):
        """设置值"""
        self.slider.setValue(value)
        
    def set_range(self, min_value, max_value):
        """设置范围"""
        self.min_value = min_value
        self.max_value = max_value
        self.slider.setMinimum(min_value)
        self.slider.setMaximum(max_value)
        
    def set_enabled(self, enabled):
        """设置启用状态"""
        self.slider.setEnabled(enabled)
        self.label.setEnabled(enabled)
        self.value_label.setEnabled(enabled)
